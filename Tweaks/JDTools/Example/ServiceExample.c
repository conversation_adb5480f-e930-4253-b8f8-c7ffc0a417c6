#include "HttpService.h"
// #include "../../Core/LocalService/HTTP/HttpService.h"
#include <stdio.h>
#include <stdlib.h>     // 🔧 添加getprogname支持
#include <string.h>
#include <unistd.h>
#include <time.h>       // 🔧 添加time函数支持
#include <syslog.h>
#include <ctype.h>
#include "JSONUtils.h"


// 自定义请求处理器示例

static int custom_request_handler(const char* method, const char* path, const char* headers, const char* body, int client_socket) {
    

    // 🔧 签名验证通过，处理不同的路径
    if (strcmp(path, "/command") == 0) {
        // 🔧 控制命令接口 - 解析JSON数据


        http_service_send_json(client_socket, HTTP_STATUS_OK, "{\"success\": true}");
        return 1; // 已处理
    }
    else if (strcmp(path, "/api/status") == 0) {
        // 状态接口
        char json_response[512];
        snprintf(json_response, sizeof(json_response),
                "{"
                "\"status\":\"running\","
                "\"service\":\"JDHttpService\","
                "\"version\":\"1.0\","
                "\"process\":\"%s\","
                "\"timestamp\":%ld,"
                "\"signature_verified\":true"
                "}", getprogname(), time(NULL));

        http_service_send_json(client_socket, HTTP_STATUS_OK, json_response);
        return 1; // 已处理
    }
    else if (strcmp(path, "/api/data") == 0) {
        // 数据接口
        http_service_send_json(client_socket, HTTP_STATUS_OK,
                              "{"
                              "\"name\":\"JD HTTP Service\","
                              "\"description\":\"POST-only HTTP service with signature auth\","
                              "\"endpoints\":[\"/command\",\"/api/status\",\"/api/data\"],"
                              "\"auth\":\"Required Authorization header with value 'kkkkkkk'\","
                              "\"signature_verified\":true"
                              "}");
        return 1; // 已处理
    }

    // 🔧 返回0表示路径未匹配，HttpService会直接关闭连接
    return 0;
}

// 简单示例：启动HTTP服务
static void start_simple_http_service(void) {
    syslog(LOG_NOTICE, "=== 启动简单HTTP服务 ===");

    // 创建自定义配置
    http_service_config_t config = {
        .port = 8080,
        .bind_address = "0.0.0.0",  // 绑定到所有接口
        .request_handler = custom_request_handler,
        .service_name = "JDHttpService"
    };

    // 启动服务
    if (http_service_start(&config) == 0) {
        syslog(LOG_NOTICE, "✅ HTTP服务启动成功，端口: 8080");
        syslog(LOG_NOTICE, "📡 可访问POST接口:");
        syslog(LOG_NOTICE, "   - POST http://localhost:8080/api/status");
        syslog(LOG_NOTICE, "   - POST http://localhost:8080/api/data");
    } else {
        syslog(LOG_NOTICE, "❌ HTTP服务启动失败");
    }
}

__attribute__((constructor))
static void init_http_service_example(void) {
    // 初始化syslog
    openlog("HttpServiceExample", LOG_PID | LOG_CONS, LOG_USER);

    start_simple_http_service();

    syslog(LOG_NOTICE, "🚀 HttpService使用示例 - 进程: %s", getprogname());
    
    // // 只在特定进程中运行示例
    // if (strcmp(getprogname(), "JD4iPhone") == 0) {
    //     // 启动HTTP服务
    //     start_simple_http_service();
    // }
}

// 析构函数
__attribute__((destructor))
static void cleanup_http_service_example(void) {
    syslog(LOG_NOTICE, "🧹 HttpService示例清理");
    
    // 确保服务已停止
    http_service_stop();
    
    closelog();
}
