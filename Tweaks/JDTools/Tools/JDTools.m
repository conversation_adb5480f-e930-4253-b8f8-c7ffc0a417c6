#include <stdio.h>
#include <stdlib.h>     // 🔧 添加getprogname支持
#include <string.h>
#include <unistd.h>
#include <time.h>       // 🔧 添加time函数支持
#include "Log.h"
#include <ctype.h>
#include <dispatch/dispatch.h>
#include <objc/NSObjCRuntime.h>
#include <Foundation/Foundation.h>
#include <UIKit/UIKit.h>
#include "JSONUtils.h"
#include "Controller.h"
#include "HttpService.h"
#include "DataCrypto.h"
#include "SocketHTTP.h"
#include "CryptoRequest.h"
#include "StringCryptor_v2.h"
#include "UIViewHookManager.h"
#include "DataRecordManager.h"
#include "Toast.h"
#include "GlobalNetworkData.h"
// #include "JDTools.h"
#include <stdbool.h>
#include "./productInfo.h"

// 记录数据 - 全局变量定义（对应头文件中的extern声明）
DataRecordManager *s_networkMonitorInstance = nil;

// static void confirmOrderWithIdOrderType(NSNumber *orderId, NSNumber *orderType) {
//     id topVC = getTopViewController();
//     if (!topVC) {
//         return;
//     }

//     SEL mgrSel = sel_registerName("confirmManager");
//     id manager = nil;
//     if ([topVC respondsToSelector:mgrSel]) {
//         manager = ((id (*)(id, SEL))objc_msgSend)(topVC, mgrSel);
//     }
//     if (!manager) {
//         return;
//     }

//     NSDictionary *params = @{
//         @"orderId": orderId,
//         @"orderType": orderType
//     };

//     SEL confirmSel = sel_registerName("confirmOrderWithParams:");
//     if ([manager respondsToSelector:confirmSel]) {
//         ((void (*)(id, SEL, id))objc_msgSend)(manager, confirmSel, params);
//     } else {
//     }
// }

// 记录服务状态
static bool service_status = false;


// 判断字符串是否以某个前缀开头
bool starts_with(const char *str, const char *prefix) {
    if (!str || !prefix) return false;
    size_t len_prefix = strlen(prefix);
    size_t len_str = strlen(str);
    if (len_str < len_prefix) return false;
    return strncmp(str, prefix, len_prefix) == 0;
}

static int request_handler(const char* method, const char* path, const char* headers, const char* body, int client_socket) {

    // 🔧 签名验证通过，处理不同的路径
    if (strcmp(path, "/command") == 0) {
        // 🔧 控制命令接口 - 解析JSON数据
        cJSON* json = JSONUtils_ParseString(body);
        if (json == NULL) {
            return HTTP_STATUS_BAD_REQUEST;
        }
        LOG("Received command: %s", body);
        // const char* windowId = JSONUtils_GetStringValue(json, "windowId");
        // const char* buttonId = JSONUtils_GetStringValue(json, "buttonId");
        // const char* serialNumber = JSONUtils_GetStringValue(json, "serialNumber");
        // // 以上三个参数都为必填项
        // if (windowId == NULL || buttonId == NULL || serialNumber == NULL) {
        //     return HTTP_STATUS_BAD_REQUEST;
        // }

        const char* data = JSONUtils_GetStringValue(json, "data");
        const char* key = JSONUtils_GetStringValue(json, "key");
        const char* sign = JSONUtils_GetStringValue(json, "sign");
        if (data == NULL || key == NULL || sign == NULL) {
            cJSON_Delete(json);
            return HTTP_STATUS_BAD_REQUEST;
        }
        DecryptResult decryptResult = decrypt_request_data(data, key, sign);
        if (!decryptResult.status) {
            cJSON_Delete(json);
            return HTTP_STATUS_BAD_REQUEST;
        }
        cJSON* requestData = decryptResult.data;
        const char* temp_windowId = JSONUtils_GetStringValue(requestData, "windowId");
        const char* temp_buttonId = JSONUtils_GetStringValue(requestData, "buttonId");
        const char* temp_serialNumber = JSONUtils_GetStringValue(requestData, "serialNumber");
        if (temp_windowId == NULL || temp_buttonId == NULL || temp_serialNumber == NULL) {
            free_decrypt_result(&decryptResult);
            cJSON_Delete(json);
            return HTTP_STATUS_BAD_REQUEST;
        }
        // 创建字符串副本以确保异步执行时内存安全
        char* windowId = strdup(temp_windowId);
        char* buttonId = strdup(temp_buttonId);
        char* serialNumber = strdup(temp_serialNumber);

        // 检查内存分配是否成功
        if (!windowId || !buttonId || !serialNumber) {
            LOG("内存分配失败");
            if (windowId) free(windowId);
            if (buttonId) free(buttonId);
            if (serialNumber) free(serialNumber);
            free_decrypt_result(&decryptResult);
            cJSON_Delete(json);
            return HTTP_STATUS_INTERNAL_ERROR;
        }

        // 处理可选参数
        char* keyword = NULL;
        char* wareId = NULL;
        char* buyCount = NULL;

        if (strncmp(windowId, "search_", 7) == 0) {
            LOG("Received search command");
            const char* temp_keyword = JSONUtils_GetStringValue(requestData, "keyword");
            const char* temp_wareId = JSONUtils_GetStringValue(requestData, "wareId");
            const char* temp_buyCount = JSONUtils_GetStringValue(requestData, "buyCount");

            // 为可选参数创建副本（如果存在）
            if (temp_keyword) {
                keyword = strdup(temp_keyword);
                if (!keyword) {
                    LOG("keyword内存分配失败");
                    free(windowId);
                    free(buttonId);
                    free(serialNumber);
                    free_decrypt_result(&decryptResult);
                    cJSON_Delete(json);
                    return HTTP_STATUS_INTERNAL_ERROR;
                }
            }

            if (temp_wareId) {
                wareId = strdup(temp_wareId);
                if (!wareId) {
                    LOG("wareId内存分配失败");
                    free(windowId);
                    free(buttonId);
                    free(serialNumber);
                    if (keyword) free(keyword);
                    free_decrypt_result(&decryptResult);
                    cJSON_Delete(json);
                    return HTTP_STATUS_INTERNAL_ERROR;
                }
            }

            if (temp_buyCount) {
                buyCount = strdup(temp_buyCount);
                if (!buyCount) {
                    LOG("buyCount内存分配失败");
                    free(windowId);
                    free(buttonId);
                    free(serialNumber);
                    if (keyword) free(keyword);
                    if (wareId) free(wareId);
                    free_decrypt_result(&decryptResult);
                    cJSON_Delete(json);
                    return HTTP_STATUS_INTERNAL_ERROR;
                }
            }
        }

        // 在主线程执行
        dispatch_async(dispatch_get_main_queue(), ^{

            LOG("💚💚💚💚💚💚💚💚💚💚按钮信息: %s, %s, %s", windowId, buttonId, serialNumber);
            if (strncmp(windowId, "search_", 7) == 0) {
                // 以 "search_" 开头
                LOG("💚💚💚💚💚💚💚💚💚💚搜索信息: %s, %s, %s", keyword, wareId, buyCount);
                searchManager(buttonId, serialNumber, keyword, wareId, buyCount);
            } else {
                if (strcmp(windowId, "login") == 0) {
                    loginManager(buttonId, serialNumber);
                // } else if (strcmp(windowId, "search") == 0) {
                //     LOG("💚💚💚💚💚💚💚💚💚💚搜索信息: %s, %s, %s", keyword, wareId, buyCount);
                //     searchManager(buttonId, serialNumber, keyword, wareId, buyCount);
                } else if (strcmp(windowId, "address") == 0) {
                    addressManager(buttonId, serialNumber);
                } else if (strcmp(windowId, "ware") == 0) {
                    wareManager(buttonId, serialNumber);
                } else if (strcmp(windowId, "payment") == 0) {
                    paymentManager(buttonId, serialNumber);
                } else if (strcmp(windowId, "order") == 0) {
                    orderManager(buttonId, serialNumber);
                }
            }

            // 释放异步block中使用的字符串副本
            free(windowId);
            free(buttonId);
            free(serialNumber);
            if (keyword) free(keyword);
            if (wareId) free(wareId);
            if (buyCount) free(buyCount);

        });

        free_decrypt_result(&decryptResult);
        cJSON_Delete(json);


        // 测试响应一个加密的内容
        // EncryptRequestData encrypted_response = encrypt_request_data("{\"message\": \"自定义加密响应内容\", \"status\":true}");

        // if (!encrypted_response.status) {
        //     LOG("数据加密失败");
        //     return false;
        // }

        // size_t encrypted_response_size = strlen(encrypted_response.data) + strlen(encrypted_response.key) + strlen(encrypted_response.sign) + 100;
        // char* encrypted_response_json = (char*)malloc(encrypted_response_size);
        // if (!encrypted_response_json) {
        //     free_encrypt_request_data(&encrypted_response);
        //     return false;
        // }
        // snprintf(encrypted_response_json, encrypted_response_size, "{\"data\":\"%s\",\"key\":\"%s\",\"sign\":\"%s\"}", encrypted_response.data, encrypted_response.key, encrypted_response.sign);
        
        // LOG("响应加密数据: %s", encrypted_response_json);

        // free_encrypt_request_data(&encrypted_response);
        
        // http_service_send_json(client_socket, HTTP_STATUS_OK, encrypted_response_json);
        // free(encrypted_response_json);

        http_service_send_json(client_socket, HTTP_STATUS_OK, "{\"success\": true}");
        return 1; // 已处理
    }

    // 🔧 返回0表示路径未匹配，HttpService会直接关闭连接
    return 0;
}

// 向JDToolsService获取设备信息，然后向服务器查询设备状态
bool checkDeviceStatus() {
    // 临时变量初始化
    char* response = NULL;
    cJSON* json = NULL;
    char* deviceInfo = NULL;
    char *check = NULL;
    DecryptResult decryptDeviceInfoResult = {0};

    // 设置请求头
    const char* headers = "Content-Type: application/json";
    // 发送请求
    int statusCode = 0;
    // http://127.0.0.1:48156/device
    const uint8_t dvUrlKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x67,0x25,0x68,0xDF,0x79,0x06,0x07,0x6E,0x88,0x49,0x19,0xB3,0x3C,0xA3,0x3C,0xA5,0x23,0xC1,0x08,0xC4,0x97,0xBD,0x64,0xC6,0x7F,0x7A,0xB7,0x62,0x82,0x5D,0x13,0xB6,0x69};
    char *durl = decrypt_string_v2(dvUrlKey, sizeof(dvUrlKey));
    if (!durl) {
        LOG("解密本地请求设备信息url解密失败");
        showToastMessage("请求地址解密失败", 1);
        return false;
    }
    bool success = socket_post_request_sync(durl, "{}", headers, &response, &statusCode);
    free(durl);
    if (!success) {
        LOG("HTTP请求失败: %s", response ? response : "未知错误");
        showToastMessage("服务异常", 1);
        if (response) free(response);
        return false;
    }

    LOG("HTTP状态码: %d", statusCode);
    LOG("响应内容: %s", response ? response : "空响应");

    if (statusCode != 200) {
        LOG("HTTP状态码错误: %d", statusCode);
        showToastMessage("获取设备信息请求失败", 1);
        if (response) free(response);
        return false;
    }

    json = JSONUtils_ParseString(response);
    if (json == NULL) {
        if (response) free(response);
        return false;
    }

    const char* data = JSONUtils_GetStringValue(json, "data");
    const char* key = JSONUtils_GetStringValue(json, "key");
    const char* sign = JSONUtils_GetStringValue(json, "sign");
    if (data == NULL || key == NULL || sign == NULL) {
        showToastMessage("设备信息请求结果异常", 1);
        cJSON_Delete(json);
        if (response) free(response);
        return false;
    }
    // 解密设备信息
    decryptDeviceInfoResult = decrypt_request_data(data, key, sign);
    if (!decryptDeviceInfoResult.status) {
        cJSON_Delete(json);
        if (response) free(response);
        showToastMessage("解密设备信息失败", 1);
        LOG("❌解密设备信息失败");
        return false;
    }
    cJSON* requestData = decryptDeviceInfoResult.data;

    deviceInfo = JSONUtils_ToString(requestData, 1);
    if (!deviceInfo) {
        LOG("❌设备信息序列化失败");
        showToastMessage("设备信息序列化失败", 1);
        goto cleanup;
    }
    LOG("设备信息: %s", deviceInfo);

    const uint8_t ckKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xDF, 0x8E, 0x1A, 0x40, 0xEA, 0xE9, 0x21, 0x30, 0x1E };
    check = decrypt_string_v2(ckKey, sizeof(ckKey));
    if (!check) {
        LOG("check 解密失败");
        goto cleanup;
    }

    CryptoRequestConfig config = {
        .path = "/tools/v1",
        .action = check,
        .deviceInfo = deviceInfo,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

    // 先释放临时变量
    if (deviceInfo) free(deviceInfo);
    cJSON_Delete(json);
    free_decrypt_result(&decryptDeviceInfoResult);
    if (response) free(response);

    if (!result.success) {
        crypto_request_free_result(&result);
        return false;
    }
    if (!result.response_data) {
        crypto_request_free_result(&result);
        return false;
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        crypto_request_free_result(&result);
        return false;
    }
    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        if (message) {
            const char* error = message ? message : "未知错误";
            showToastMessage(error, 2);
            LOG("设备状态校验失败: %s", error);
        } else {
            LOG("设备状态校验失败: 未知错误 %d", code);
        }
        
        crypto_request_free_result(&result);
        return false;
    }
    cJSON* response_data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!response_data) {
        crypto_request_free_result(&result);
        return false;
    }
    bool status = JSONUtils_GetBoolValue(response_data, "status", false);
    if (!status) {
        const char* msg = JSONUtils_GetStringValue(response_data, "msg");
        const char* error = msg ? msg : "unknown error";
        showToastMessage(error, 2);
    }

    // 释放资源
    crypto_request_free_result(&result);

    LOG("授权状态: %s", status ? "已授权" : "未授权");

    return status;

cleanup:
    // 统一清理临时变量
    if (deviceInfo) free(deviceInfo);
    if (check) free(check);
    if (json) cJSON_Delete(json);
    if (decryptDeviceInfoResult.status) {
        free_decrypt_result(&decryptDeviceInfoResult);
    }
    if (response) free(response);
    return false;
}


__attribute__((constructor))
static void start_jdtools_service(void) {
    
    // 只在特定进程中运行示例
    if (strcmp(getprogname(), "JD4iPhone") == 0) {
        if (!checkDeviceStatus()) {
            LOG("设备未授权或未绑定");
            return;
        }
        // 启动HTTP服务
        // start_simple_http_service();
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 127.0.0.1
            const uint8_t addKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA9, 0x5B, 0x81, 0xEF, 0x8A, 0x01, 0x5F, 0x13, 0x87, 0x38, 0x5B, 0x9B, 0x9C };
            char *address = decrypt_string_v2(addKey, sizeof(addKey));
            if (!address) {
                return;
            }
            // 创建自定义配置
            http_service_config_t config = {
                .port = 48237,
                .bind_address = address,  // 本地绑定
                .request_handler = request_handler,
                .service_name = "JDTools"
            };

            // 启动服务
            if (http_service_start(&config) == 0) {
                service_status = true;
                enable_no_sleep("KeepAlive");
            }
            free(address);

        });

        // 监听拦截弹窗窗口
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // [UIViewHookManager start];
            start_uiview_hook();
        });

        // 启动数据收集
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 初始化全局网络数据
            initializeGlobalNetworkData();
            // 创建 AFURLSessionManager 实例，传入全局变量
            s_networkMonitorInstance = [[DataRecordManager alloc] initWithDataStore:g_collectedNetworkData];
            // 启动网络监控
            [s_networkMonitorInstance startMonitoring];
        });
    }
}

// 析构函数
__attribute__((destructor))
static void cleanup_jdtools_service(void) {
    if (strcmp(getprogname(), "JD4iPhone") == 0) {
        if (service_status) {
            // 确保服务已停止
            http_service_stop();

            disable_no_sleep();
        }

        // 清理全局变量
        if (s_networkMonitorInstance) {
            // [s_networkMonitorInstance stopMonitoring];
            s_networkMonitorInstance = nil;
        }
    }
}
