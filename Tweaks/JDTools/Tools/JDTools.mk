# JDTools Plugin Configuration
# This file contains all configuration for the JDTools plugin

# DEBUG 控制变量，默认为 0（不输出日志） 1 输出日志
# DEBUG = 0

JDTools_FILES = \
$(CRYPTOR_FILES) \
$(CJSON_FILES) \
$(HTTPSERVICE_FILES) \
$(REQUESTMANAGER_FILES) \
../../Core/Window/Toast.m \
../../Core/Window/Dialog.m \
../../Core/Window/QRCode.m \
./Controller/Dialog.c \
./Controller/Toast.c \
./Controller/InputText.c \
./Controller/Manager.c \
./Controller/Address.c \
./Controller/Search.c \
./Controller/Ware.c \
./Controller/URLScheme.c \
./Controller/Order.c \
./Controller/Controller.c \
./Controller/Login.c \
./Controller/SMSCode.c \
./Controller/Payment.m \
./Controller/Hook/UIViewHookManager.c \
./Controller/Hook/DataRecordManager.m \
./Controller/Hook/GlobalNetworkData.m \
./Controller/Hook/QR.m \
./Tools/JDTools.m

# Compiler flags for JDTools
JDTools_CFLAGS = -fobjc-arc \
$(CRYPTOR_CFLAGS) \
$(CJSON_CFLAGS) \
$(HTTPSERVICE_CFLAGS) \
$(REQUESTMANAGER_CFLAGS) \
-I./Controller \
-I./Controller/Hook \
-I../../Core/Window \
-I../../Core/Device \
-I../../Core/Log/ \
-DDEBUG=$(DEBUG) \



JDTools_FRAMEWORKS = IOKit CoreFoundation
JDTools_LDFLAGS += -framework Foundation -framework UIKit -lobjc




RANDOM_SEED := $(shell echo $$RANDOM)
JDTools_CFLAGS += -fobjc-arc \
                    -O3 \
                    -flto \
                    -fvisibility=hidden \
                    -funroll-loops \
                    -fdata-sections \
                    -ffunction-sections \
                    -fomit-frame-pointer \
                    -finline-functions \
                    -fno-stack-protector \
                    -fno-common \
                    -fno-asynchronous-unwind-tables \
                    -fno-exceptions \
                    -ObjC \
                    -DANTI_PTRACE \
                    -DANTI_HOOK \
                    -DANTI_DUMP \
                    -DANTI_DEBUG \
                    -DENABLE_ENCRYPTION \
                    -DRANDOM_SEED=$(RANDOM_SEED)

JDTools_LDFLAGS = \
-Wl,-S \
-Wl,-x \
-Wl,-dead_strip \
-F$(THEOS)/sdks/iPhoneOS14.5.sdk/System/Library/PrivateFrameworks \


JDTools_FRAMEWORKS = UIKit Foundation SystemConfiguration Network CFNetwork CoreFoundation IOKit MobileCoreServices AppSupport
# 在链接时使用 strip 删除符号表和调试信息
JDTools_POST_LINK = \
    strip -S --strip-all --remove-section=.note.gnu.build-id --remove-section=.comment --remove-section=__LLVM,__bitcode $@ && \
    \
    codesign --remove-signature $@ && \
    \
    codesign --force --sign - $@