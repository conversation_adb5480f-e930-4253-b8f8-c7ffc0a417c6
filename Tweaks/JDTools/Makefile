# rootful
INSTALL_TARGET_PROCESSES = SpringBoard
ARCHS = arm64 arm64e


# 如果指定了 BUILD=rootless，则切换到 rootless 配置
ifeq ($(BUILD),rootful)
TARGET := iphone:clang:latest:13.0
else
TARGET = iphone:clang:latest:15.0
THEOS_PACKAGE_SCHEME = rootless
endif

DEBUG = 1
# DEBUG 控制变量，默认为 0（不输出日志） 1 输出日志

OBFUSCATOR_FLAGS = \
    -mllvm -split \
    -mllvm -split_num=10 \
    -mllvm -sub \
    -mllvm -sub_loop=10 \
    -mllvm -bcf \
    -mllvm -bcf_loop=10 \
    -mllvm -fla \
    -mllvm -obf-strip-symbols \
    -mllvm -obf-function-name \
    -mllvm -obf-pointer \
    -mllvm -obf-string \
    -mllvm -reg-init-obf \
    -mllvm -merge-cbe \
    -mllvm -force-flat-dispatch \
    -mllvm -expand-memop \
    -mllvm -expand-global-obf \
    -mllvm -const-obf \
    -mllvm -const-obf-loop=5 \
    -mllvm -enable-cff-except \
    -mllvm -cff-except-prob=100

include $(THEOS)/makefiles/common.mk
include ../../Core/CJSON/cJSON.mk
include ../../Core/Cryptor/Cryptor.mk
include ../../Core/HttpService/HttpService.mk
include ../../Core/RequestManager/RequestManager.mk

TWEAK_NAME = JDToolsService JDTools
# ServiceExample


# Include plugin configurations
include ./Service/JDToolsService.mk
include ./Tools/JDTools.mk
# include ServiceExample.mk

include $(THEOS_MAKE_PATH)/tweak.mk
