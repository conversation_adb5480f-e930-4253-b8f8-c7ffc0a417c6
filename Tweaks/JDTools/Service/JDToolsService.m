#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import "Log.h"
#import "HttpService.h"
#import "JDToolsService.h"
#import "ButtonClickHandler.h"
#import "DeviceInfo.h"
#import "FloatingView.h"
#import "FloatingWindow.h"
#import "Toast.h"
#import "StringCryptor.h"

#import "Buttons/Address/Address.h"
#import "Buttons/Order/Order.h"

#import "Buttons/Base/BaseButtonManager.h"
#import "Buttons/Debug/Debug.h"
#import "Buttons/Default/Default.h"
#import "Buttons/Login/Login.h"
#import "Buttons/Ware/Ware.h"
#import "Buttons/Payment/Payment.h"
#import "Buttons/Search/Search.h"
// #import "Buttons/Network/Network.h"
#import "JSONUtils.h"
#import "CryptoRequest.h"
#import "DataCrypto.h"
// #import "StringCryptor.h"
#import "StringCryptor_v2.h"

#include <stdio.h>
#include <time.h>
#include <stdbool.h>
#include "./productInfo.h"

// 判断当前时间是否已经到期
// bool isExpired(int expireTimestamp) {
//     time_t now = time(NULL);  // 当前时间戳（秒）
//     return now >= expireTimestamp;
// }

// 记录设备到期时间
int expireTime = 0;

// 记录服务状态
bool service_status = false;

// 定义全局常量指针
const char *serialNumber = NULL;
// 全局变量 - 支持多个窗口
static NSMutableArray<FloatingView *> *floatingViews = nil;
static NSMutableArray<FloatingWindow *> *floatingWindows = nil;

// 获取本地时间、设备信息进行服务器校验状态，判断时间，绑定设备
bool checkDeviceStatus(void) {
    char* deviceInfo = deviceInfoInJson();
    if (!deviceInfo) {
        return false;
    }

    // 临时变量声明（用于统一清理）
    char *check = NULL;

    // check
    const uint8_t ckKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xDF, 0x8E, 0x1A, 0x40, 0xEA, 0xE9, 0x21, 0x30, 0x1E };
    check = decrypt_string_v2(ckKey, sizeof(ckKey));
    if (!check) {
        LOG("check 解密失败");
        goto cleanup;
    }

    CryptoRequestConfig config = {
        .path = "/tools/v1",
        .action = check,
        .deviceInfo = deviceInfo,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

    // 先释放临时变量
    if (deviceInfo) free(deviceInfo);
    if (check) free(check);

    if (!result.success) {
        crypto_request_free_result(&result);
        return false;
    }
    if (!result.response_data) {
        crypto_request_free_result(&result);
        return false;
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        crypto_request_free_result(&result);
        return false;
    }
    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        if (message) {
            const char* error = message ? message : "未知错误";
            (void)error;
            LOG("设备状态校验失败: %s", error);
        } else {
            LOG("设备状态校验失败: 未知错误 %d", code);
        }
        
        crypto_request_free_result(&result);
        return false;
    }
    cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!data) {
        crypto_request_free_result(&result);
        return false;
    }

    int time = JSONUtils_GetIntValue(data, "time", 0);
    if (!time || time < 0) {
        LOG("未返回设备到期时间");
        crypto_request_free_result(&result);
        return false;
    }

    LOG("设备到期时间: %d", time);

    expireTime = time;

    bool status = JSONUtils_GetBoolValue(data, "status", false);
    

    // 释放资源
    crypto_request_free_result(&result);

    LOG("[JDToolsService] 授权状态: %s", status ? "已授权" : "未授权");

    return status;

cleanup:
    // 统一清理临时变量
    if (deviceInfo) free(deviceInfo);
    if (check) free(check);
    return false;
}

// 独立封装获取关键词数据的静态方法，返回值为:NSArray
NSArray *fetchKeywords(void) {
    char serialBuffer[32];
    getSerialNumber(serialBuffer, sizeof(serialBuffer));
    LOG("--------> 设备序列号: %s", serialBuffer);
    // char request_data[256] = {0};

    // 安全地构建请求数据，检查返回值
    // int written = snprintf(request_data, sizeof(request_data),
    //         "{\"action\":\"keywords\", \"serialNumber\": \"%s\"}", serialBuffer);
    // if (written < 0 || written >= (int)sizeof(request_data)) {
    //     LOG("构建关键词请求数据失败");
    //     return @[]; // 返回空数组
    // }
    // LOG("--------> 关键词请求数据: %s", request_data);
    
    // CryptoRequestConfig config = {.request_data = request_data};
    CryptoRequestConfig config = {
        .path = "/tools/v1",
        .action = "keywords",
        .serial_number = serialBuffer,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

    // 释放 request_data
    // free(request_data);

    if (!result.success) {
        crypto_request_free_result(&result);
        return @[]; // 返回空数组
    }

    if (!result.response_data) {
        crypto_request_free_result(&result);
        return @[]; // 返回空数组
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        crypto_request_free_result(&result);
        return @[]; // 返回空数组
    }

    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        const char* error = message ? message : "未知错误";
        (void)error;
        LOG("获取关键词失败: %s", error);
        crypto_request_free_result(&result);
        return @[]; // 返回空数组
    }
    cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!data) {
        crypto_request_free_result(&result);
        return @[];
    }
    bool status = JSONUtils_GetBoolValue(data, "status", false);
    if (!status) {
        // showToastMessage("缺少关键词", 2.0);
        crypto_request_free_result(&result);
        return @[];
    }

    const char *keywords = JSONUtils_GetStringValue(data, "keywords");
    if (!keywords || strlen(keywords) == 0) {
        LOG("关键词为空");
        crypto_request_free_result(&result);
        return @[];
    }

    NSString *jsonString = [NSString stringWithUTF8String:keywords];
    if (!jsonString) {
        LOG("关键词字符串非法");
        crypto_request_free_result(&result);
        return @[];
    }

    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    crypto_request_free_result(&result);

    NSError *error = nil;
    NSArray *parsedArray = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    if (error || ![parsedArray isKindOfClass:[NSArray class]]) {
        LOG("解析关键词 JSON 失败: %s", error.localizedDescription.UTF8String);
        return @[];
    }

    LOG("获取关键词成功, count: %lu", (unsigned long)parsedArray.count);
    return parsedArray;
}

void createDefaultFloatingViews() { 
    // 设备未授权情况下，只创建一个默认窗口，不需要主控制窗口
    LOG("[JDToolsService] 设备未授权，创建默认窗口");

    // 初始化数组
    floatingViews = [NSMutableArray array];
    floatingWindows = [NSMutableArray array];

    CGFloat startY = 150.00;
    UIColor *uiColor = [UIColor colorWithRed:0x02/255.0 green:0x56/255.0 blue:0xff/255.0 alpha:1.0];

    // 设置全局按钮点击处理函数
    // setGlobalButtonClickHandler(buttonClickHandler);

    NSMutableArray *windowConfigs = [NSMutableArray arrayWithArray:@[
        [Default initWithConfig:uiColor startY:startY],
    ]];

    // 创建默认悬浮窗
    for (NSInteger i = 0; i < windowConfigs.count; i++) {
        NSMutableDictionary *config = [windowConfigs[i] mutableCopy];

        // 动态计算startY：第一个使用原始startY，后续每个递增50
        CGFloat dynamicStartY = startY + (i * 50);
        config[@"startY"] = @(dynamicStartY);

        // 创建超级悬浮窗口
        FloatingWindow *window = [[FloatingWindow alloc] init];

        // 创建悬浮视图
        FloatingView *view = [[FloatingView alloc] initWithConfig:config];

        // 使用优化的添加子视图方法
        [window addFloatingSubview:view];

        NSString *windowId = config[@"windowId"];
        (void)windowId;
        LOG("[JDToolsService] 创建默认悬浮窗 %ld，配置: %s", (long)i, windowId ? windowId.UTF8String : "未知");

        // 显示动画 - 错开时间
        view.alpha = 0;
        view.transform = CGAffineTransformMakeScale(0.5, 0.5);

        // 强制显示并保持在最顶层
        window.hidden = NO;
        [window makeKeyAndVisible];

        // 强制刷新显示（解决某些系统不显示的问题）
        [window forceRefreshDisplay];

        // ✅ 添加到数组中进行管理
        [floatingWindows addObject:window];
        [floatingViews addObject:view];

        // 错开显示动画时间
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * i * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.5
                                    delay:0
                    usingSpringWithDamping:0.7
                    initialSpringVelocity:0.5
                                options:UIViewAnimationOptionCurveEaseOut
                                animations:^{
                                    view.alpha = 1.0;
                                    view.transform = CGAffineTransformIdentity;
                                }
                                completion:^(BOOL finished) {
                                    LOG("[JDToolsService] 默认悬浮窗 %ld 显示动画完成", (long)i);
                                }];
        });
    }

    LOG("[JDToolsService] 默认悬浮窗创建完成，共创建 %lu 个窗口", (unsigned long)windowConfigs.count);
}
// 创建悬浮窗
void createFloatingViews(ButtonClickHandler buttonClickHandler) {
    if (floatingViews) return;

    // 如果获取失败则返回空数组
    NSArray *taskKeywordsList = fetchKeywords();

    // 初始化数组
    floatingViews = [NSMutableArray array];
    floatingWindows = [NSMutableArray array];
    CGFloat startY = 150.00;
    UIColor *uiColor = [UIColor colorWithRed:0x02/255.0 green:0x56/255.0 blue:0xff/255.0 alpha:1.0];
    // UIColor *uiColor = [UIColor blueColor];

    // 设置全局按钮点击处理函数
    setGlobalButtonClickHandler(buttonClickHandler);

    // 创建基础窗口配置（除了Search）
    NSMutableArray *windowConfigs = [NSMutableArray arrayWithArray:@[
        [Login initWithConfig:uiColor startY:startY],
        [Address initWithConfig:uiColor startY:startY],
        [Ware initWithConfig:uiColor startY:startY],
        [Payment initWithConfig:uiColor startY:startY],
        [Order initWithConfig:uiColor startY:startY],
        // [Network initWithConfig:uiColor startY:startY],
        [Debug initWithConfig:uiColor startY:startY],
    ]];

    // 根据taskKeywordsList动态创建多个Search窗口
    if (taskKeywordsList && [taskKeywordsList isKindOfClass:[NSArray class]]) {
        LOG("[JDToolsService] 开始创建 %lu 个搜索窗口", (unsigned long)[taskKeywordsList count]);

        for (NSInteger i = 0; i < [taskKeywordsList count]; i++) {
            NSDictionary *taskItem = taskKeywordsList[i];

            // 验证数据格式
            if (![taskItem isKindOfClass:[NSDictionary class]]) {
                LOG("[JDToolsService] 跳过无效的任务项 %ld", (long)i);
                continue;
            }

            NSString *buttonText = taskItem[@"shortTitle"];
            NSArray *params = taskItem[@"keywords"];

            if (!buttonText || !params) {
                LOG("[JDToolsService] 跳过缺少必要字段的任务项 %ld", (long)i);
                continue;
            }

            // 为每个任务项创建一个Search窗口配置
            NSDictionary *searchConfig = [Search initWithConfig:uiColor
                                                        startY:startY
                                                searchConfig:@[taskItem]  // 传递单个任务项
                                                    windowTitle:buttonText]; // 传递窗口标题

            if (searchConfig) {
                [windowConfigs addObject:searchConfig];
                NSLOG("[JDToolsService] 创建搜索窗口: %@", buttonText);
            }
        }
    } else {
        LOG("[JDToolsService] taskKeywordsList 为空或格式错误，跳过搜索窗口创建");
    }

    // 调试：打印所有搜索窗口配置
    // [Search debugPrintAllConfigs];

    // 🎯 首先创建主控制器窗口
    LOG("[JDToolsService] 开始创建主控制器窗口");
    FloatingView *masterControlView = [FloatingView createMasterControlWindow];
    if (masterControlView) {
        FloatingWindow *masterWindow = [[FloatingWindow alloc] init];

        // 使用优化的添加子视图方法
        [masterWindow addFloatingSubview:masterControlView];
        LOG("[JDToolsService] 主控制器视图已添加到窗口");

        // 主控制器窗口始终显示
        masterWindow.hidden = NO;
        [masterWindow makeKeyAndVisible];

        // 强制刷新显示（解决某些系统不显示的问题）
        [masterWindow forceRefreshDisplay];

        // ✅ 添加到数组中进行管理
        [floatingWindows addObject:masterWindow];
        [floatingViews addObject:masterControlView];

        // 主控制器立即显示动画
        masterControlView.alpha = 0;
        masterControlView.transform = CGAffineTransformMakeScale(0.5, 0.5);

        NSLOG(@"[ToolsTweak] 主控制器已创建，frame: %@", NSStringFromCGRect(masterControlView.frame));

        [UIView animateWithDuration:0.8
                            delay:0.2
            usingSpringWithDamping:0.5
            initialSpringVelocity:1.0
                            options:UIViewAnimationOptionCurveEaseOut
                        animations:^{
            masterControlView.alpha = 1.0;
            masterControlView.transform = CGAffineTransformIdentity;
        } completion:^(BOOL finished) {
            LOG("[JDToolsService] 主控制器动画完成，alpha: %f", masterControlView.alpha);

            // 验证主控制器窗口是否真正可见
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                BOOL visible = [masterWindow isActuallyVisible];
                LOG("[JDToolsService] 主控制器窗口可见性: %s", visible ? "可见" : "不可见");

                if (!visible) {
                    LOG("[JDToolsService] 主控制器窗口不可见，尝试强制刷新");
                    [masterWindow forceRefreshDisplay];
                }
            });
        }];
    } else {
        LOG("[JDToolsService] ❌ 主控制器创建失败");
    }

    // 分别统计左右两侧的窗口索引
    NSInteger leftIndex = 0;
    NSInteger rightIndex = 0;

    // 然后创建其他悬浮窗 - 根据position动态计算坐标
    for (NSInteger i = 0; i < windowConfigs.count; i++) {
        NSMutableDictionary *config = [windowConfigs[i] mutableCopy];

        NSString *position = config[@"position"];
        CGFloat dynamicStartY;

        if ([position isEqualToString:@"left"]) {
            // 左侧窗口：从传入的startY开始，每个窗口递增50
            dynamicStartY = startY + (leftIndex * 50);
            leftIndex++;
        } else {
            // 右侧窗口：从传入的startY开始，每个窗口递增50
            dynamicStartY = startY + (rightIndex * 50);
            rightIndex++;
        }

        config[@"startY"] = @(dynamicStartY);

        // 创建超级悬浮窗口
        FloatingWindow *window = [[FloatingWindow alloc] init];

        // 创建悬浮视图
        FloatingView *view = [[FloatingView alloc] initWithConfig:config];

        // 使用优化的添加子视图方法
        [window addFloatingSubview:view];

        NSString *windowId = config[@"windowId"];
        (void)windowId;
        LOG("[JDToolsService] 创建悬浮窗 %ld，配置: %s", (long)i, windowId ? windowId.UTF8String : "未知");

        // 显示动画 - 错开时间
        view.alpha = 0;
        view.transform = CGAffineTransformMakeScale(0.5, 0.5);

        // 强制显示并保持在最顶层
        window.hidden = NO;
        [window makeKeyAndVisible];

        // 强制刷新显示（解决某些系统不显示的问题）
        [window forceRefreshDisplay];

        // 错开的入场动画
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(i * 0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:0.5
                                delay:0
                usingSpringWithDamping:0.6
                initialSpringVelocity:0.8
                                options:UIViewAnimationOptionCurveEaseOut
                            animations:^{
                view.alpha = 1.0;
                view.transform = CGAffineTransformIdentity;
            } completion:nil];
        });

        // 添加到数组
        [floatingWindows addObject:window];
        [floatingViews addObject:view];
    }

    // 确保主应用窗口保持活跃
    dispatch_async(dispatch_get_main_queue(), ^{
        NSSet *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            if ([scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                if (windowScene.activationState == UISceneActivationStateForegroundActive) {
                    for (UIWindow *window in windowScene.windows) {
                        BOOL isFloatingWindow = NO;
                        for (FloatingWindow *floatingWin in floatingWindows) {
                            if (window == floatingWin) {
                                isFloatingWindow = YES;
                                break;
                            }
                        }
                        if (!isFloatingWindow && !window.isKeyWindow) {
                            [window makeKeyWindow];
                            break;
                        }
                    }
                    break;
                }
            }
        }
    });

    

}

static int request_handler(const char* method, const char* path, const char* headers, const char* body, int client_socket) {

    // 🔧 签名验证通过，处理不同的路径
    if (strcmp(path, "/device") == 0) {
        // 获取设备信息
        char* deviceInfo = deviceInfoInJson();
        if (!deviceInfo) {
            LOG("获取设备信息失败");
            return 0;
        }

        EncryptRequestData encrypted_response = encrypt_request_data(deviceInfo);
        if (!encrypted_response.status) {
            free(deviceInfo);
            LOG("设备信息加密失败");
            return 0;
        }
        free(deviceInfo);

        // 安全地计算所需大小，防止整数溢出
        size_t data_len = strlen(encrypted_response.data);
        size_t key_len = strlen(encrypted_response.key);
        size_t sign_len = strlen(encrypted_response.sign);

        // 检查单个字段长度
        if (data_len > 16384 || key_len > 1024 || sign_len > 1024) {
            LOG("加密响应数据字段过长");
            free_encrypt_request_data(&encrypted_response);
            return 0;
        }

        // 安全地计算总大小
        size_t encrypted_response_size = data_len + key_len + sign_len + 100;

        // 检查是否溢出
        if (encrypted_response_size < data_len || encrypted_response_size < key_len || encrypted_response_size < sign_len) {
            LOG("加密响应数据大小计算溢出");
            free_encrypt_request_data(&encrypted_response);
            return 0;
        }

        char* encrypted_response_json = (char*)malloc(encrypted_response_size);
        if (!encrypted_response_json) {
            free_encrypt_request_data(&encrypted_response);
            LOG("分配内存失败");
            return 0;
        }
        int written = snprintf(encrypted_response_json, encrypted_response_size, "{\"data\":\"%s\",\"key\":\"%s\",\"sign\":\"%s\"}", encrypted_response.data, encrypted_response.key, encrypted_response.sign);
        if (written < 0 || (size_t)written >= encrypted_response_size) {
            LOG("加密响应数据JSON格式化失败");
            free(encrypted_response_json);
            free_encrypt_request_data(&encrypted_response);
            return 0;
        }

        LOG("响应加密设备数据: %s", encrypted_response_json);
        free_encrypt_request_data(&encrypted_response);
        

        http_service_send_json(client_socket, HTTP_STATUS_OK, encrypted_response_json);
        free(encrypted_response_json);
        LOG("设备信息响应成功");
        return 1; // 已处理
    }
    // 🔧 返回0表示路径未匹配，HttpService会直接关闭连接
    return 0;
}

__attribute__((constructor))
static void start_jdtools_service(void) {

    LOG("[JDToolsService]: Starting JDToolsService");

    const uint8_t prognameKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x5C,0x46,0x7E,0x96,0x9F,0x4A,0xE6,0x1E,0x01,0x6B,0xA9,0x03,0xD6,0x2D,0xD2};

    char *progname = decrypt_string_v2(prognameKey, sizeof(prognameKey));
    if (!progname) {
        return;
    }
    LOG("[JDToolsService]: current process name : %s", progname);
    if (strcmp(getprogname(), progname) != 0) {
        // 释放内存
        free(progname);
        LOG("[JDToolsService]: Not the target process");
        return;
    }
    // 释放内存
    free(progname);

    // 检查设备状态
    if (!checkDeviceStatus()) {
        // dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        //     createDefaultFloatingViews();
        // });
        return;
    }
    // bool authStatus = checkDeviceStatus();

    // 进程正确，开始加载服务
    LOG("[JDToolsService]: Starting JDToolsService");

    // 创建按钮窗口
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 创建悬浮窗
        LOG("[JDToolsService]: Creating floating views");
        createFloatingViews(handleButtonClick);
        // createFloatingViews(handleButtonClick, authStatus);
    });

    // 记录当前设备序列号
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        char serialBuffer[32];
        getSerialNumber(serialBuffer, sizeof(serialBuffer));

        // 复制到常量内存，检查分配是否成功
        serialNumber = strdup(serialBuffer);
        if (!serialNumber) {
            LOG("[JDToolsService]: 序列号内存分配失败");
            return;
        }

        LOG("[JDToolsService]: Service started with serial number: %s", serialNumber);

    });

    // 启动http服务
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 127.0.0.1
        const uint8_t addKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA9, 0x5B, 0x81, 0xEF, 0x8A, 0x01, 0x5F, 0x13, 0x87, 0x38, 0x5B, 0x9B, 0x9C };
        char *address = decrypt_string_v2(addKey, sizeof(addKey));
        if (!address) {
            return;
        }
        // LOG("解密得到地址: %s", address);
        const char *serviceName = "JDToolsService";
        LOG("[JDToolsService]: Starting HTTP service %s", serviceName);
        // 创建自定义配置
        http_service_config_t config = {
            .port = 48156,
            .bind_address = address,  // 绑定到所有接口
            .request_handler = request_handler,
            .service_name = serviceName
        };

        // 启动服务
        if (http_service_start(&config) == 0) {
            service_status = true;
            LOG("[JDToolsService] HTTP service started");
            // 启动服务成功就需要开启防止休眠
            LOG("[JDToolsService] Enabling no sleep");
            enable_no_sleep("KeepAlive");
        } else {
            LOG("[JDToolsService] HTTP service start failed");
        }
        free(address);
    });
}
// 析构函数
__attribute__((destructor))
static void cleanup_jdtools_service(void) {
    if (!service_status) {
    
        // 关闭http服务
        http_service_stop();
        // 关闭防止休眠
        disable_no_sleep();

        service_status = false;
    }
    // 释放全局变量内存
    if (serialNumber) {
        free((void*)serialNumber);
        serialNumber = NULL;
    }
}
