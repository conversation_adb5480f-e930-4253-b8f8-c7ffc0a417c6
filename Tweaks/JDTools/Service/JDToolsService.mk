# JDToolsService Plugin Configuration
# This file contains all configuration for the JDToolsService plugin

# DEBUG 控制变量，默认为 0（不输出日志） 1 输出日志
# DEBUG = 0

JDToolsService_FILES = \
$(CRYPTOR_FILES) \
$(CJSON_FILES) \
$(HTTPSERVICE_FILES) \
$(REQUESTMANAGER_FILES) \
../../Core/Device/DeviceInfo.c \
../../Core/Device/DeviceControl.m \
../../Core/Window/Dialog.m \
../../Core/Window/Toast.m \
../../Core/Window/FloatingView.m \
../../Core/Window/FloatingWindow.m \
Buttons/Address/Address.m \
Buttons/Order/Order.m \
Buttons/Base/BaseButtonManager.m \
Buttons/Debug/Debug.m \
Buttons/Default/Default.m \
Buttons/Login/Login.m \
Buttons/Ware/Ware.m \
Buttons/Payment/Payment.m \
Buttons/Search/Search.m \
Buttons/ButtonClickHandler.c \
./Service/JDToolsService.m

# Buttons/Network/Network.m \
# Compiler flags for JDToolsService
JDToolsService_CFLAGS = -fobjc-arc

JDToolsService_CFLAGS += \
$(CRYPTOR_CFLAGS) \
$(WINDOW_CFLAGS) \
$(CJSON_CFLAGS) \
$(HTTPSERVICE_CFLAGS) \
$(REQUESTMANAGER_CFLAGS) \
-I Buttons \
-I Buttons/Base/ \
-I Controller \
-I./Service \
-I../../Core/Device/ \
-I../../Core/Log/ \
-I../../Core/Window/ \
-DDEBUG=$(DEBUG)

JDToolsService_FRAMEWORKS = IOKit CoreFoundation
JDToolsService_LDFLAGS += -framework Foundation -framework UIKit -lobjc



RANDOM_SEED := $(shell echo $$RANDOM)
JDToolsService_CFLAGS += -fobjc-arc \
                    -O3 \
                    -flto \
                    -fvisibility=hidden \
                    -funroll-loops \
                    -fdata-sections \
                    -ffunction-sections \
                    -fomit-frame-pointer \
                    -finline-functions \
                    -fno-stack-protector \
                    -fno-common \
                    -fno-asynchronous-unwind-tables \
                    -fno-exceptions \
                    -ObjC \
                    -DANTI_PTRACE \
                    -DANTI_HOOK \
                    -DANTI_DUMP \
                    -DANTI_DEBUG \
                    -DENABLE_ENCRYPTION \
                    -DRANDOM_SEED=$(RANDOM_SEED)

JDToolsService_LDFLAGS = \
-Wl,-S \
-Wl,-x \
-Wl,-dead_strip \
-F$(THEOS)/sdks/iPhoneOS14.5.sdk/System/Library/PrivateFrameworks \


JDToolsService_FRAMEWORKS = UIKit Foundation SystemConfiguration Network CFNetwork CoreFoundation IOKit MobileCoreServices AppSupport
# 在链接时使用 strip 删除符号表和调试信息
JDToolsService_POST_LINK = \
    strip -S --strip-all --remove-section=.note.gnu.build-id --remove-section=.comment --remove-section=__LLVM,__bitcode $@ && \
    \
    codesign --remove-signature $@ && \
    \
    codesign --force --sign - $@