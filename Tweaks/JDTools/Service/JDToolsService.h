//
//  JDToolsService.h
//  JDTools
//
//  共享头文件，定义通用的按钮点击处理函数和常用类型
//

#ifndef JDToolsService_h
#define JDToolsService_h

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "ButtonClickHandler.h"
// #include "ButtonClickHandler.h"
// 到期时间
extern int expireTime;

// 到期状态
extern bool service_status;

// 全局设备序列号声明
extern const char *serialNumber;

// 按钮点击处理函数类型定义
typedef bool (*ButtonClickHandler)(const ButtonClickParams *params);

// 全局按钮点击处理函数声明
extern ButtonClickHandler g_buttonClickHandler;

// 设置全局按钮点击处理函数
void setGlobalButtonClickHandler(ButtonClickHandler handler);

// 执行按钮点击事件
bool executeButtonClick(const ButtonClickParams *params);

// 判断是否到期
// bool isExpired(int expireTimestamp);

// 通用的Manager协议
@protocol ButtonManagerProtocol <NSObject>

@required
// 获取悬浮窗配置的标准方法
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY;

@optional
// 带搜索配置的初始化方法（仅SearchManager需要）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY searchConfig:(NSArray *)searchConfig;

@end

#endif /* JDToolsService_h */
