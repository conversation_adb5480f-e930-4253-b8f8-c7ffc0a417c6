//
//  BaseButtonManager.h
//  JDTools
//
//  按钮管理器基类，提供通用的按钮点击处理逻辑
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "JDToolsService.h"

@interface BaseButtonManager : NSObject <ButtonManagerProtocol>

// 执行按钮点击命令的通用方法
+ (BOOL)executeCommandWithButtonId:(NSString *)buttonId WindowId:(NSString *)windowId;

// 创建标准的actionBlock（手动指定windowId）
+ (id)createActionBlock:(NSString *)windowId;

// 创建自动提取windowId的actionBlock
+ (id)createActionBlockWithConfig:(NSDictionary *)config;

// 辅助方法：自动添加actionBlock到配置中
+ (NSMutableDictionary *)addActionBlockToConfig:(NSDictionary *)config;

@end

void openAppWithURL(const char *url);