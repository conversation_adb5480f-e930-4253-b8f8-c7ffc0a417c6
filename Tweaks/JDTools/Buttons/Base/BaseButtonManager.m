//
//  BaseButtonManager.m
//  JDTools
//
//  按钮管理器基类实现
//
#include <objc/runtime.h>
#include <objc/message.h>
#import "BaseButtonManager.h"
#import "Toast.h"
#import "Log.h"
#import "../Debug/Debug.h"
// #import "../Network/Network.h"
#import "JDToolsService.h"

void openAppWithURL(const char *url) {
    Class UIApplicationClass = (Class)objc_getClass("UIApplication");
    id sharedApp = ((id (*)(Class, SEL))objc_msgSend)(UIApplicationClass, sel_registerName("sharedApplication"));

    Class NSURLClass = (Class)objc_getClass("NSURL");
    SEL URLWithStringSEL = sel_registerName("URLWithString:");
    SEL openURLSEL = sel_registerName("openURL:");

    id nsURL = ((id (*)(Class, SEL, id))objc_msgSend)(NSURLClass, URLWithStringSEL,
        ((id (*)(Class, SEL, const char *))objc_msgSend)((Class)objc_getClass("NSString"), sel_registerName("stringWithUTF8String:"), url));

    ((void (*)(id, SEL, id))objc_msgSend)(sharedApp, openURLSEL, nsURL);
}


// 全局按钮点击处理函数
ButtonClickHandler g_buttonClickHandler = NULL;

@implementation BaseButtonManager

// 设置全局按钮点击处理函数
void setGlobalButtonClickHandler(ButtonClickHandler handler) {
    g_buttonClickHandler = handler;
}

// 执行按钮点击事件
bool executeButtonClick(const ButtonClickParams *params) {
    if (g_buttonClickHandler) {
        // 判断是否已到期
        time_t now = time(NULL);
        if (now >= expireTime) {
            LOG("❌❌❌❌❌❌❌------->已到期");
            [Toast show:@"服务已到期" duration:2.0];
            return false;
        } else {
            LOG("✅✅✅✅✅✅✅------->未到期");
            bool success = g_buttonClickHandler(params);
            if (!success) {
                [Toast show:@"请检查京东是否已启动" duration:2.0];
            }
            return success;
            // return g_buttonClickHandler(params);
        }
    }
    return false;
}

// 执行按钮点击命令的通用方法
+ (BOOL)executeCommandWithButtonId:(NSString *)buttonId WindowId:(NSString *)windowId {
    // 检查服务状态
    if (!service_status) {
        [Toast show:@"服务未启动" duration:2.0];
        return NO;
    }
    ButtonClickParams params = {0};
    params.buttonId = [buttonId UTF8String];
    params.windowId = [windowId UTF8String];
    // 这里的序列号是JDToolsService.h中记录的全局变量
    params.serialNumber = serialNumber;
    // return executeButtonClick(&params);
    bool success = executeButtonClick(&params);
    // if (!success) {
    //     [Toast show:@"请检查京东是否已启动" duration:2.0];
    //     // [Toast show:@"尝试自动加载京东" duration:1.0];
    //     // openAppWithURL("openapp.jdmobile://");
    // }
    return success;
}

// 创建标准的actionBlock（手动指定windowId）
+ (id)createActionBlock:(NSString *)windowId {
    return ^(NSString *buttonId) {
        [self executeCommandWithButtonId:buttonId WindowId:windowId];
    };
}

// 创建自动提取windowId的actionBlock
+ (id)createActionBlockWithConfig:(NSDictionary *)config {
    NSString *windowId = config[@"windowId"];
    if (!windowId) {
        windowId = @"unknown";
    }
    // 判断windowId是否为debug
    if ([windowId isEqualToString:@"debug"]) {
        return ^(NSString *buttonId) {
            [Debug executeCommandWithButtonId:buttonId];
        };
    // } else if ([windowId isEqualToString:@"network"]) {
    //     return ^(NSString *buttonId) {
    //         [Network executeCommandWithButtonId:buttonId];
    //     };
    } else {
    return ^(NSString *buttonId) {
            [self executeCommandWithButtonId:buttonId WindowId:windowId];
        };
    }
}

// 辅助方法：自动添加actionBlock到配置中
+ (NSMutableDictionary *)addActionBlockToConfig:(NSDictionary *)config {
    NSMutableDictionary *mutableConfig = [config mutableCopy];
    mutableConfig[@"actionBlock"] = [self createActionBlockWithConfig:config];
    return mutableConfig;
}

// 基类的默认实现（子类应该重写）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {
    @throw [NSException exceptionWithName:@"AbstractMethodException"
                                   reason:@"Subclass must implement initWithConfig:startY:"
                                 userInfo:nil];
}

@end
