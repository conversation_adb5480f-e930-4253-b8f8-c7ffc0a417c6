//  Ware.m
#import "Ware.h"
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <objc/message.h>
#import <objc/runtime.h>
#import "FloatingView.h"

@implementation Ware

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"windowId": @"ware", // 🎯 添加窗口唯一ID
        @"buttonText": @"商",
        @"color": color,
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"自动垫单", kButtonIdKey: @"orderBuffer", kButtonAutoCollapseKey: @NO},
            @{kButtonTitleKey: @"加入购物车", kButtonIdKey: @"addToCart", kButtonAutoCollapseKey: @NO},
            // @{kButtonTitleKey: @"进入购物车", kButtonIdKey: @"gotoCart", kButtonAutoCollapseKey: @YES},
            // @{kButtonTitleKey: @"进入客服", kButtonIdKey: @"gotoChat", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"收藏/取消", kButtonIdKey: @"favorite", kButtonAutoCollapseKey: @NO},
            @{kButtonTitleKey: @"浏览评价", kButtonIdKey: @"comments", kButtonAutoCollapseKey: @NO},
            @{kButtonTitleKey: @"去结算", kButtonIdKey: @"checkout", kButtonAutoCollapseKey: @YES},
            // @{kButtonTitleKey: @"返回", kButtonIdKey: @"back", kButtonAutoCollapseKey: @YES},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
    // NSDictionary *buttonConfig = @{
    //     @"modules": @"ware",
    //     @"buttonId": buttonId
    // };
    // NSLog(@"按钮配置信息: %@", buttonConfig);

    // if ([buttonId isEqualToString:@"back"]) {
    //     callBackButtonClicked();
    //     return;
    // }
    // if ([buttonId isEqualToString:@"addToCart"]) {
    //     // 加入购物车
    //     performWareAction(G4WareActionAddToCart);
    // } else if ([buttonId isEqualToString:@"gotoCart"]) {
    //     // 去购物车
    //     performWareAction(G4WareActionGotoCart);
    // } else if ([buttonId isEqualToString:@"gotoChat"]) {
    //     // 去聊天
    //     performWareAction(G4WareActionGotoChat);
    // } else if ([buttonId isEqualToString:@"checkout"]) {
    //     // 去结算
    //     performWareAction(G4WareActionCheckout);
    // } else if ([buttonId isEqualToString:@"favorite"]) {
    //     // 收藏
    //     performWareAction(G4WareActionFavorite);
    // } else if ([buttonId isEqualToString:@"comments"]) {
    //     // 浏览评价
    //     scrollToCommentSemanticTagDetail();
    // }
}

@end
