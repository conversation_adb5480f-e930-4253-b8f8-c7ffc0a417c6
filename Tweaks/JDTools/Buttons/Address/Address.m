//  Address.m
#import "Address.h"
#import "FloatingView.h"

@implementation Address

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"buttonText": @"地",
        @"windowId": @"address",
        @"color": color,
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"地址管理", kButtonIdKey: @"jumpAddressPage", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"删除地址", kButtonIdKey: @"delete", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"自动创建", kButtonIdKey: @"createAddress", kButtonAutoCollapseKey: @(YES)},
            // @{kButtonTitleKey: @"返回", kButtonIdKey: @"back", kButtonAutoCollapseKey: @YES},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
}

@end
