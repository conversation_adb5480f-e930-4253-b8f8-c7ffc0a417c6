//
//  ButtonClickHandler.c
//  JDTools
//
//  按钮点击处理模块实现
//

#include "ButtonClickHandler.h"
#include "Log.h"
#include <string.h>
#include <stdlib.h>
#include "SocketHTTP.h"
#include "DataCrypto.h"
#include "JSONUtils.h"
#include "StringCryptor_v2.h"


// 辅助函数：安全地追加JSON字段
static bool append_json_field(char **json_str, size_t *capacity, size_t *offset, const char *key, const char *value) {
    if (!value) return true; // 跳过空值

    // 检查输入长度，防止整数溢出
    size_t key_len = strlen(key);
    size_t value_len = strlen(value);

    if (key_len > 1024 || value_len > 4096) {
        LOG("JSON字段过长");
        return false;
    }

    // 计算需要的空间（包括转义字符），防止溢出
    size_t needed = key_len + value_len * 2 + 10; // 预留转义空间

    // 检查计算是否溢出
    if (needed < key_len || needed < value_len) {
        LOG("长度计算溢出");
        return false;
    }

    // 检查是否需要扩展缓冲区
    if (*offset + needed >= *capacity) {
        size_t new_capacity = *capacity * 2;

        // 检查新容量是否足够
        if (new_capacity < *offset + needed) {
            new_capacity = *offset + needed + 1024;
        }

        char *new_str = realloc(*json_str, new_capacity);
        if (!new_str) {
            LOG("内存重新分配失败");
            return false;
        }
        *json_str = new_str;
        *capacity = new_capacity;
    }

    // 添加字段 - 确保有足够的剩余空间
    if (*offset >= *capacity) {
        LOG("缓冲区偏移量异常");
        return false;
    }

    size_t remaining = *capacity - *offset;
    int written = snprintf(*json_str + *offset, remaining, "\"%s\":\"%s\",", key, value);
    if (written < 0 || (size_t)written >= remaining) {
        LOG("JSON字段写入失败");
        return false; // 写入失败
    }

    *offset += written;
    return true;
}

// 按钮点击处理函数
bool handleButtonClick(const ButtonClickParams *params) {
    if (!params || !params->buttonId || !params->windowId || !params->serialNumber) {
        LOG("参数验证失败");
        return false;
    }

    // 检查必需参数的长度，防止过长的输入
    if (strlen(params->buttonId) > 256 ||
        strlen(params->windowId) > 256 ||
        strlen(params->serialNumber) > 256) {
        LOG("必需参数过长");
        return false;
    }

    // 检查可选参数的长度
    if ((params->keyword && strlen(params->keyword) > 512) ||
        (params->wareId && strlen(params->wareId) > 256) ||
        (params->buyCount && strlen(params->buyCount) > 64)) {
        LOG("可选参数过长");
        return false;
    }

    // 初始化动态缓冲区
    size_t capacity = 1024;
    size_t offset = 0;
    char *json_string = malloc(capacity);
    if (!json_string) {
        LOG("内存分配失败");
        return false;
    }

    // 开始JSON对象 - 确保缓冲区足够大
    if (capacity < 2) {
        LOG("初始缓冲区太小");
        free(json_string);
        return false;
    }
    strcpy(json_string, "{");
    offset = 1;

    // 添加必需字段
    if (!append_json_field(&json_string, &capacity, &offset, "buttonId", params->buttonId) ||
        !append_json_field(&json_string, &capacity, &offset, "windowId", params->windowId) ||
        !append_json_field(&json_string, &capacity, &offset, "serialNumber", params->serialNumber)) {
        LOG("JSON构建失败");
        free(json_string);
        return false;
    }

    // 添加可选字段
    append_json_field(&json_string, &capacity, &offset, "keyword", params->keyword);
    append_json_field(&json_string, &capacity, &offset, "wareId", params->wareId);
    append_json_field(&json_string, &capacity, &offset, "buyCount", params->buyCount);

    // 移除最后的逗号并关闭JSON对象
    if (offset > 1 && offset <= capacity && json_string[offset - 1] == ',') {
        // 确保有足够空间设置结束符
        if (offset < capacity) {
            json_string[offset - 1] = '}';
            json_string[offset] = '\0';
        } else {
            LOG("JSON结束符空间不足");
            free(json_string);
            return false;
        }
    } else {
        if (offset + 1 < capacity) {
            json_string[offset] = '}';
            json_string[offset + 1] = '\0';
        } else {
            LOG("JSON结束符添加失败");
            free(json_string);
            return false;
        }
    }

    // 记录构建的JSON
    LOG("发送JSON: %s", json_string);

    EncryptRequestData encrypted_data = encrypt_request_data(json_string);

    if (!encrypted_data.status) {
        LOG("数据加密失败");
        free(json_string);
        return false;
    }
    // 将encrypted_data.data， encrypted_data.key，encrypted_data.sign 拼接成json字符串
    // 安全地计算所需大小，防止整数溢出
    size_t data_len = strlen(encrypted_data.data);
    size_t key_len = strlen(encrypted_data.key);
    size_t sign_len = strlen(encrypted_data.sign);

    // 检查单个字段长度
    if (data_len > 16384 || key_len > 1024 || sign_len > 1024) {
        LOG("加密数据字段过长");
        free(json_string);
        free_encrypt_request_data(&encrypted_data);
        return false;
    }

    // 安全地计算总大小
    size_t encrypted_data_size = data_len + key_len + sign_len + 100;

    // 检查是否溢出
    if (encrypted_data_size < data_len || encrypted_data_size < key_len || encrypted_data_size < sign_len) {
        LOG("加密数据大小计算溢出");
        free(json_string);
        free_encrypt_request_data(&encrypted_data);
        return false;
    }

    char* encrypted_data_json = (char*)malloc(encrypted_data_size);
    if (!encrypted_data_json) {
        LOG("加密数据JSON内存分配失败");
        free(json_string);
        free_encrypt_request_data(&encrypted_data);
        return false;
    }
    int written = snprintf(encrypted_data_json, encrypted_data_size, "{\"data\":\"%s\",\"key\":\"%s\",\"sign\":\"%s\"}", encrypted_data.data, encrypted_data.key, encrypted_data.sign);
    if (written < 0 || (size_t)written >= encrypted_data_size) {
        LOG("加密数据JSON格式化失败");
        free(json_string);
        free(encrypted_data_json);
        free_encrypt_request_data(&encrypted_data);
        return false;
    }

    LOG("请求数据: %s", encrypted_data_json);

    // 释放原始JSON字符串
    free(json_string);
    // // 释放加密数据
    free_encrypt_request_data(&encrypted_data);

    // 设置请求头
    const char* headers = "Content-Type: application/json";

    // 发送请求
    char* response = NULL;
    int statusCode = 0;
    // http://127.0.0.1:48237/command
    const uint8_t curlKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x67, 0x25, 0x68, 0xDF, 0x79, 0x06, 0x07, 0x6E, 0x88, 0x49, 0x19, 0xB3, 0x3C, 0xA3, 0x3C, 0xA5, 0x23, 0xC1, 0x08, 0xFC, 0xE4, 0xB2, 0x64, 0x2E, 0x41, 0x21, 0x92, 0x10, 0x7D, 0x51, 0x72, 0xC2, 0x48, 0x38 };
    char *com_url = decrypt_string_v2(curlKey, sizeof(curlKey));
    if (!com_url) {
        LOG("URL解密失败");
        free(encrypted_data_json);  // 修复内存泄漏
        return false;
    }
    bool success = socket_post_request_sync(com_url, encrypted_data_json, headers, &response, &statusCode);
    free(com_url);
    // 释放原始JSON字符串
    free(encrypted_data_json);
    // 清理JSON字符串（不再需要）

    if (!success) {
        LOG("HTTP请求失败: %s", response ? response : "未知错误");
        if (response) free(response);
        return false;
    }

    LOG("HTTP状态码: %d", statusCode);
    LOG("响应内容: %s", response ? response : "空响应");
    
    // 解密响应内容
    // cJSON* json = JSONUtils_ParseString(response);
    // if (json == NULL) {
    //     if (response) free(response);
    //     return false;
    // }

    // const char* data = JSONUtils_GetStringValue(json, "data");
    // const char* key = JSONUtils_GetStringValue(json, "key");
    // const char* sign = JSONUtils_GetStringValue(json, "sign");
    // if (data == NULL || key == NULL || sign == NULL) {
    //     cJSON_Delete(json);
    //     if (response) free(response);
    //     return false;
    // }
    
    // DecryptResult decryptResult = decrypt_request_data(data, key, sign);
    // if (!decryptResult.status) {
    //     cJSON_Delete(json);
    //     if (response) free(response);
    //     return false;
    // }

    // cJSON* requestData = decryptResult.data;
    // const char* message = JSONUtils_GetStringValue(requestData, "message");
    // if (!message) {
    //     cJSON_Delete(json);
    //     if (response) free(response);
    //     free_decrypt_result(&decryptResult);
    //     return false;
    // }
    // bool status = JSONUtils_GetBoolValue(requestData, "status", false);
    // if (!status) {
    //     cJSON_Delete(json);
    //     if (response) free(response);
    //     free_decrypt_result(&decryptResult);
    //     return false;
    // }

    // LOG("响应数据: %s", message);
    // LOG("响应状态: %s", status ? "成功" : "失败");

    // if (response) free(response);
    // cJSON_Delete(json);
    // free_decrypt_result(&decryptResult);

    if (response) free(response);

    // 只判断返回值等于200的情况，才为成功的情况
    return (statusCode == 200);
}
