//  LoginManager.m
#import "Login.h"
#import "FloatingView.h"

@implementation Login

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"windowId": @"login", // 🎯 添加窗口唯一ID
        @"buttonText": @"登",
        @"color": color,
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"短信登录(美)", kButtonIdKey: @"loginBySMSCode1", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"短信登录(港)", kButtonIdKey: @"loginBySMSCode2", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"账密登录", kButtonIdKey: @"loginByPWD", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"获取验证码", kButtonIdKey: @"getSMSCode", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"输入验证码", kButtonIdKey: @"enterSMSCode", kButtonAutoCollapseKey: @(NO)},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
}

@end
