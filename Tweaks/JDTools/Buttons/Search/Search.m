//  Search.m
#import "Search.h"
#import "FloatingView.h"
#import "Toast.h"
#import "Log.h"
#import "JDToolsService.h"

// 使用字典存储每个窗口的搜索配置，key为windowId
static NSMutableDictionary *windowSearchConfigs = nil;

// 记录上一次点击的关键词信息
static NSDictionary *lastClickedKeywordInfo = nil;

@implementation Search

// 初始化配置字典
+ (void)initialize {
    if (self == [Search class]) {
        windowSearchConfigs = [NSMutableDictionary dictionary];
    }
}

// 获取悬浮窗配置 - 根据searchConfig动态创建按钮（兼容旧版本）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY searchConfig:(NSArray *)searchConfigParam {
    return [self initWithConfig:color startY:startY searchConfig:searchConfigParam windowTitle:@"搜索"];
}

// 获取悬浮窗配置 - 支持自定义窗口标题
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY searchConfig:(NSArray *)searchConfigParam windowTitle:(NSString *)windowTitle {
    // 生成唯一的窗口ID（基于标题）
    // search_窗口标题
    NSString *uniqueWindowId = [NSString stringWithFormat:@"search_%@", [windowTitle stringByReplacingOccurrencesOfString:@" " withString:@"_"]];

    // 为这个窗口保存搜索配置
    if (searchConfigParam) {
        windowSearchConfigs[uniqueWindowId] = searchConfigParam;
    }

    // 创建动态按钮配置数组
    NSMutableArray *buttonConfigs = [NSMutableArray array];

    // 如果有搜索配置，循环添加关键词按钮
    if (searchConfigParam && searchConfigParam.count > 0) {
        // 处理新的数据格式
        for (NSInteger i = 0; i < searchConfigParam.count; i++) {
            NSDictionary *taskItem = searchConfigParam[i];

            // 获取params数组
            NSArray *params = taskItem[@"keywords"];
            if (params && [params isKindOfClass:[NSArray class]]) {
                // 遍历params数组，为每个keyword创建按钮
                for (NSDictionary *paramItem in params) {
                    NSString *keyword = paramItem[@"keyword"];
                    NSString *skuId = paramItem[@"skuId"];
                    NSString *buttonId = [[keyword stringByAppendingString:@"_"] stringByAppendingString:skuId];

                    if (keyword && keyword.length > 0) {
                        // 将关键词作为按钮名称和ID
                        NSDictionary *keywordButton = @{
                            kButtonTitleKey: keyword,
                            kButtonIdKey: buttonId, // 使用关键词 拼接 skuId作为按钮ID
                            kButtonAutoCollapseKey: @(NO)
                        };
                        [buttonConfigs addObject:keywordButton];
                    }
                }
            }
        }

        // 当关键词大于0的时候才有 进入目标 的按钮
        if (buttonConfigs.count > 0) {
            [buttonConfigs addObject:@{kButtonTitleKey: @"进入目标商品", kButtonIdKey: @"target", kButtonAutoCollapseKey: @(NO)}];
        }
    }

    // 添加固定的功能按钮
    // [buttonConfigs addObjectsFromArray:@[
    //     @{kButtonTitleKey: @"随机进入", kButtonIdKey: @"random", kButtonAutoCollapseKey: @(YES)},
    //     @{kButtonTitleKey: @"进入目标商品", kButtonIdKey: @"target", kButtonAutoCollapseKey: @(YES)},
    //     // @{kButtonTitleKey: @"返回", kButtonIdKey: @"back", kButtonAutoCollapseKey: @YES},
    // ]];

    // 固定按钮：随机进入
    [buttonConfigs addObject:@{kButtonTitleKey: @"随机进入", kButtonIdKey: @"random", kButtonAutoCollapseKey: @(YES)}];
    [buttonConfigs addObject:@{kButtonTitleKey: @"删除任务", kButtonIdKey: @"delete", kButtonAutoCollapseKey: @(YES)}];

    // 使用传入的窗口标题，如果没有则使用默认值
    NSString *displayTitle = windowTitle ?: @"搜";

    // 确保 uniqueWindowId 被 block 正确捕获
    NSString *capturedWindowId = [uniqueWindowId copy];

    return @{
        @"buttonText": displayTitle,
        @"windowId": capturedWindowId, // 🎯 使用已生成的唯一窗口ID
        @"color": color ?: [UIColor redColor],
        @"position": @"left",
        @"startY": @(startY),
        @"buttonConfigs": [buttonConfigs copy],
        @"actionBlock": ^(NSString *buttonId) {
            // 传递窗口ID给执行方法
            [self executeCommandWithWindowId:capturedWindowId buttonId:buttonId];
        }
    };

}

// 新的执行方法，支持指定窗口ID
+ (BOOL)executeCommandWithWindowId:(NSString *)windowId buttonId:(NSString *)buttonId {
    time_t now = time(NULL);
    if (now >= expireTime) {
        LOG("❌❌❌❌❌❌❌------->已到期");
        [Toast show:@"服务已到期" duration:2.0];
        return NO;
    }

    // 删除当前任务
    if ([buttonId isEqualToString:@"delete"]) {
        // 从全局配置中删除当前窗口的配置
        [windowSearchConfigs removeObjectForKey:windowId];
        // 从悬浮窗中删除当前窗口
        BOOL success = [FloatingView removeWindowWithId:windowId];
        NSString *error = success ? @"删除成功" : @"删除失败";
        [Toast show:error duration:2.0];
        return YES;
    }


    // 处理返回按钮
    if ([buttonId isEqualToString:@"reset"]) {
        [FloatingView resetAllWindowsToInitialPositions];
        return YES;
    }

    // 处理随机按钮
    if ([buttonId isEqualToString:@"random"]) {
        // 直接调用全局处理函数
        ButtonClickParams params = {0};
        params.buttonId = [buttonId UTF8String];
        params.windowId = [windowId UTF8String]; // 使用传入的窗口ID
        params.serialNumber = serialNumber;
        return executeButtonClick(&params);
    }

    // 处理目标按钮
    if ([buttonId isEqualToString:@"target"]) {
        if (!lastClickedKeywordInfo) {
            [Toast show:@"请先执行搜索" duration:1.0];
            return NO;
        }

        NSString *keyword = lastClickedKeywordInfo[@"keyword"];
        NSString *wareId = lastClickedKeywordInfo[@"wareId"];
        NSString *buyCount = lastClickedKeywordInfo[@"buyCount"];

        NSLOG(@"执行跳转-----> 点击关键词:%@, 商品id:%@, 购买数量:%@", keyword, wareId, buyCount);

        // 调用全局处理函数，传递完整参数
        ButtonClickParams params = {0};
        params.buttonId = [buttonId UTF8String];
        params.windowId = [windowId UTF8String]; // 使用传入的窗口ID
        params.serialNumber = serialNumber;
        params.keyword = [keyword UTF8String];
        params.wareId = [wareId UTF8String];
        params.buyCount = [buyCount UTF8String];
        return executeButtonClick(&params);
    }

    // 处理关键词按钮
    // 找到当前按钮(关键词)的配置信息，使用指定窗口的配置
    NSDictionary *item = [self getCurrentKeywordInfo:windowId buttonId:buttonId];
    if (!item) {
        [Toast show:[NSString stringWithFormat:@"关键词匹配失败: %@ (窗口: %@)", buttonId, windowId] duration:2.0];
        return NO;
    }
    // 找到关键词信息要做记录
    NSString *keyword = item[@"keyword"];
    NSString *wareId = item[@"wareId"];
    NSString *buyCount = item[@"buyCount"];

    // 保存完整的关键词信息（包括skuId和bugCount）
    lastClickedKeywordInfo = [item copy];

    NSLOG(@"关键词: %@, 商品id: %@, 数量: %@", keyword, wareId, buyCount);

    // 调用全局处理函数，传递完整参数
    ButtonClickParams params = {0};
    params.buttonId = [buttonId UTF8String];
    params.windowId = [windowId UTF8String]; // 使用传入的窗口ID
    params.serialNumber = serialNumber;
    params.keyword = [keyword UTF8String];
    params.wareId = [wareId UTF8String];
    params.buyCount = [buyCount UTF8String];
    return executeButtonClick(&params);

}

// 根据关键词和窗口ID获取搜索配置信息
+ (NSDictionary *)getCurrentKeywordInfo:(NSString *)windowId buttonId:(NSString *)buttonId {
    if (!buttonId || buttonId.length == 0) {
        NSLOG(@"[Search] 错误：关键词为空");
        return nil;
    }

    // 获取指定窗口的搜索配置
    NSArray *searchConfig = windowSearchConfigs[windowId];
    if (!searchConfig) {
        NSLOG(@"[Search] 错误：窗口 %@ 的搜索配置不存在，可用窗口: %@", windowId, [windowSearchConfigs.allKeys componentsJoinedByString:@", "]);
        return nil;
    }

    NSLOG(@"[Search] 在窗口 %@ 中查找关键词: %@，配置数量: %lu", windowId, buttonId, (unsigned long)[searchConfig count]);

    // 处理新的数据格式：遍历taskItem中的params数组
    for (NSDictionary *taskItem in searchConfig) {
        NSArray *params = taskItem[@"keywords"];
        if (params && [params isKindOfClass:[NSArray class]]) {
            for (NSDictionary *paramItem in params) {
                NSString *itemKeyword = paramItem[@"keyword"];
                NSString *skuId = paramItem[@"skuId"];
                NSString *itemButtonId = [[itemKeyword stringByAppendingString:@"_"] stringByAppendingString:skuId];

                if ([itemButtonId isEqualToString:buttonId]) {
                    NSLOG(@"[Search] 找到关键词 %@ 的配置信息", buttonId);
                    // 返回找到的参数项，包含keyword、skuId、bugCount
                    return @{
                        @"keyword": paramItem[@"keyword"] ?: @"",
                        @"wareId": paramItem[@"skuId"] ?: @"",  // skuId映射为wareId
                        @"buyCount": paramItem[@"bugCount"] ?: @"1"  // bugCount映射为buyCount
                    };
                }
            }
        }
    }

    NSLOG(@"[Search] 在窗口 %@ 中未找到关键词: %@", windowId, buttonId);
    return nil;
}

// 删除指定窗口的搜索配置
+ (void)removeSearchConfigForWindowId:(NSString *)windowId {
    if (!windowId || [windowId length] == 0) {
        return;
    }

    // 删除窗口的搜索配置
    if (windowSearchConfigs[windowId]) {
        [windowSearchConfigs removeObjectForKey:windowId];
    }

}


@end

