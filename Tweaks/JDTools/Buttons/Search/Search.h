
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "BaseButtonManager.h"

@interface Search : BaseButtonManager

// 查找关键词信息
// + (NSDictionary *)getCurrentKeywordInfo:(NSString *)keyword; 

// 根据关键词和窗口ID获取搜索配置信息
+ (NSDictionary *)getCurrentKeywordInfo:(NSString *)windowId buttonId:(NSString *)buttonId;

// 支持自定义窗口标题的初始化方法
+ (NSDictionary *)initWithConfig:(UIColor *)color
                          startY:(CGFloat)startY
                    searchConfig:(NSArray *)searchConfigParam
                     windowTitle:(NSString *)windowTitle;

// 支持指定窗口ID的执行方法
+ (BOOL)executeCommandWithWindowId:(NSString *)windowId buttonId:(NSString *)buttonId;

// 删除指定窗口的搜索配置≤
+ (void)removeSearchConfigForWindowId:(NSString *)windowId;

// 调试方法：打印所有窗口配置
// + (void)debugPrintAllConfigs;

@end