/**
 * @file Multi_Window_Keyword_Test.m
 * @brief 多窗口关键词点击测试
 * 
 * 验证多个搜索窗口的关键词点击和目标商品按钮功能
 */

#import "Search.h"
#import "ToastManager.h"

/**
 * @brief 模拟多窗口关键词点击测试
 */
void test_multi_window_keyword_clicks() {
    NSLog(@"=== 多窗口关键词点击测试 ===");
    
    // 模拟两个搜索窗口的配置
    NSArray *window1Config = @[
        @{
            @"buttonText": @"搜1",
            @"params": @[
                @{@"keyword": @"女鞋", @"skuId": @"111111", @"bugCount": @"1"},
                @{@"keyword": @"凉鞋", @"skuId": @"222222", @"bugCount": @"2"},
                @{@"keyword": @"高跟鞋", @"skuId": @"333333", @"bugCount": @"3"}
            ]
        }
    ];
    
    NSArray *window2Config = @[
        @{
            @"buttonText": @"手",
            @"params": @[
                @{@"keyword": @"手机", @"skuId": @"444444", @"bugCount": @"4"},
                @{@"keyword": @"三星手机", @"skuId": @"555555", @"bugCount": @"5"},
                @{@"keyword": @"苹果手机", @"skuId": @"666666", @"bugCount": @"6"}
            ]
        }
    ];
    
    // 初始化搜索配置
    NSString *window1Id = @"search_搜1";
    NSString *window2Id = @"search_手";
    
    // 模拟初始化窗口配置
    [Search initWithConfig:[UIColor redColor] startY:150 searchConfig:window1Config windowTitle:@"搜1"];
    [Search initWithConfig:[UIColor blueColor] startY:250 searchConfig:window2Config windowTitle:@"手"];
    
    NSLog(@"已创建两个搜索窗口:");
    NSLog(@"  - 窗口1 ID: %@", window1Id);
    NSLog(@"  - 窗口2 ID: %@", window2Id);
    
    // 模拟按顺序点击关键词
    NSLog(@"\n1. 模拟点击窗口1的'女鞋'关键词");
    [Search executeCommandWithButtonId:@"女鞋" windowId:window1Id];
    
    NSLog(@"\n2. 模拟点击窗口2的'手机'关键词");
    [Search executeCommandWithButtonId:@"手机" windowId:window2Id];
    
    NSLog(@"\n3. 模拟点击窗口1的'凉鞋'关键词");
    [Search executeCommandWithButtonId:@"凉鞋" windowId:window1Id];
    
    // 模拟点击目标商品按钮
    NSLog(@"\n4. 模拟点击窗口1的'进入目标商品'按钮");
    NSDictionary *window1Item = [Search getCurrentKeywordInfo:@"凉鞋" windowId:window1Id];
    NSLog(@"   窗口1当前关键词信息: %@", window1Item);
    NSLog(@"   期望使用的skuId: %@", window1Item[@"wareId"]);
    
    NSLog(@"\n5. 模拟点击窗口2的'进入目标商品'按钮");
    NSDictionary *window2Item = [Search getCurrentKeywordInfo:@"手机" windowId:window2Id];
    NSLog(@"   窗口2当前关键词信息: %@", window2Item);
    NSLog(@"   期望使用的skuId: %@", window2Item[@"wareId"]);
    
    NSLog(@"\n6. 再次模拟点击窗口1的'进入目标商品'按钮");
    window1Item = [Search getCurrentKeywordInfo:@"凉鞋" windowId:window1Id];
    NSLog(@"   窗口1当前关键词信息: %@", window1Item);
    NSLog(@"   期望使用的skuId: %@", window1Item[@"wareId"]);
    
    // 验证修复效果
    NSLog(@"\n=== 验证修复效果 ===");
    NSLog(@"修复前的问题: 点击窗口1的'进入目标商品'按钮时，会使用窗口2的'手机'关键词信息");
    NSLog(@"修复后的行为: 每个窗口独立记录最后点击的关键词，不会互相干扰");
    
    // 打印当前记录的关键词
    NSLog(@"\n当前各窗口记录的关键词:");
    NSLog(@"  - 窗口1 (%@): %@", window1Id, [Search debugGetLastClickedKeywordForWindow:window1Id]);
    NSLog(@"  - 窗口2 (%@): %@", window2Id, [Search debugGetLastClickedKeywordForWindow:window2Id]);
}

/**
 * @brief 测试边界情况
 */
void test_edge_cases() {
    NSLog(@"\n=== 测试边界情况 ===");
    
    NSString *window1Id = @"search_搜1";
    NSString *window2Id = @"search_手";
    NSString *nonExistWindowId = @"search_不存在";
    
    // 测试未点击关键词时点击目标商品按钮
    NSLog(@"1. 测试未点击关键词时点击目标商品按钮");
    NSString *newWindowId = @"search_新窗口";
    
    // 创建新窗口但不点击任何关键词
    NSArray *newWindowConfig = @[
        @{
            @"buttonText": @"新窗口",
            @"params": @[
                @{@"keyword": @"测试", @"skuId": @"999999", @"bugCount": @"1"}
            ]
        }
    ];
    [Search initWithConfig:[UIColor greenColor] startY:350 searchConfig:newWindowConfig windowTitle:@"新窗口"];
    
    // 直接点击目标商品按钮
    NSLog(@"   点击新窗口的'进入目标商品'按钮 (应该提示先执行搜索)");
    BOOL result = [Search executeCommandWithButtonId:@"target" windowId:newWindowId];
    NSLog(@"   执行结果: %@", result ? @"成功" : @"失败 (预期行为)");
    
    // 测试删除窗口后的关键词记录
    NSLog(@"\n2. 测试删除窗口后的关键词记录");
    NSLog(@"   删除窗口前的记录:");
    NSLog(@"   - 窗口1: %@", [Search debugGetLastClickedKeywordForWindow:window1Id]);
    NSLog(@"   - 窗口2: %@", [Search debugGetLastClickedKeywordForWindow:window2Id]);
    
    NSLog(@"   模拟删除窗口1");
    [Search removeSearchConfigForWindowId:window1Id];
    
    NSLog(@"   删除后的记录:");
    NSLog(@"   - 窗口1: %@", [Search debugGetLastClickedKeywordForWindow:window1Id]);
    NSLog(@"   - 窗口2: %@", [Search debugGetLastClickedKeywordForWindow:window2Id]);
    
    // 测试不存在的窗口ID
    NSLog(@"\n3. 测试不存在的窗口ID");
    NSLog(@"   尝试获取不存在窗口的关键词信息");
    NSDictionary *nonExistItem = [Search getCurrentKeywordInfo:@"测试" windowId:nonExistWindowId];
    NSLog(@"   结果: %@", nonExistItem ? nonExistItem : @"nil (预期行为)");
}

/**
 * @brief 测试多次点击同一窗口的不同关键词
 */
void test_multiple_clicks_same_window() {
    NSLog(@"\n=== 测试多次点击同一窗口的不同关键词 ===");
    
    // 创建测试窗口
    NSString *windowId = @"search_测试";
    NSArray *windowConfig = @[
        @{
            @"buttonText": @"测试",
            @"params": @[
                @{@"keyword": @"关键词1", @"skuId": @"111", @"bugCount": @"1"},
                @{@"keyword": @"关键词2", @"skuId": @"222", @"bugCount": @"2"},
                @{@"keyword": @"关键词3", @"skuId": @"333", @"bugCount": @"3"}
            ]
        }
    ];
    
    [Search initWithConfig:[UIColor yellowColor] startY:450 searchConfig:windowConfig windowTitle:@"测试"];
    
    // 依次点击三个关键词
    NSLog(@"1. 点击'关键词1'");
    [Search executeCommandWithButtonId:@"关键词1" windowId:windowId];
    NSLog(@"   当前记录: %@", [Search debugGetLastClickedKeywordForWindow:windowId]);
    
    NSLog(@"\n2. 点击'关键词2'");
    [Search executeCommandWithButtonId:@"关键词2" windowId:windowId];
    NSLog(@"   当前记录: %@", [Search debugGetLastClickedKeywordForWindow:windowId]);
    
    NSLog(@"\n3. 点击'关键词3'");
    [Search executeCommandWithButtonId:@"关键词3" windowId:windowId];
    NSLog(@"   当前记录: %@", [Search debugGetLastClickedKeywordForWindow:windowId]);
    
    // 点击目标商品按钮
    NSLog(@"\n4. 点击'进入目标商品'按钮");
    NSDictionary *item = [Search getCurrentKeywordInfo:[Search debugGetLastClickedKeywordForWindow:windowId] windowId:windowId];
    NSLog(@"   使用的关键词信息: %@", item);
    NSLog(@"   skuId: %@", item[@"wareId"]);
    
    // 验证是否使用最后点击的关键词
    BOOL isCorrect = [item[@"wareId"] isEqualToString:@"333"];
    NSLog(@"\n验证结果: %@", isCorrect ? @"✅ 正确使用最后点击的关键词" : @"❌ 使用了错误的关键词");
}

/**
 * @brief 主测试函数
 */
int main(int argc, const char * argv[]) {
    @autoreleasepool {
        NSLog(@"多窗口关键词点击测试开始");
        NSLog(@"========================");
        
        // 添加调试方法
        [Search addDebugMethods];
        
        test_multi_window_keyword_clicks();
        test_edge_cases();
        test_multiple_clicks_same_window();
        
        NSLog(@"========================");
        NSLog(@"测试完成");
    }
    return 0;
}

/**
 * @brief 修复总结
 * 
 * 修复前的问题：
 * - 使用全局静态变量记录最后点击的关键词和窗口ID
 * - 不同窗口的点击会互相覆盖记录
 * - 点击"进入目标商品"按钮时可能使用错误窗口的关键词信息
 * 
 * 修复后的行为：
 * - 使用字典为每个窗口独立记录最后点击的关键词
 * - 不同窗口的点击不会互相干扰
 * - 点击"进入目标商品"按钮时始终使用当前窗口的最后点击关键词
 * 
 * 关键改动：
 * 1. 将全局静态变量改为字典，key为windowId
 * 2. 点击关键词时，只更新当前窗口的记录
 * 3. 点击目标商品按钮时，只查找当前窗口的记录
 * 4. 删除窗口时，清理对应的记录
 */

// 为测试添加的调试方法扩展
@implementation Search (Debug)

+ (void)addDebugMethods {
    // 这个方法只是为了让编译器不警告
}

// 获取指定窗口的最后点击关键词
+ (NSString *)debugGetLastClickedKeywordForWindow:(NSString *)windowId {
    return windowLastClickedKeywords[windowId];
}

@end
