#import "Network.h"
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <dlfcn.h>
#import <notify.h>
#import "FloatingView.h"
#import "DeviceControl.h"

// @interface RadiosPreferences : NSObject
// - (BOOL)airplaneMode;
// - (void)setAirplaneMode:(BOOL)enabled;
// @end

// void toggleAirplaneMode(BOOL enable) {
//     RadiosPreferences *prefs = [[RadiosPreferences alloc] init];
//     [prefs setAirplaneMode:enable];
// }

@implementation Network

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"buttonText": @"网",
        @"windowId": @"network",
        @"color": color,
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"飞行模式-开", kButtonIdKey: @"airplaneMode-on", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"飞行模式-关", kButtonIdKey: @"airplaneMode-off", kButtonAutoCollapseKey: @(NO)},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
}


+ (void)executeCommandWithButtonId:(NSString *)buttonId {
     // 飞行模式控制
    if ([buttonId isEqualToString:@"airplaneMode-on"]) {
        [DeviceControl toggleAirplaneMode:YES];
        return;
    }
    if ([buttonId isEqualToString:@"airplaneMode-off"]) {
        [DeviceControl toggleAirplaneMode:NO];
        return;
    }
}

@end

