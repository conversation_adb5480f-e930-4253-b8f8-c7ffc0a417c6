#import "Debug.h"
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <dlfcn.h>
#import <notify.h>
#import "FloatingView.h"
#import "DeviceControl.h"

#include <objc/runtime.h>
#include <objc/message.h>
#include "DeviceControl.h"
// #include "GlobalNetworkData.h"
#include "BaseButtonManager.h"

// 测试网络请求使用
#include "JSONUtils.h"
#include "CryptoRequest.h"
#include "Log.h"

@implementation Debug

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"buttonText": @"D",
        @"windowId": @"debug",
        @"color": [UIColor redColor],
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"注销手机", kButtonIdKey: @"respring", kButtonAutoCollapseKey: @(YES)},
            // @{kButtonTitleKey: @"重启手机", kButtonIdKey: @"reboot", kButtonAutoCollapseKey: @(YES)},
            @{kButtonTitleKey: @"飞行模式(开)", kButtonIdKey: @"airplaneMode-on", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"飞行模式(关)", kButtonIdKey: @"airplaneMode-off", kButtonAutoCollapseKey: @(NO)},
            @{kButtonTitleKey: @"佐罗", kButtonIdKey: @"zorro", kButtonAutoCollapseKey: @(YES)},
            // @{kButtonTitleKey: @"京东", kButtonIdKey: @"jd", kButtonAutoCollapseKey: @(YES)},
            @{kButtonTitleKey: @"复位", kButtonIdKey: @"reset", kButtonAutoCollapseKey: @(YES)},
            // @{kButtonTitleKey: @"地址", kButtonIdKey: @"address", kButtonAutoCollapseKey: @(NO)},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
}


+ (void)executeCommandWithButtonId:(NSString *)buttonId {
 
    if ([buttonId isEqualToString:@"respring"]) {
        [DeviceControl respring];
        return;
    }
    // if ([buttonId isEqualToString:@"reboot"]) {
    //     [DeviceControl reboot];
    //     return;
    // }

    if ([buttonId isEqualToString:@"airplaneMode-on"]) {
        [DeviceControl toggleAirplaneMode:YES];
        return;
    }
    if ([buttonId isEqualToString:@"airplaneMode-off"]) {
        [DeviceControl toggleAirplaneMode:NO];
        return;
    }

    if ([buttonId isEqualToString:@"zorro"]) {
        openAppWithURL("zorro://");
        return;
    }
    if ([buttonId isEqualToString:@"jd"]) {
        openAppWithURL("openapp.jdmobile://");
        return;
    }
    if ([buttonId isEqualToString:@"reset"]) {
        [FloatingView resetAllWindowsToInitialPositions];
        return;
    }

}

@end

