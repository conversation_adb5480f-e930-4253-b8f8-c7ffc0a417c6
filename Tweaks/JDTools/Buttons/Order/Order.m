//  Order.m

#import "Order.h"
#import "FloatingView.h"

@implementation Order

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"buttonText": @"收",
        @"windowId": @"order", 
        @"color": color,
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"待收货列表", kButtonIdKey: @"wait4Delivery", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"一键收货", kButtonIdKey: @"confirmOrder", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"评价中心", kButtonIdKey: @"commentCenter", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"指定评语", kButtonIdKey: @"specialComment", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"通用评语", kButtonIdKey: @"generalComment", kButtonAutoCollapseKey: @YES},
            // @{kButtonTitleKey: @"返回", kButtonIdKey: @"back", kButtonAutoCollapseKey: @YES},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
}

@end