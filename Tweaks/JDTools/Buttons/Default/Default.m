#import "Default.h"
#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <dlfcn.h>
#import <notify.h>
#import "FloatingView.h"

#include <objc/runtime.h>
#include <objc/message.h>
#include "DeviceControl.h"
#include "BaseButtonManager.h"

@implementation Default

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {
    return @{
        @"buttonText": @"D",
        @"windowId": @"default",
        @"color": [UIColor redColor],
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"注销手机", kButtonIdKey: @"respring", kButtonAutoCollapseKey: @(YES)},
            @{kButtonTitleKey: @"重启手机", kButtonIdKey: @"reboot ", kButtonAutoCollapseKey: @(YES)},
        ],
        @"actionBlock": ^(NSString *buttonId) {
            [self executeCommandWithButtonId:buttonId];
        }
    };
}


+ (void)executeCommandWithButtonId:(NSString *)buttonId {
 
    if ([buttonId isEqualToString:@"respring"]) {
        [DeviceControl respring];
        return;
    }
    if ([buttonId isEqualToString:@"reboot"]) {
        [DeviceControl reboot];
        return;
    }
}

@end

