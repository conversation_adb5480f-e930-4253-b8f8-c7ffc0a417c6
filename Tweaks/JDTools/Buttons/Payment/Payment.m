//  Payment.m
#import "Payment.h"
#import "FloatingView.h"

@implementation Payment

// 获取完整的悬浮窗配置（包含动态参数）
+ (NSDictionary *)initWithConfig:(UIColor *)color startY:(CGFloat)startY {

    // 先构建基础配置
    NSDictionary *baseConfig = @{
        @"buttonText": @"付",
        @"windowId": @"payment",
        @"color": color,
        @"position": @"right",
        @"startY": @(startY),
        @"buttonConfigs": @[
            @{kButtonTitleKey: @"云闪付代付", kButtonIdKey: @"platUnionPay", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"QQ代付", kButtonIdKey: @"platQQWalletPay", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"微信代付", kButtonIdKey: @"platDFPay", kButtonAutoCollapseKey: @YES},
            @{kButtonTitleKey: @"微信支付", kButtonIdKey: @"platWXPay", kButtonAutoCollapseKey: @YES},
            // @{kButtonTitleKey: @"返回", kButtonIdKey: @"back", kButtonAutoCollapseKey: @YES},
        ]
    };

    // 自动添加actionBlock（从windowId自动提取）
    return [self addActionBlockToConfig:baseConfig];
}

@end
