#include <objc/runtime.h>
#include <objc/message.h>
#include "Log.h"
#include <time.h>
#include <dispatch/dispatch.h>
#include <unistd.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <objc/NSObjCRuntime.h>
#include "Controller.h"
#include "SMSCode.h"
#include "CryptoRequest.h"
#include "JSONUtils.h"
#include "/Users/<USER>/Documents/Main/Tweak/Tweaks/JDTools/productInfo.h"

typedef struct {
    char *phoneNumber;
    char *url;
    char *username;
    char *password;
} ApiConfig;

// 解析格式类型
typedef enum {
    FORMAT_FOUR_DASH,    // xxx----xxxx----xxx----xxx
    FORMAT_TWO_DASH,     // xxx----xxxx
    FORMAT_PIPE,         // xxx|xxxxx
    FORMAT_UNKNOWN
} ApiFormat;

// 去掉首尾空白
static char *trim(const char *str) {
    while (isspace(*str)) str++;
    const char *end = str + strlen(str) - 1;
    while (end > str && isspace(*end)) end--;
    size_t len = end - str + 1;
    char *out = malloc(len + 1);
    if (!out) return NULL;
    strncpy(out, str, len);
    out[len] = '\0';
    return out;
}

// 释放 ApiConfig
void free_api_config(ApiConfig *api) {
    if (!api) return;
    free(api->phoneNumber);
    free(api->url);
    free(api->username);
    free(api->password);
    free(api);
}

// 写入 ApiConfig 到文件 - 支持多种格式
bool write_api_config_to_file(const char* phoneNumber, const char* url, const char* username, const char* password) {
    const char *home = getenv("HOME");
    if (!home) {
        LOG("[API] 获取HOME失败");
        return false;
    }

    char path[1024];
    snprintf(path, sizeof(path), "%s/Documents/api.txt", home);
    LOG("[API] 写入文件路径: %s", path);

    FILE *fp = fopen(path, "w");
    if (!fp) {
        LOG("[API] 无法创建或打开文件: %s", path);
        return false;
    }

    // 检查必需参数（手机号和URL必须存在）
    if (!phoneNumber || !url) {
        LOG("[API] 必需参数不能为空：手机号和URL");
        fclose(fp);
        return false;
    }

    // 根据提供的参数决定写入格式
    if (username && strlen(username) > 0 && password && strlen(password) > 0) {
        // 四个字段都有，使用四段----格式
        fprintf(fp, "%s----%s----%s----%s", phoneNumber, url, username, password);
        LOG("[API] 使用四段----格式写入");
    } else if (username && strlen(username) > 0) {
        // 只有用户名，使用|分隔格式
        fprintf(fp, "%s|%s|%s", phoneNumber, url, username);
        LOG("[API] 使用|分隔格式写入（3个字段）");
    } else {
        // 只有手机号和URL，使用两段----格式
        fprintf(fp, "%s----%s", phoneNumber, url);
        LOG("[API] 使用两段----格式写入");
    }

    fclose(fp);
    LOG("[API] 配置写入成功");
    return true;
}

// 新增：写入指定格式的配置文件
bool write_api_config_with_format(const char* phoneNumber, const char* url, const char* username, const char* password, ApiFormat format) {
    const char *home = getenv("HOME");
    if (!home) {
        LOG("[API] 获取HOME失败");
        return false;
    }

    char path[1024];
    snprintf(path, sizeof(path), "%s/Documents/api.txt", home);
    LOG("[API] 写入文件路径: %s", path);

    FILE *fp = fopen(path, "w");
    if (!fp) {
        LOG("[API] 无法创建或打开文件: %s", path);
        return false;
    }

    // 检查必需参数
    if (!phoneNumber || !url) {
        LOG("[API] 必需参数不能为空：手机号和URL");
        fclose(fp);
        return false;
    }

    switch (format) {
        case FORMAT_FOUR_DASH:
            if (!username) username = "";
            if (!password) password = "";
            fprintf(fp, "%s----%s----%s----%s", phoneNumber, url, username, password);
            LOG("[API] 强制使用四段----格式写入");
            break;

        case FORMAT_TWO_DASH:
            fprintf(fp, "%s----%s", phoneNumber, url);
            LOG("[API] 强制使用两段----格式写入");
            break;

        case FORMAT_PIPE:
            if (username && strlen(username) > 0) {
                if (password && strlen(password) > 0) {
                    fprintf(fp, "%s|%s|%s|%s", phoneNumber, url, username, password);
                } else {
                    fprintf(fp, "%s|%s|%s", phoneNumber, url, username);
                }
            } else {
                fprintf(fp, "%s|%s", phoneNumber, url);
            }
            LOG("[API] 强制使用|分隔格式写入");
            break;

        default:
            LOG("[API] 未知格式，使用默认四段----格式");
            if (!username) username = "";
            if (!password) password = "";
            fprintf(fp, "%s----%s----%s----%s", phoneNumber, url, username, password);
            break;
    }

    fclose(fp);
    LOG("[API] 配置写入成功");
    return true;
}

// 检测API配置格式
static ApiFormat detect_api_format(const char *content) {
    if (!content) return FORMAT_UNKNOWN;

    // 统计分隔符数量
    int dash_count = 0;
    int pipe_count = 0;

    const char *ptr = content;
    while (*ptr) {
        if (strncmp(ptr, "----", 4) == 0) {
            dash_count++;
            ptr += 4;
        } else if (*ptr == '|') {
            pipe_count++;
            ptr++;
        } else {
            ptr++;
        }
    }

    LOG("[API] 检测到分隔符: ---- 数量=%d, | 数量=%d", dash_count, pipe_count);

    // 根据分隔符数量判断格式
    if (dash_count == 3) {
        return FORMAT_FOUR_DASH;  // 4个字段用----分隔
    } else if (dash_count == 1) {
        return FORMAT_TWO_DASH;   // 2个字段用----分隔
    } else if (pipe_count >= 1) {
        return FORMAT_PIPE;       // 使用|分隔符
    }

    return FORMAT_UNKNOWN;
}

// 通用的token解析函数
static char *next_token_with_separator(char **src, const char *separator) {
    if (!*src) return NULL;

    char *start = *src;
    char *sep = strstr(start, separator);

    if (sep) {
        *sep = '\0';
        *src = sep + strlen(separator);  // 跳过分隔符
    } else {
        *src = NULL;  // 最后一段
    }

    return trim(start);
}

// 解析四段----格式: xxx----xxxx----xxx----xxx
static ApiConfig *parse_four_dash_format(char *content) {
    char *ptr = content;
    ApiConfig *api = malloc(sizeof(ApiConfig));
    if (!api) return NULL;

    api->phoneNumber = next_token_with_separator(&ptr, "----");
    api->url = next_token_with_separator(&ptr, "----");
    api->username = next_token_with_separator(&ptr, "----");
    api->password = next_token_with_separator(&ptr, "----");

    // 检查必需字段
    if (!api->phoneNumber || !api->url) {
        LOG("[API] 四段----格式解析失败：缺少必需字段");
        free_api_config(api);
        return NULL;
    }

    // 可选字段如果为空，设置为空字符串
    if (!api->username) api->username = strdup("");
    if (!api->password) api->password = strdup("");

    return api;
}

// 解析两段----格式: xxx----xxxx
static ApiConfig *parse_two_dash_format(char *content) {
    char *ptr = content;
    ApiConfig *api = malloc(sizeof(ApiConfig));
    if (!api) return NULL;

    api->phoneNumber = next_token_with_separator(&ptr, "----");
    api->url = next_token_with_separator(&ptr, "----");
    api->username = strdup("");  // 设置为空字符串
    api->password = strdup("");  // 设置为空字符串

    // 检查必需字段
    if (!api->phoneNumber || !api->url) {
        LOG("[API] 两段----格式解析失败：缺少必需字段");
        free_api_config(api);
        return NULL;
    }

    return api;
}

// 解析|分隔格式: xxx|xxxxx 或 xxx|xxxx|xxx|xxx
static ApiConfig *parse_pipe_format(char *content) {
    char *ptr = content;
    ApiConfig *api = malloc(sizeof(ApiConfig));
    if (!api) return NULL;

    api->phoneNumber = next_token_with_separator(&ptr, "|");
    api->url = next_token_with_separator(&ptr, "|");
    api->username = next_token_with_separator(&ptr, "|");
    api->password = next_token_with_separator(&ptr, "|");

    // 检查必需字段
    if (!api->phoneNumber || !api->url) {
        LOG("[API] |分隔格式解析失败：缺少必需字段");
        free_api_config(api);
        return NULL;
    }

    // 可选字段如果为空，设置为空字符串
    if (!api->username) api->username = strdup("");
    if (!api->password) api->password = strdup("");

    return api;
}

// 读取 ApiConfig
ApiConfig *read_api_config_from_file(void) {
    const char *home = getenv("HOME");
    if (!home) {
        LOG("[API] 获取HOME失败");
        return NULL;
    }

    char path[1024];
    snprintf(path, sizeof(path), "%s/Documents/api.txt", home);
    LOG("[API] 检查文件路径: %s", path);

    // 第一步：判断是否存在api.txt文件
    FILE *fp = fopen(path, "r");
    if (!fp) {
        LOG("[API] 文件不存在: %s", path);
        return NULL;
    }

    // 获取文件内容
    fseek(fp, 0, SEEK_END);
    long size = ftell(fp);
    rewind(fp);

    if (size <= 0) {
        LOG("[API] 文件为空");
        fclose(fp);
        return NULL;
    }

    char *content = malloc(size + 1);
    if (!content) {
        LOG("[API] 内存不足");
        fclose(fp);
        return NULL;
    }

    fread(content, 1, size, fp);
    content[size] = '\0';
    fclose(fp);

    LOG("[API] 文件内容: %s", content);

    // 第二步：根据预先添加的格式来解析字符串匹配格式，匹配成功一个即可
    ApiFormat format = detect_api_format(content);
    ApiConfig *api = NULL;

    // 创建内容副本用于解析（因为解析过程会修改字符串）
    char *content_copy = strdup(content);
    if (!content_copy) {
        LOG("[API] 内存分配失败");
        free(content);
        return NULL;
    }

    switch (format) {
        case FORMAT_FOUR_DASH:
            LOG("[API] 检测到四段----格式");
            api = parse_four_dash_format(content_copy);
            break;

        case FORMAT_TWO_DASH:
            LOG("[API] 检测到两段----格式");
            api = parse_two_dash_format(content_copy);
            break;

        case FORMAT_PIPE:
            LOG("[API] 检测到|分隔格式");
            api = parse_pipe_format(content_copy);
            break;

        default:
            LOG("[API] 未知格式，无法解析");
            break;
    }

    free(content);
    free(content_copy);

    if (!api) {
        LOG("[API] 解析失败");
        return NULL;
    }

    // 验证必需字段（第一个为手机号码，第二个为url地址）
    if (!api->phoneNumber || strlen(api->phoneNumber) == 0 ||
        !api->url || strlen(api->url) == 0) {
        LOG("[API] 必需字段缺失：手机号或URL为空");
        free_api_config(api);
        return NULL;
    }

    LOG("[API] 配置读取成功");
    LOG("[API] 手机号: %s", api->phoneNumber);
    LOG("[API] URL: %s", api->url);
    LOG("[API] 用户名: %s", api->username ? api->username : "(空)");
    LOG("[API] 密码: %s", api->password ? api->password : "(空)");

    return api;
}

// 其他方式登录
void otherLoginButton() {
    id topVC = getTopViewController();
    if (!topVC) {
        return;
    }
    const char *className = getClassName(topVC);
    if (!className) {
        return;
    }

    if (strcmp(className, "JDNewLoginViewController") != 0) {
        id topVC = getTopViewController();
        if (!topVC) {
            return;
        }
        SEL sel_otherLogin = sel_registerName("otherLoginButtonClicked");
        if (class_respondsToSelector(object_getClass(topVC), sel_otherLogin)) {
            ((void (*)(id, SEL))objc_msgSend)(topVC, sel_otherLogin);
        }
    }
    return;
}

// 切换登录方式
void changeViewIsPhoneLogin(id topVC, int isPhoneLogin) {
    // 切换登录模式: true 切换为手机号登录，false切换为账号密码登录
    SEL sel = selectorIfRespondsTo(topVC, "changeViewIsPhoneLogin:");
    if (sel) {
        ((void (*)(id, SEL, BOOL))objc_msgSend)(topVC, sel, isPhoneLogin ? YES : NO);
    }
    return;
}

// 账号密码登录
bool setUserPwdAndLogin(id topVC, const char *username, const char *password) {
    if (!topVC) {
        LOG("[setUserPwdAndLogin] topVC 为空");
        return false;
    }

    id inputView = getIvar(topVC, "_inputView");
    if (!inputView) {
        LOG("[setUserPwdAndLogin] _inputView 为空");
        return false;
    }

    id usernameField = getIvar(inputView, "_usernameField");
    if (!usernameField) {
        LOG("[setUserPwdAndLogin] _usernameField 为空");
    } else {
        id nsUsername = ((id (*)(Class, SEL, const char *))objc_msgSend)(
            objc_getClass("NSString"),
            sel_registerName("stringWithUTF8String:"),
            username
        );
        ((void (*)(id, SEL, id))objc_msgSend)(usernameField, sel_registerName("setText:"), nsUsername);
        LOG("[setUserPwdAndLogin] 已设置用户名");
    }

    id passwordField = getIvar(inputView, "_passwordField");
    if (!passwordField) {
        LOG("[setUserPwdAndLogin] _passwordField 为空");
    } else {
        id nsPassword = ((id (*)(Class, SEL, const char *))objc_msgSend)(
            objc_getClass("NSString"),
            sel_registerName("stringWithUTF8String:"),
            password
        );
        ((void (*)(id, SEL, id))objc_msgSend)(passwordField, sel_registerName("setText:"), nsPassword);
        LOG("[setUserPwdAndLogin] 已设置密码");
    }

    SEL loginSel = selectorIfRespondsTo(topVC, "loginAction1:");
    if (!loginSel) {
        LOG("[setUserPwdAndLogin] topVC 不响应 loginAction1:");
        return false;
    }

    ((void (*)(id, SEL, id))objc_msgSend)(topVC, loginSel, NULL);
    LOG("[setUserPwdAndLogin] 已调用 loginAction1:");
    return true;
}

// 手机号获取验证码登录
bool setMobileAndRequestCode(id topVC, const char *mobileCString) {
    if (!topVC || !mobileCString) return false;

    if (!isKindOfClass(topVC, "JDNewLoginViewController")) {
        return false;
    }

    id phoneText = getIvar(topVC, "_phoneText");
    if (!phoneText) return false;

    SEL sel_setText = sel_registerName("setText:");
    if (!selectorIfRespondsTo(phoneText, "setText:")) {
        return false;
    }

    // 创建 NSString *mobile
    id mobileStr = ((id (*)(Class, SEL, const char *))objc_msgSend)(
        objc_getClass("NSString"),
        sel_registerName("stringWithUTF8String:"),
        mobileCString
    );

    // 设置手机号
    ((void (*)(id, SEL, id))objc_msgSend)(phoneText, sel_setText, mobileStr);

    // 调用 getCodeButtonClicked1:
    if (!selectorIfRespondsTo(topVC, "getCodeButtonClicked1:")) {
        return false;
    }

    ((void (*)(id, SEL, id))objc_msgSend)(topVC, sel_registerName("getCodeButtonClicked1:"), NULL);

    return true;
}

// context 结构
typedef struct {
    id topVC;
    char *mobileCString;
} LoginContext;

// 带 context 的回调类型
typedef void (*SelectCountryCallbackEx)(bool success, void *context);

#pragma mark - 异步选择国家/地区

void selectCountryAsyncEx(id topVC, const char *targetName, SelectCountryCallbackEx callback, void *context) {
    if (!topVC || !targetName || !callback) {
        if (callback) callback(false, context);
        return;
    }

    if (!isKindOfClass(topVC, "JDNewLoginViewController") &&
        !isKindOfClass(topVC, "JDRegisterVerifyPhoneViewController")) {
        showToastMessage("不是登录页或注册页", 1.0);
        callback(false, context);
        return;
    }

    const char *currentCountryCodeStr = getVar(topVC, "_phoneAndCodeNum");
    if (!currentCountryCodeStr) {
        showToastMessage("获取当前区号失败", 1.0);
        callback(false, context);
        return;
    }

    if ((strcmp(targetName, "中国") == 0 && strcmp(currentCountryCodeStr, "(+86)") == 0) ||
        (strcmp(targetName, "美国") == 0 && strcmp(currentCountryCodeStr, "(+1)") == 0) ||
        (strcmp(targetName, "中国香港") == 0 && strcmp(currentCountryCodeStr, "(+852)") == 0)) {
        LOG("[selectCountryAsyncEx] 已经是目标国家: %s", targetName);
        callback(true, context);
        return;
    }

    SEL sel_selectCountry = sel_registerName("getCountryDistrictCodeList");
    if (!sel_selectCountry || !((BOOL (*)(id, SEL, SEL))objc_msgSend)(topVC, sel_registerName("respondsToSelector:"), sel_selectCountry)) {
        showToastMessage("无法获取国家/地区列表", 1.0);
        callback(false, context);
        return;
    }

    ((void (*)(id, SEL))objc_msgSend)(topVC, sel_selectCountry);

    char *targetNameCopy = strdup(targetName);
    if (!targetNameCopy) {
        showToastMessage("内存分配失败", 1.0);
        callback(false, context);
        return;
    }

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        id currentTopVC = getTopViewController();
        if (!currentTopVC) {
            showToastMessage("页面状态获取失败", 1.0);
            free(targetNameCopy);
            callback(false, context);
            return;
        }

        Class vcClass = object_getClass(currentTopVC);
        Ivar originDataIvar = class_getInstanceVariable(vcClass, "_originData");

        for (int retry = 0; retry < 20; retry++) {
            usleep(500000);

            id originData = originDataIvar ? object_getIvar(currentTopVC, originDataIvar) : NULL;
            if (!originData) continue;

            NSUInteger groupCount = ((NSUInteger (*)(id, SEL))objc_msgSend)(originData, sel_registerName("count"));
            if (groupCount == 0) continue;

            for (NSUInteger i = 0; i < groupCount; i++) {
                id group = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(originData, sel_registerName("objectAtIndex:"), i);
                if (!group) continue;

                NSUInteger itemCount = ((NSUInteger (*)(id, SEL))objc_msgSend)(group, sel_registerName("count"));
                if (itemCount == 0) continue;

                for (NSUInteger j = 0; j < itemCount; j++) {
                    id item = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(group, sel_registerName("objectAtIndex:"), j);
                    if (!item) continue;

                    Ivar nameIvar = class_getInstanceVariable(object_getClass(item), "_countryDistrictName");
                    id countryNameObj = nameIvar ? object_getIvar(item, nameIvar) : NULL;
                    if (!countryNameObj) continue;

                    const char *countryName = ((const char *(*)(id, SEL))objc_msgSend)(
                        countryNameObj, sel_registerName("UTF8String")
                    );


                    if (countryName && strcmp(countryName, targetNameCopy) == 0) {

                        Class indexPathCls = objc_getClass("NSIndexPath");

                        NSUInteger fixedRow = (j > 0) ? (j - 1) : 0;  // 修复：减1
                        id indexPath = ((id (*)(Class, SEL, NSUInteger, NSUInteger))objc_msgSend)(
                            indexPathCls, sel_registerName("indexPathForRow:inSection:"), fixedRow, i
                        );

                        Ivar tableViewIvar = class_getInstanceVariable(vcClass, "_tableView");
                        id tableView = tableViewIvar ? object_getIvar(currentTopVC, tableViewIvar) : NULL;

                        SEL sel_didSelect = sel_registerName("tableView:didSelectRowAtIndexPath:");
                        if (!tableView || !class_respondsToSelector(vcClass, sel_didSelect)) {
                            free(targetNameCopy);
                            callback(false, context);
                            return;
                        }

                        ((void (*)(id, SEL, id, id))objc_msgSend)(currentTopVC, sel_didSelect, tableView, indexPath);

                        free(targetNameCopy);
                        callback(true, context);
                        return;
                    }
                }
            }
        }

        free(targetNameCopy);
        callback(false, context);
    });
}

#pragma mark - 短信登录->选择国家之后的回调函数

void onSelectCountryCompleteEx(bool success, void *ctx) {
    LoginContext *context = (LoginContext *)ctx;

    LOG("[onSelectCountryCompleteEx] 国家选择结果: %s", success ? "成功" : "失败");

    if (!success) {
        LOG("[onSelectCountryCompleteEx] 选择国家/地区失败");
        showToastMessage("选择国家/地区失败", 1.0);

        // 释放内存
        if (context) {
            if (context->mobileCString) {
                free(context->mobileCString);
            }
            free(context);
        }
        return;
    }

    // 成功后延时1秒执行 setMobileAndRequestCode
    LOG("[onSelectCountryCompleteEx] 国家选择成功，延时1秒后设置手机号");

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        LOG("[onSelectCountryCompleteEx] 开始设置手机号: %s",
               context->mobileCString ? context->mobileCString : "NULL");

        // 验证 context 是否仍然有效
        if (!context) {
            LOG("[onSelectCountryCompleteEx] context 为空");
            return;
        }

        if (!context->mobileCString) {
            LOG("[onSelectCountryCompleteEx] mobileCString 为空");
            free(context);
            return;
        }

        // 调用设置手机号和获取验证码
        bool result = setMobileAndRequestCode(context->topVC, context->mobileCString);
        if (result) {
            LOG("[onSelectCountryCompleteEx] 手机号设置和验证码请求成功");
        } else {
            LOG("[onSelectCountryCompleteEx] 手机号设置或验证码请求失败");
            showToastMessage("验证码请求失败", 1.0);
        }

        // 释放内存
        if (context->mobileCString) {
            free(context->mobileCString);
        }
        free(context);
    });
}

#pragma mark - 外部调用入口
bool loginByMobileCode(id topVC, const char *countryName, const char *mobileCString) {

    if (!countryName || !mobileCString) {
        return false;
    }

    // id topVC = getTopViewController();
    // if (!topVC || !isKindOfClass(topVC, "JDNewLoginViewController")) return false;

    LoginContext *ctx = malloc(sizeof(LoginContext));
    if (!ctx) {
        showToastMessage("内存分配失败", 1.0);
        return false;
    }

    ctx->topVC = topVC;
    ctx->mobileCString = strdup(mobileCString);
    if (!ctx->mobileCString) {
        free(ctx);
        showToastMessage("内存分配失败", 1.0);
        return false;
    }

    selectCountryAsyncEx(topVC, countryName, onSelectCountryCompleteEx, ctx);

    return true;

}

bool loginManager(const char *buttonId, const char *serialNumber) {

    // 1. 读取当前备份的api.txt
    ApiConfig *api = read_api_config_from_file();
    if (!api) {
        LOG("读取配置文件失败");
        showToastMessage("读取api.txt配置文件失败", 1.0);
        return false;
    }
    LOG("已加载配置: %s %s", api->phoneNumber, api->url);
    
    const char *phoneNumber = api->phoneNumber;
    const char *url = api->url;
    const char *username = api->username;
    const char *password = api->password;

    // 校验必需参数（手机号和URL必须存在，用户名和密码可选）
    if (!phoneNumber || !url) {
        LOG("配置文件必需参数不完整：缺少手机号或URL");
        free_api_config(api);
        return false;
    }

    id topVC = getTopViewController();
    if (!topVC) return false;
    // 只允许在这2个页面任何一个都可以继续
    // if (!isKindOfClass(topVC, "JDRegisterVerifyMessageCodeViewController") && !isKindOfClass(topVC, "JDPhoneLoginFirstViewController")) {
    //     LOG("当前页面不是登录页或注册页");
    //     return false;
    // }

    if (strcmp(buttonId, "loginBySMSCode1") == 0) {
        // 美号
        loginByMobileCode(topVC, "美国", phoneNumber);
    } else if (strcmp(buttonId, "loginBySMSCode2") == 0) {
        // 港号
        loginByMobileCode(topVC, "中国香港", phoneNumber);
    } else if (strcmp(buttonId, "loginByPWD") == 0) {
        // 账号密码登录 - 检查用户名和密码是否存在
        if (!username || strlen(username) == 0 || !password || strlen(password) == 0) {
            LOG("账号密码登录失败：用户名或密码为空");
            showToastMessage("配置文件中缺少用户名或密码", 1.0);
            free_api_config(api);
            return false;
        }
        setUserPwdAndLogin(topVC, username, password);
    } else if (strcmp(buttonId, "getSMSCode") == 0) {
        fetchVerificationCodeAsync(url, 60, 2);
    } else if (strcmp(buttonId, "enterSMSCode") == 0) {
        // 如果是在登录或者注册页面就直接调用
        if (isKindOfClass(topVC, "JDPhoneLoginFirstViewController") || isKindOfClass(topVC, "JDRegisterVerifyMessageCodeViewController")) {
            // 从剪贴板提取内容
            char* code = getClipboardTextIOS();
            // 自动填入验证码
            accessCaptchaTextField(code);
            free(code);
        } else {
            // 尝试从剪切板输入验证码
            // 在获取验证码成功之后，会尝试自动输入，不管是否输入成功都会将验证码写入剪贴板
            InputResult result = handleInpuText(NULL, true);
            if (result.status) {
                showToastMessage("输入成功", 1.0);
            } else {
                LOG("输入失败: %s", result.error);
                const char* error = result.error ? result.error : "未知错误";
                showToastMessage(error, 1.0);
            }
            freeInputTextResult(&result);
        }
    } else if (strcmp(buttonId, "getApi") == 0) { 
        CryptoRequestConfig config = {
            .path = "/tools/v1",
            .action = "api",
            .serial_number = serialNumber,
            .version = version_data,
            .version_length = version_data_length,
            .product_id = product_id_data,
            .product_id_length = product_id_data_length
        };
        CryptoRequestResult result = crypto_request_send_and_decrypt(&config);


        if (!result.success) {
            showToastMessage("服务器请求失败", 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        if (!result.response_data) {
            showToastMessage("服务器响应数据为空", 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
        if (code == 0) {
            showToastMessage("服务器响应数据为空", 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        if (code != 200) {
            const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
            const char* error = message ? message : "未知错误";
            showToastMessage(error, 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
        if (!data) {
            showToastMessage("服务器响应数据为空", 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        bool status = JSONUtils_GetBoolValue(data, "status", false);
        if (!status) {
            showToastMessage("无api数据", 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        const char* apiUrl = JSONUtils_GetStringValue(data, "api");
        if (!apiUrl) {
            showToastMessage("返回评价内容异常", 2.0);
            crypto_request_free_result(&result);
            return false;
        }
        

        showToastMessage(apiUrl, 2.0);

        LOG("api链接: %s", apiUrl);

        crypto_request_free_result(&result);

    }

    // 使用完成需要释放
    // free_api_config(api);
    return true;
}