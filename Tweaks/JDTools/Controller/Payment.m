#include <objc/runtime.h>
#include <objc/message.h>
#include <syslog.h>
#include <objc/NSObjCRuntime.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>
#include "GlobalNetworkData.h"
#include "Controller.h"


void invokeCashierPayMethod(const char *functionIdStr) {
    // 1. 获取 topVC
    id topVC = getTopViewController();
    if (!topVC) {
        showToastMessage("获取页面失败", 1.0);
        return;
    }
    // syslog(LOG_NOTICE, "topVC: %p", topVC);


    // Class cls = object_getClass(topVC);
    // if (!cls) {
    //     // showToastMessage("无法获取页面类", 2.0);
    //     return;
    // }

    // const char *className = class_getName(cls);
    // syslog(LOG_NOTICE, "topVC class name: %s", className);
    const char *className = getClassName(topVC);
    // 安全地判断类名
    if (strcmp(className, "JDCashierPayViewController") != 0) {
        showToastMessage("当前页面不是收银台", 2.0);
        return;
    }

    // 3. 构造 selector
    SEL sel = sel_registerName("_payWithParamModel:channelModel:functionId:");


    // 4. 创建 NSString *，使用调用者传入的 const char * 参数
    Class NSStringCls = objc_getClass("NSString");
    SEL allocSel = sel_registerName("alloc");
    SEL initUTF8Sel = sel_registerName("initWithUTF8String:");
    id str = ((id (*)(id, SEL, const char *))objc_msgSend)(
        ((id (*)(Class, SEL))objc_msgSend)(NSStringCls, allocSel),
        initUTF8Sel,
        functionIdStr
    );

    // 5. 执行反射调用，前两个参数传 nil，第三个传字符串对象
    ((void (*)(id, SEL, id, id, id))objc_msgSend)(topVC, sel, 0, 0, str);

}

bool paymentManager(const char *buttonId, const char *serialNumber) {
    if (s_networkMonitorInstance) {
        [s_networkMonitorInstance updateStatus:YES];
        invokeCashierPayMethod(buttonId);
        return true;
    } else {
        showToastMessage("代付功能服务异常", 2.0);
        return false;
    }
}