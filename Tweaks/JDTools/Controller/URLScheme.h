// URLScheme.h

#ifndef URLSCHEME_H
#define URLSCHEME_H

#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

bool openURLScheme(const char *urlCString);

// 订单列表 
bool openURLSchemeOrderList(int listType);

// 打开评价中心
bool openURLSchemeCommontCenter();

// 打开商品详情页
bool openURLSchemeWarePage(const char *skuId);

#ifdef __cplusplus
}
#endif

#endif // URLSCHEME_H

/**

-- {
--     首页:HomePage
--     分类:category
--     发现:Discovery
--     购物车:cart
--     我的:myJd
--     订单列表:orderlist
--     搜索输入状态:search
--     领券中心/签到:couponCenter
--     京东客服:jd_native_jimi
--     京东秒杀:seckill
--     评价中心:commentCenter
--     我的钱包:myWallet
--     优惠券:mycoupon
--     通用:setting
--     订单详情:orderDetail,orderId
--     订单追踪:ordertrace,orderId
--     商品详情:productDetail,skuId
--     店铺首页:jshopMain,shopId
-- }
-- listType: 表示类型 1=全部 2=代付款 3=待发货 4=已完成 5=已取消
-- openURL('openapp.jdmobile://virtual?params='..urlEncoder('{"category":"jump","des":"orderlist","listType":"3"}'))

-- listType: 表示类型 1=全部 2=代付款 3=待发货 4=已完成 5=已取消
-- script.jd.scheme({des = "orderlist", listType = "1"})
-- script.jd.scheme({des = "orderDetail", orderId = "************"}) -- 订单详情
-- script.jd.scheme({des = "ordertrace", orderId = "************"}) -- 订单追踪
-- script.jd.scheme({des = "productDetail", skuId = "100002302927"}) -- 商品详情
-- script.jd.scheme({des = "jshopMain", shopId = "659378"}) -- 店铺首页
-- script.jd.scheme({des = "search", keyword = "古"}) -- 搜索关键词
-- script.jd.scheme({des = "mycoupon"}) -- 优惠券
-- script.jd.scheme({des = "m", url = "https://www.jd.com/"}) -- 加载url
-- script.jd.scheme({des = "VideoImmersion", id = "790793975"})--跳转视频
-- openURL('openapp.jdpingou://virtual?params='..urlEncoder('{"category":"jump","des":"m","url":"'..url..'"}'))
-- openURL('openjdlite://virtual?params='..urlEncoder('{"category":"jump","des":"'..des..'"}'))
-- function jump_schemeurl(app, url)
--     if app == 'com.360buy.jdmobile' then
--         openURL('openapp.jdmobile://virtual?params='..urlEncoder('{"category":"jump","des":"m","url":"'..url..'"}'))
--     end
--     if app == 'com.jd.jdmobilelite' then
--         openURL('openjdlite://virtual?params='..urlEncoder('{"category":"jump","des":"m","url":"'..url..'"}'))
--     end
--     if app == 'com.360buy.jdpingou' then
--         openURL('openapp.jdpingou://virtual?params='..urlEncoder('{"category":"jump","des":"m","url":"'..url..'"}'))
--     end
-- end

-- openURL("openApp.jdMobile://virtual?params="..urlEncoder('{"category":"jump","des":"oftenBuy","fromPage":"ddxq","newUIType":"1"}'))--常购清单
-- openURL("openApp.jdMobile://virtual?params="..urlEncoder('{"category":"jump","des":"feedback","from":"dingdanzhongxin"}'))--功能反馈
-- openURL("openApp.jdMobile://virtual?params="..urlEncoder('{"category":"jump","des":"orderRecycle","from":"orderDetail"}'))--订单回收站
-- openURL("openApp.jdMobile://virtual?params="..urlEncoder('{"category":"jump","des":"orderConfirmSuccessPage","orderId":"276500034360"}'))--订单收货成功页面


-- \t_allGoodJumpUrl (NSString*): @"openApp.jdMobile://virtual?params={"category":"jump","des":"jshopMain","shopId":"1000004259","venderId":"1000004259","jumpTab":"allProduct"}"
-- \t_inShopLookJumpUrl (NSString*): @"openApp.jdMobile://virtual?params={"category":"jump","des":"jshopMain","shopId":"1000004259","venderId":"1000004259","jumpTab":"home"}"

-- script.jd.scheme({des = "m", url = "https://coupon.m.jd.com/coupons/show.action?key=46aabd4e7ad1465197702e731eda6e86&roleId=136848829&to=mall.jd.com/index-13305560.html"}) -- 加载url

*/