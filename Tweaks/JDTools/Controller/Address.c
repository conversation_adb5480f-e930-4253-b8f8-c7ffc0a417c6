#include <objc/runtime.h>
#include <objc/message.h>
#include "Log.h"
#include <objc/NSObjCRuntime.h>
#include <time.h>
#include <unistd.h>
#include <stdio.h>
#include <string.h>
#include <dispatch/dispatch.h>
#include "Controller.h"
#include "JSONUtils.h"
#include "CryptoRequest.h"
#include "/Users/<USER>/Documents/Main/Tweak/Tweaks/JDTools/productInfo.h"

// 随机生成一个字母
const char* random_letter() {
    static char buf[2]; // 静态数组用于返回字符串，包含一个字母和 '\0'
    const char alphabet[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    size_t len = sizeof(alphabet) - 1;

    buf[0] = alphabet[rand() % len];
    buf[1] = '\0';
    return buf;
}
// 跳转地址管理
bool jumpAddressList() {
    id topVC = getTopViewController();
    if (!topVC) {
        showToastMessage("地址状态获取失败，请重试", 1.0);
        return false;
    }
    
    // 判断是否在地址管理页面
    if (isKindOfClass(topVC, "COCheckoutAddressListViewController")) {
        return true;
    }

    bool success = jdOAuthManager("COCheckoutAddressListViewController");
    if (!success) {
        showToastMessage("加载地址管理失败", 1.0);
        return false;
    }
    // showToastMessage("加载成功", 1.0);
    return true;
}

// 点击新建收货人按钮
bool pressNewContactButton(id topVC) {
    SEL sel = selectorIfRespondsTo(topVC, "pressNewContactButton:");
    if (sel) {
        ((void (*)(id, SEL))objc_msgSend)(topVC, sel);
        return true;
    }
    return false;
}

// 设置默认地址 - 增强安全性版本
bool setDefaultAddress(id topVC) {
    // 参数有效性检查
    if (!topVC) {
        LOG("setDefaultAddress: topVC 为空");
        return false;
    }

    // 页面类型检查
    if (!isKindOfClass(topVC, "COCheckoutAddressEditViewController")) {
        LOG("setDefaultAddress: 页面类型不正确");
        return false;
    }

    // 检查页面是否仍然有效
    SEL isViewLoadedSel = selectorIfRespondsTo(topVC, "isViewLoaded");
    if (isViewLoadedSel) {
        BOOL isLoaded = ((BOOL (*)(id, SEL))objc_msgSend)(topVC, isViewLoadedSel);
        if (!isLoaded) {
            LOG("setDefaultAddress: 页面未加载");
            return false;
        }
    }

    id tableView = getIvar(topVC, "_tableView");
    if (!tableView) {
        LOG("setDefaultAddress: tableView 获取失败");
        return false;
    }

    id visibleCells = getIvar(tableView, "_visibleCells");
    if (!visibleCells) {
        LOG("setDefaultAddress: visibleCells 获取失败");
        return false;
    }

    // 检查是否响应 count 方法
    SEL sel_count = selectorIfRespondsTo(visibleCells, "count");
    if (!sel_count) {
        LOG("setDefaultAddress: count 方法不存在");
        return false;
    }

    // 调用计算count方法，获取数量
    NSUInteger count = ((NSUInteger (*)(id, SEL))objc_msgSend)(visibleCells, sel_count);
    LOG("setDefaultAddress: 找到 %lu 个可见单元格", (unsigned long)count);

    SEL sel_objectAtIndex = sel_registerName("objectAtIndex:");
    SEL sel_isOn = sel_registerName("isOn");
    SEL sel_handleTapSwitch = sel_registerName("handleTapSwitch");

    for (NSUInteger i = 0; i < count; i++) {
        id cell = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(visibleCells, sel_objectAtIndex, i);
        if (!cell) {
            LOG("setDefaultAddress: 单元格 %lu 为空", (unsigned long)i);
            continue;
        }

        Class cellClass = object_getClass(cell);
        if (!cellClass) {
            LOG("setDefaultAddress: 单元格 %lu 类获取失败", (unsigned long)i);
            continue;
        }

        const char* className = class_getName(cellClass);
        if (!className || !strstr(className, "Default")) {
            LOG("setDefaultAddress: 单元格 %lu 不是默认地址类型: %s", (unsigned long)i, className ? className : "NULL");
            continue;
        }

        // 获取 _switchBtn
        Ivar switchBtnIvar = class_getInstanceVariable(cellClass, "_switchBtn");
        if (!switchBtnIvar) {
            LOG("setDefaultAddress: 单元格 %lu 的开关按钮变量不存在", (unsigned long)i);
            continue;
        }

        id switchBtn = object_getIvar(cell, switchBtnIvar);
        if (!switchBtn) {
            LOG("setDefaultAddress: 单元格 %lu 的开关按钮为空", (unsigned long)i);
            continue;
        }

        // 检查开关是否响应必要方法
        if (!selectorIfRespondsTo(switchBtn, "isOn")) {
            LOG("setDefaultAddress: 开关按钮不响应 isOn 方法");
            continue;
        }

        // 判断开关是否开启
        BOOL isOn = ((BOOL (*)(id, SEL))objc_msgSend)(switchBtn, sel_isOn);
        if (!isOn) {
            // 检查是否响应点击方法
            if (selectorIfRespondsTo(switchBtn, "handleTapSwitch")) {
                ((void (*)(id, SEL))objc_msgSend)(switchBtn, sel_handleTapSwitch);
                LOG("setDefaultAddress: 成功设置默认地址开关");
            } else {
                LOG("setDefaultAddress: 开关按钮不响应 handleTapSwitch 方法");
            }
        } else {
            LOG("setDefaultAddress: 开关已经开启");
        }
    }

    LOG("setDefaultAddress: 默认地址设置完成");
    return true;
}

// 保存地址 - 增强安全性版本
bool saveAddress(id topVC) {
    // 参数有效性检查
    if (!topVC) {
        LOG("saveAddress: topVC 为空");
        return false;
    }

    // 页面类型检查
    if (!isKindOfClass(topVC, "COCheckoutAddressEditViewController")) {
        LOG("saveAddress: 页面类型不正确");
        return false;
    }

    // 检查页面是否仍然有效
    SEL isViewLoadedSel = selectorIfRespondsTo(topVC, "isViewLoaded");
    if (isViewLoadedSel) {
        BOOL isLoaded = ((BOOL (*)(id, SEL))objc_msgSend)(topVC, isViewLoadedSel);
        if (!isLoaded) {
            LOG("saveAddress: 页面未加载");
            return false;
        }
    }

    SEL sel = selectorIfRespondsTo(topVC, "saveAddressToServer");
    if (sel) {
        ((void (*)(id, SEL))objc_msgSend)(topVC, sel);
        LOG("saveAddress: 地址保存调用成功");
        return true;
    } else {
        LOG("saveAddress: saveAddressToServer 方法不存在");
        return false;
    }
}

// 智能识别地址 - 增强安全性版本
bool handleIntelligentText(id topVC, const char* addressText){
    // 参数有效性检查
    if (!topVC || !addressText || strlen(addressText) == 0) {
        LOG("handleIntelligentText: 参数无效 - topVC: %p, addressText: %s", topVC, addressText ? addressText : "NULL");
        return false;
    }

    // 页面类型检查
    if (!isKindOfClass(topVC, "COCheckoutAddressEditViewController")) {
        LOG("handleIntelligentText: 页面类型不正确");
        return false;
    }

    // 检查页面是否仍然有效
    SEL isViewLoadedSel = selectorIfRespondsTo(topVC, "isViewLoaded");
    if (isViewLoadedSel) {
        BOOL isLoaded = ((BOOL (*)(id, SEL))objc_msgSend)(topVC, isViewLoadedSel);
        if (!isLoaded) {
            LOG("handleIntelligentText: 页面未加载");
            return false;
        }
    }

    // 方法存在性检查
    SEL handleSel = selectorIfRespondsTo(topVC, "handleIntelligentText:");
    if (!handleSel) {
        LOG("handleIntelligentText: 目标方法不存在");
        return false;
    }

    // 安全创建 NSString
    Class NSStringClass = objc_getClass("NSString");
    if (!NSStringClass) {
        LOG("handleIntelligentText: NSString 类不存在");
        return false;
    }

    // 创建字符串对象
    id address = ((id (*)(Class, SEL, const char*))objc_msgSend)(NSStringClass,
                                                               sel_registerName("stringWithUTF8String:"),
                                                               addressText);
    if (!address) {
        LOG("handleIntelligentText: 字符串创建失败");
        return false;
    }

    // 调用目标方法
    ((void (*)(id, SEL, id))objc_msgSend)(topVC, handleSel, address);
    LOG("handleIntelligentText: 地址识别调用成功");
    return true;
}

// 删除地址
static void deleteNextAddressRecursive(id topVC) {
    if (!topVC) return;

    // Ivar tableViewIvar = class_getInstanceVariable(object_getClass(topVC), "_tableView");
    // id tableView = tableViewIvar ? object_getIvar(topVC, tableViewIvar) : NULL;
    id tableView = getIvar(topVC, "_tableView");
    if (!tableView) {
        LOG("delete_next_address_recursive: tableView nil");
        return;
    }

    // Ivar visibleCellsIvar = class_getInstanceVariable(object_getClass(tableView), "_visibleCells");
    // id visibleCells = visibleCellsIvar ? object_getIvar(tableView, visibleCellsIvar) : NULL;
    id visibleCells = getIvar(tableView, "_visibleCells");
    if (!visibleCells) {
        LOG("delete_next_address_recursive: visibleCells nil");
        return;
    }

    SEL countSel = sel_registerName("count");
    NSUInteger count = ((NSUInteger (*)(id, SEL))objc_msgSend)(visibleCells, countSel);
    if (count == 0) {
        LOG("delete_next_address_recursive: no more visible addresses");
        showToastMessage("无地址列表", 1.0);
        return;
    }

    SEL objectAtIndexSel = sel_registerName("objectAtIndex:");
    SEL createSwipeSel = sel_registerName("createSwipeViewIfNeeded");
    SEL tappedButtonSel = sel_registerName("tappedButton:");

    // 只删除第0个地址，删除后等待再继续递归
    id cell = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(visibleCells, objectAtIndexSel, 0);
    if (!cell) {
        LOG("delete_next_address_recursive: first cell is nil, retrying after delay");
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            deleteNextAddressRecursive(topVC);
        });
        return;
    }

    Ivar cellSwiperIvar = class_getInstanceVariable(object_getClass(cell), "_cellSwiper");
    id cellSwiper = cellSwiperIvar ? object_getIvar(cell, cellSwiperIvar) : NULL;
    if (!cellSwiper) {
        LOG("delete_next_address_recursive: cellSwiper nil, retrying after delay");
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            deleteNextAddressRecursive(topVC);
        });
        return;
    }

    ((void (*)(id, SEL))objc_msgSend)(cellSwiper, createSwipeSel);

    Ivar swipeViewIvar = class_getInstanceVariable(object_getClass(cellSwiper), "_swipeView");
    id swipeView = swipeViewIvar ? object_getIvar(cellSwiper, swipeViewIvar) : NULL;
    if (!swipeView) {
        LOG("delete_next_address_recursive: swipeView nil, retrying after delay");
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)),
                       dispatch_get_main_queue(), ^{
            deleteNextAddressRecursive(topVC);
        });
        return;
    }

    LOG("delete_next_address_recursive: deleting address, swipeView: %p", swipeView);

    ((void (*)(id, SEL, id))objc_msgSend)(swipeView, tappedButtonSel, NULL);

    // 1秒后递归调用自己，继续删除下一个
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)),
                   dispatch_get_main_queue(), ^{
        deleteNextAddressRecursive(topVC);
    });
    showToastMessage("删除成功", 1.0);
}
// 删除所有地址
void deleteAllVisibleAddresses() {
    // 判断当前页面，是否在地址管理，如果不在就跳转过去，如果在就执行删除

    id topVC = getTopViewController();
    if (!topVC) {
        showToastMessage("地址管理状态获取失败", 1.0);
        return;
    }
    if (isKindOfClass(topVC, "COCheckoutAddressListViewController")) {
        // 当前所在地址管理页面，直接执行删除逻辑
        dispatch_async(dispatch_get_main_queue(), ^{
            deleteNextAddressRecursive(topVC);
        });
    } else {
        // 跳转到地址
        bool success = jdOAuthManager("COCheckoutAddressListViewController");
        if (!success) {
            LOG("deleteAllVisibleAddresses: jdOAuthManager nil");
            showToastMessage("加载地址失败", 1.0);
            return;
        }
        showToastMessage("加载地址成功，2后执行删除地址", 1.0);
        // dispatch延迟1秒执行
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            id topVC = getTopViewController();
            if (!topVC) {
                showToastMessage("地址管理状态获取失败", 1.0);
                LOG("deleteAllVisibleAddresses: getTopViewController nil");
                return;
            }

            dispatch_async(dispatch_get_main_queue(), ^{
                deleteNextAddressRecursive(topVC);
            });
        });
    }
    
}

// 新建收货人
void executeCreateAddressLogic(id topVC, const char* serialNumber) {
    LOG("executeCreateAddressLogic---> %s", serialNumber);
    LOG("topVC---> %p", topVC);

    LOG("[executeCreateAddressLogic] 函数开始，接收到的 serialNumber: %s",
           serialNumber ? serialNumber : "NULL");

    // 验证 serialNumber 参数
    if (!serialNumber) {
        LOG("[executeCreateAddressLogic] serialNumber 为 NULL");
        showToastMessage("序列号参数无效", 1.0);
        return;
    }

    if (strlen(serialNumber) == 0) {
        LOG("[executeCreateAddressLogic] serialNumber 为空字符串");
        showToastMessage("序列号参数为空", 1.0);
        return;
    }


    if (!topVC) {
        topVC = getTopViewController();
        if (!topVC) {
            showToastMessage("获取页面控制器失败", 1.0);
            return;
        }
    }

    // 确保是正确的页面类型
    if (!isKindOfClass(topVC, "COCheckoutAddressEditViewController")) {
        showToastMessage("页面类型不正确", 1.0);
        return;
    }

    LOG("[executeCreateAddressLogic] 验证通过，使用 serialNumber: %s", serialNumber);

    // 构建请求配置
    CryptoRequestConfig config = {
        .path = "/tools/v1",
        .action = "address",
        .serial_number = serialNumber,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    LOG("[executeCreateAddressLogic] 开始发送网络请求");

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

        // 检查请求是否成功
        if (!result.success) {
            LOG("[executeCreateAddressLogic] 网络请求失败");
            showToastMessage("服务器请求失败", 2.0);
            crypto_request_free_result(&result);
            return;
        }

        // 检查响应数据是否存在
        if (!result.response_data) {
            LOG("[executeCreateAddressLogic] 响应数据为空");
            showToastMessage("服务器响应数据为空", 2.0);
            crypto_request_free_result(&result);
            return;
        }

        // 解析响应状态码
        int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
        LOG("[executeCreateAddressLogic] 响应状态码: %d", code);

        if (code == 0) {
            LOG("[executeCreateAddressLogic] 状态码为0，响应无效");
            showToastMessage("服务器响应数据无效", 2.0);
            crypto_request_free_result(&result);
            return;
        }

        if (code != 200) {
            const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
            const char* error = message ? message : "未知错误";
            LOG("[executeCreateAddressLogic] 服务器返回错误: %s", error);
            showToastMessage(error, 2.0);
            crypto_request_free_result(&result);
            return;
        }

        // 获取数据对象
        cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
        if (!data) {
            LOG("[executeCreateAddressLogic] data 对象为空");
            showToastMessage("服务器响应数据格式错误", 2.0);
            crypto_request_free_result(&result);
            return;
        }

        // 检查状态
        bool status = JSONUtils_GetBoolValue(data, "status", false);
        if (!status) {
            LOG("[executeCreateAddressLogic] 服务器返回状态为 false");
            showToastMessage("缺少地址，请及时上传", 2.0);
            crypto_request_free_result(&result);
            return;
        }

        // 获取地址文本
        const char* addressText = JSONUtils_GetStringValue(data, "address");
        if (!addressText || strlen(addressText) == 0) {
            LOG("[executeCreateAddressLogic] 地址文本为空");
            showToastMessage("返回地址异常", 2.0);
            crypto_request_free_result(&result);
            return;
        }

        LOG("[executeCreateAddressLogic] 成功获取地址: %s", addressText);
        showToastMessage(addressText, 2.0);

    LOG("地址: %s", addressText);
    showToastMessage(addressText, 2.0);

    // 安全的地址处理逻辑
    if (handleIntelligentText(topVC, addressText)) {
        LOG("地址识别成功，开始后续处理");

        // 创建 topVC 的弱引用副本，避免循环引用
        __unsafe_unretained id weakTopVC = topVC;

        // 延时设置为默认地址
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 重新获取强引用并验证页面状态
            id strongTopVC = weakTopVC;
            if (!strongTopVC) {
                LOG("topVC 已被释放，取消默认地址设置");
                return;
            }

            // 验证页面是否仍然有效
            if (!isKindOfClass(strongTopVC, "COCheckoutAddressEditViewController")) {
                LOG("页面类型已改变，取消默认地址设置");
                return;
            }

            // 检查页面是否仍然加载
            SEL isViewLoadedSel = selectorIfRespondsTo(strongTopVC, "isViewLoaded");
            if (isViewLoadedSel) {
                BOOL isLoaded = ((BOOL (*)(id, SEL))objc_msgSend)(strongTopVC, isViewLoadedSel);
                if (!isLoaded) {
                    LOG("页面未加载，取消默认地址设置");
                    return;
                }
            }

            if (setDefaultAddress(strongTopVC)) {
                    LOG("默认地址设置成功，准备保存");

                    // 再次延时保存地址
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        // 最终验证
                        id finalTopVC = weakTopVC;
                        if (!finalTopVC) {
                            LOG("topVC 已被释放，取消地址保存");
                            return;
                        }

                        if (!isKindOfClass(finalTopVC, "COCheckoutAddressEditViewController")) {
                            LOG("页面类型已改变，取消地址保存");
                            return;
                        }

                        // 最终检查页面是否仍然加载
                        SEL finalIsViewLoadedSel = selectorIfRespondsTo(finalTopVC, "isViewLoaded");
                        if (finalIsViewLoadedSel) {
                            BOOL finalIsLoaded = ((BOOL (*)(id, SEL))objc_msgSend)(finalTopVC, finalIsViewLoadedSel);
                            if (!finalIsLoaded) {
                                LOG("页面未加载，取消地址保存");
                                return;
                            }
                        }

                        if (saveAddress(finalTopVC)) {
                            LOG("地址保存成功");
                            showToastMessage("地址创建完成", 1.0);
                        } else {
                            LOG("地址保存失败");
                            showToastMessage("地址保存失败", 1.0);
                        }
                    });
                } else {
                    LOG("默认地址设置失败");
                    showToastMessage("默认地址设置失败", 1.0);
                }
        });
        } else {
            LOG("地址识别失败");
            showToastMessage("地址识别失败", 1.0);
        }

    // 清理资源
    crypto_request_free_result(&result);
}

// 自动创建地址入口
void createAddress(const char* serialNumber) {

    id topVC = getTopViewController();
    if (!topVC) {
        showToastMessage("获取页面控制器失败", 1.0);
        return;
    }
    if (isKindOfClass(topVC, "COCheckoutAddressEditViewController")) {
        // 已经在地址编辑页面，直接执行创建逻辑
        executeCreateAddressLogic(topVC, serialNumber);
        return;
    }

    double delayTime = 0.0;

    if (!isKindOfClass(topVC, "COCheckoutAddressListViewController")) {
        // 先跳转至地址管理页面
        if (!jdOAuthManager("COCheckoutAddressListViewController")) {
            showToastMessage("加载地址管理失败", 1.0);
            return;
        }
        delayTime = 1.0;
    }

    // 创建 serialNumber 的副本以避免内存问题
    char* serialNumberCopy = strdup(serialNumber);
    if (!serialNumberCopy) {
        LOG("[createAddress] 内存分配失败，无法复制 serialNumber");
        showToastMessage("内存分配失败", 1.0);
        return;
    }

    LOG("[createAddress] 创建 serialNumber 副本: %s", serialNumberCopy);

    // 然后延时执行点击新建联系人
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        id topVC = getTopViewController();
        if (!topVC) {
            showToastMessage("地址管理状态获取失败", 1.0);
            free(serialNumberCopy); // 释放内存
            return;
        }
        // 点击新建联系人
        if (!pressNewContactButton(topVC)) {
            showToastMessage("进入编辑状态失败", 1.0);
            free(serialNumberCopy); // 释放内存
            return;
        }

        // 再次创建副本用于内层 block
        char* innerSerialNumberCopy = strdup(serialNumberCopy);
        if (!innerSerialNumberCopy) {
            LOG("[createAddress] 内层 block 内存分配失败");
            showToastMessage("内存分配失败", 1.0);
            free(serialNumberCopy);
            return;
        }

        LOG("[createAddress] 内层 block 使用 serialNumber: %s", innerSerialNumberCopy);

        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            LOG("[createAddress] 最终传递给 executeCreateAddressLogic 的 serialNumber: %s", innerSerialNumberCopy);
            executeCreateAddressLogic(nil, innerSerialNumberCopy);
            free(innerSerialNumberCopy); // 释放内存
            return;
        });

        free(serialNumberCopy); // 释放外层副本
    });
}

// --------------------------------------------------------------------------------------------------------

// 选择下拉列表中的地址(未优化)
void selectSuggestionFromAddressSuggestionView(id topVC) {
    // id topVC = getTopViewController();
    // if (!topVC) {
    //     LOG("[SuggestionView] 无法获取 topVC");
    //     return;
    // }

    // 获取 _tableView 和 _addressSuggestionView
    Class topVCClass = object_getClass(topVC);
    Ivar tableViewIvar = class_getInstanceVariable(topVCClass, "_tableView");
    // Ivar tableViewIvar = getIvar(topVC, "_tableView");
    id tableView = tableViewIvar ? object_getIvar(topVC, tableViewIvar) : NULL;

    Ivar suggestionViewIvar = class_getInstanceVariable(topVCClass, "_addressSuggestionView");
    id suggestionView = suggestionViewIvar ? object_getIvar(topVC, suggestionViewIvar) : NULL;

    if (!suggestionView) {
        LOG("[SuggestionView] _addressSuggestionView 不存在");
        return;
    }

    const char *suggestionDesc = NULL;
    SEL descSel = sel_registerName("description");
    if (suggestionView && class_respondsToSelector(object_getClass(suggestionView), descSel)) {
        id descObj = ((id (*)(id, SEL))objc_msgSend)(suggestionView, descSel);
        if (descObj) {
            SEL utf8Sel = sel_registerName("UTF8String");
            suggestionDesc = ((const char *(*)(id, SEL))objc_msgSend)(descObj, utf8Sel);
        }
    }
    if (suggestionDesc) {
        LOG("[SuggestionView] 找到 suggestionView: %s", suggestionDesc);
    }

    // 获取 _dataSource
    Class suggestionViewClass = object_getClass(suggestionView);
    Ivar dataSourceIvar = class_getInstanceVariable(suggestionViewClass, "_dataSource");
    id dataSource = dataSourceIvar ? object_getIvar(suggestionView, dataSourceIvar) : NULL;

    SEL countSel = sel_registerName("count");
    SEL respondsSel = sel_registerName("respondsToSelector:");
    if (!dataSource || !((BOOL (*)(id, SEL, SEL))objc_msgSend)(dataSource, respondsSel, countSel)) {
        LOG("[SuggestionView] dataSource 无效或不响应 count");
        return;
    }

    NSUInteger count = ((NSUInteger (*)(id, SEL))objc_msgSend)(dataSource, countSel);
    if (count == 0) {
        LOG("[SuggestionView] dataSource 是空的");
        return;
    }

    NSUInteger maxLen = 0;
    NSInteger selectedIndex = -1;
    id selectedSubTitle = NULL;

    Class NSStringClass = objc_getClass("NSString");
    SEL objectAtIndexSel = sel_registerName("objectAtIndex:");
    SEL isKindOfClassSel = sel_registerName("isKindOfClass:");
    SEL lengthSel = sel_registerName("length");

    for (NSUInteger i = 0; i < count; i++) {
        id item = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(dataSource, objectAtIndexSel, i);
        if (!item) continue;

        Class itemClass = object_getClass(item);
        Ivar subTitleIvar = class_getInstanceVariable(itemClass, "_subTitle");
        id subTitle = subTitleIvar ? object_getIvar(item, subTitleIvar) : NULL;

        if (subTitle && ((BOOL (*)(id, SEL, Class))objc_msgSend)(subTitle, isKindOfClassSel, NSStringClass)) {
            NSUInteger textLength = ((NSUInteger (*)(id, SEL))objc_msgSend)(subTitle, lengthSel);

            const char *subDesc = NULL;
            id subDescObj = ((id (*)(id, SEL))objc_msgSend)(subTitle, descSel);
            if (subDescObj) {
                SEL utf8Sel = sel_registerName("UTF8String");
                subDesc = ((const char *(*)(id, SEL))objc_msgSend)(subDescObj, utf8Sel);
            }
            if (subDesc) {
                LOG("[SuggestionView] [%lu] subTitle = %s", (unsigned long)i, subDesc);
            }

            if (textLength > maxLen) {
                maxLen = textLength;
                selectedIndex = i;
                selectedSubTitle = subTitle;
            }
        }
    }

    if (selectedIndex == -1) {
        LOG("[SuggestionView] 没有找到有效的 subTitle 项");
        return;
    }

    const char *selectedDesc = NULL;
    if (selectedSubTitle) {
        id descObj = ((id (*)(id, SEL))objc_msgSend)(selectedSubTitle, descSel);
        if (descObj) {
            SEL utf8Sel = sel_registerName("UTF8String");
            selectedDesc = ((const char *(*)(id, SEL))objc_msgSend)(descObj, utf8Sel);
        }
    }
    if (selectedDesc) {
        LOG("[SuggestionView] 选择第 %ld 项，subTitle = %s", (long)selectedIndex, selectedDesc);
    }

    // 构造 NSIndexPath
    Class NSIndexPathClass = objc_getClass("NSIndexPath");
    SEL indexPathSel = sel_registerName("indexPathForRow:inSection:");
    id indexPath = ((id (*)(Class, SEL, NSUInteger, NSUInteger))objc_msgSend)(NSIndexPathClass, indexPathSel, selectedIndex, 0);

    // 调用 tableView:didSelectRowAtIndexPath:
    SEL didSelectSel = sel_registerName("tableView:didSelectRowAtIndexPath:");
    if (((BOOL (*)(id, SEL, SEL))objc_msgSend)(suggestionView, respondsSel, didSelectSel)) {
        ((void (*)(id, SEL, id, id))objc_msgSend)(suggestionView, didSelectSel, tableView, indexPath);
        LOG("[SuggestionView] 成功调用 didSelectRowAtIndexPath");
    } else {
        LOG("[SuggestionView] suggestionView 不响应 tableView:didSelectRowAtIndexPath:");
    }
}

// 街道地址: 唤醒键盘，收起键盘，输入内容，清空输入(未优化)
void controlStreetTextView(const char *action, const char *street) {
    id topVC = getTopViewController();
    if (!topVC) {
        return;
    }

    Ivar tableViewIvar = class_getInstanceVariable(object_getClass(topVC), "_tableView");
    id tableView = tableViewIvar ? object_getIvar(topVC, tableViewIvar) : NULL;
    if (!tableView) return;

    // 获取 _visibleCells 数组
    SEL visibleCellsSel = sel_registerName("visibleCells");
    id (*objc_msgSend_id)(id, SEL) = (id (*)(id, SEL))objc_msgSend;
    id cells = objc_msgSend_id(tableView, visibleCellsSel);
    if (!cells) return;

    // 检查 cells 是否为 NSArray 类型
    Class nsArrayClass = objc_getClass("NSArray");
    SEL isKindOfClassSel = sel_registerName("isKindOfClass:");
    BOOL (*objc_msgSend_bool)(id, SEL, Class) = (BOOL (*)(id, SEL, Class))objc_msgSend;
    if (!objc_msgSend_bool(cells, isKindOfClassSel, nsArrayClass)) {
        return;
    }

    // 获取 NSArray 的 count 方法，避免多次调用 sel_registerName
    SEL countSel = sel_registerName("count");
    NSUInteger (*objc_msgSend_count)(id, SEL) = (NSUInteger (*)(id, SEL))objc_msgSend;
    NSUInteger count = objc_msgSend_count(cells, countSel);

    // 遍历 visibleCells
    SEL objectAtIndexSel = sel_registerName("objectAtIndex:");
    id (*objc_msgSend_objAtIdx)(id, SEL, NSUInteger) = (id (*)(id, SEL, NSUInteger))objc_msgSend;

    for (NSUInteger i = 0; i < count; i++) {
        id cell = objc_msgSend_objAtIdx(cells, objectAtIndexSel, i);
        if (!cell) continue;

        // 判断类名是否包含 "BasicInfo"
        Class cellClass = object_getClass(cell);
        const char *className = class_getName(cellClass);
        if (!className) continue;
        if (!strstr(className, "BasicInfo")) continue;

        Ivar streetTextViewIvar = class_getInstanceVariable(cellClass, "_streetTextView");
        if (!streetTextViewIvar) continue;
        id streetTextView = object_getIvar(cell, streetTextViewIvar);
        if (!streetTextView) continue;

        SEL sel = NULL;

        // 对比 action 字符串
        if (strcmp(action, "becomeFirstResponder") == 0) {
            sel = sel_registerName("becomeFirstResponder");
            void (*objc_msgSend_void)(id, SEL) = (void (*)(id, SEL))objc_msgSend;
            objc_msgSend_void(streetTextView, sel);

        } else if (strcmp(action, "resignFirstResponder") == 0) {
            sel = sel_registerName("resignFirstResponder");
            void (*objc_msgSend_void)(id, SEL) = (void (*)(id, SEL))objc_msgSend;
            objc_msgSend_void(streetTextView, sel);

        } else if (strcmp(action, "pressClearButton") == 0) {
            sel = sel_registerName("pressClearButton:");
            void (*objc_msgSend_void_id)(id, SEL, id) = (void (*)(id, SEL, id))objc_msgSend;
            objc_msgSend_void_id(streetTextView, sel, NULL);

        } else if (strcmp(action, "insertText") == 0) {
            sel = sel_registerName("insertText:");
            void (*objc_msgSend_void_id)(id, SEL, id) = (void (*)(id, SEL, id))objc_msgSend;
            // 创建 NSString 对象传入
            Class nsStringClass = objc_getClass("NSString");
            SEL stringWithUTF8Sel = sel_registerName("stringWithUTF8String:");
            id (*objc_msgSend_nsString)(Class, SEL, const char *) = (id (*)(Class, SEL, const char *))objc_msgSend;
            id nsStreet = objc_msgSend_nsString(nsStringClass, stringWithUTF8Sel, street);
            objc_msgSend_void_id(streetTextView, sel, nsStreet);
        }
    }
}


void addressManager(const char *buttonId, const char *serialNumber) {
    if (strcmp(buttonId, "jumpAddressPage") == 0) {
        jumpAddressList();
    } else if (strcmp(buttonId, "delete") == 0) {
        deleteAllVisibleAddresses();
    } else if (strcmp(buttonId, "createAddress") == 0) {
        createAddress(serialNumber);
    }
}