#include <objc/runtime.h>
#include <objc/message.h>
#include "Controller.h"
#include <string.h>
#include "URLScheme.h"
#include <dispatch/dispatch.h>
#include "Log.h"
#include "JSONUtils.h"
#include "CryptoRequest.h"

#include <time.h>
#include <unistd.h>
#include <stdio.h>
#include "/Users/<USER>/Documents/Main/Tweak/Tweaks/JDTools/productInfo.h"

/**
 * 商品操作类型枚举
 */
typedef enum {
    G4WareActionFavorite,
    G4WareActionAddToCart,
    G4WareActionGotoCart,
    G4WareActionCheckout,
    G4WareActionGotoChat
} G4WareActionType;


/**
 * 滚动到商品评论详情区域
 */
void scrollToCommentSemanticTagDetail() {
    id topVC = getTopViewController();
    if (!topVC) {
        LOG("获取 topVC 失败");
        return;
    }

    SEL selector = sel_registerName("scrollToCommentSemanticTagDetailListViewController:");
    if (!((BOOL (*)(id, SEL, SEL))objc_msgSend)(topVC, sel_registerName("respondsToSelector:"), selector)) {
        LOG("topVC 不响应该方法");
        return;
    }

    ((void (*)(id, SEL, id))objc_msgSend)(topVC, selector, nil);
    LOG("方法已调用");
}

/**
 * 执行商品相关操作
 * @param actionType 操作类型
 * @return 操作是否成功
 */
bool performWareAction(G4WareActionType actionType) {
    id topVC = getTopViewController();
    if (!topVC) {
        LOG("获取 topVC 失败");
        return false;
    }

    if (!isKindOfClass(topVC, "WareInfoViewController")) {
        showToastMessage("请先打开商品详情页", 1.0);
        return false;
    }

    const char *selName = NULL;

    switch (actionType) {
        case G4WareActionFavorite:
            selName = "favoriteButtonModelAction:";
            break;
        case G4WareActionAddToCart:
            selName = "addWareToShopCart:";
            break;
        case G4WareActionGotoCart:
            selName = "gotoCheckoutButtonPressed";
            break;
        case G4WareActionCheckout:
            selName = "activityTocheckout:";
            break;
        case G4WareActionGotoChat:
            selName = "realGotoCustomerServicePage";
            break;
        default:
            LOG("不支持的类型: %d", actionType);
            return false;
    }

    SEL sel = sel_registerName(selName);
    if (!sel) {
        LOG("注册 Selector 失败: %s", selName);
        return false;
    }

    BOOL canRespond = ((BOOL (*)(id, SEL, SEL))objc_msgSend)(topVC, sel_registerName("respondsToSelector:"), sel);
    if (!canRespond) {
        LOG("当前 topVC 不响应 %s", selName);
        return false;
    }

    if (actionType == G4WareActionAddToCart) {
        ((void (*)(id, SEL, BOOL))objc_msgSend)(topVC, sel, true);
    } else if (actionType == G4WareActionFavorite || actionType == G4WareActionCheckout) {
        ((void (*)(id, SEL, id))objc_msgSend)(topVC, sel, NULL);
    } else {
        ((void (*)(id, SEL))objc_msgSend)(topVC, sel);
    }

    return true;
}

// 跳转成功回调
typedef void (*ConfirmOrderCallback)(bool success);

// 通过urlsheme跳转到商品详情页 并判断是否风险，成功之后的状态
void jumpWarePageSuccess(const char *skuId, ConfirmOrderCallback callback) {
    // 参数验证
    if (!skuId || strlen(skuId) == 0) {
        LOG("jumpWarePageSuccess: skuId 参数无效");
        if (callback) callback(false);
        return;
    }

    if (!callback) {
        LOG("jumpWarePageSuccess: callback 参数为空");
        return;
    }

    // 创建 skuId 的副本用于异步操作
    char* skuIdCopy = strdup(skuId);
    if (!skuIdCopy) {
        LOG("jumpWarePageSuccess: 内存分配失败");
        callback(false);
        return;
    }

    LOG("jumpWarePageSuccess: 开始跳转到商品页，skuId: %s", skuIdCopy);

    if (!openURLSchemeWarePage(skuIdCopy)) {
        LOG("jumpWarePageSuccess: 跳转失败");
        showToastMessage("载入商品页失败", 2.0);
        free(skuIdCopy);  // 释放副本
        callback(false);
        return;
    }

    // 延时执行，使用副本
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 重新获取页面状态
        id topVC = getTopViewController();
        if (!topVC) {
            LOG("jumpWarePageSuccess: 获取页面控制器失败");
            free(skuIdCopy);  // 释放副本
            callback(false);
            return;
        }

        if (isKindOfClass(topVC, "JDRiskDefaultController")) {
            LOG("jumpWarePageSuccess: 遇到京东风控验证");
            showToastMessage("京东验证", 2.0);
            free(skuIdCopy);  // 释放副本
            callback(false);
            return;
        }

        if (!isKindOfClass(topVC, "WareInfoViewController")) {
            LOG("jumpWarePageSuccess: 不是商品详情页");
            showToastMessage("不是商品详情页", 2.0);
            free(skuIdCopy);  // 释放副本
            callback(false);
            return;
        }

        LOG("jumpWarePageSuccess: 成功跳转到商品详情页");
        free(skuIdCopy);  // 释放副本
        callback(true);
    });
}

// 判断是否无货
bool isOutStock(id topVC) {
    if (!topVC) {
        return false;
    }
    // Class topVCClass = object_getClass(topVC);
    
    // Ivar ivar = class_getInstanceVariable(topVCClass, "_outStockView");
    id outStockView = getIvar(topVC, "_outStockView");
    if (!outStockView) {
        return false;
    }

    id outStockNewPopView = getIvar(outStockView, "_outStockNewPopView");
    if (!outStockNewPopView) {
        return false;
    }

    id topView = getIvar(outStockNewPopView, "_topView");
    if (!topView) {
        return false;
    }

    id addressBtn = getIvar(topView, "_addressBtn");
    if (!addressBtn) {
        return false;
    }

    id titleView = getIvar(addressBtn, "_titleView");
    if (!titleView) {
        return false;
    }

    // 读取按钮文本
    SEL textSel = sel_registerName("text");
    id text = ((id (*)(id, SEL))objc_msgSend)(titleView, textSel);
    if (text) {
        SEL utf8Sel = sel_registerName("UTF8String");
        if (((BOOL (*)(id, SEL, SEL))objc_msgSend)(text, sel_registerName("respondsToSelector:"), utf8Sel)) {
            const char *cstr = ((const char * (*)(id, SEL))objc_msgSend)(text, utf8Sel);
            if (cstr) {
                showToastMessage(cstr, 2.0);
                return true;
            }
        }
    }
    // LOG("按钮文本: %s", cstr);
    return false;
}


// 跳转商品成功回调执行函数
void  my_callback(bool success) {
    if (success) {
        LOG("✅ 成功跳转到商品详情页");
        showToastMessage("等待2秒数据加载", 2.0);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 重新获取页面状态并验证
            id topVC = getTopViewController();
            if (!topVC) {
                LOG("my_callback: 获取页面控制器失败");
                showToastMessage("获取页面控制器失败", 2.0);
                return;
            }

            // 验证是否仍在商品详情页
            if (!isKindOfClass(topVC, "WareInfoViewController")) {
                LOG("my_callback: 页面已改变，不再是商品详情页");
                showToastMessage("页面状态已改变", 2.0);
                return;
            }

            // 检查是否无货
            if (isOutStock(topVC)) {
                LOG("my_callback: 商品无货或地区限购");
                showToastMessage("商品无货或地区限购", 2.0);
                return;
            }

            // 去结算
            LOG("my_callback: 开始执行去结算操作");
            if (!performWareAction(G4WareActionCheckout)) {
                LOG("my_callback: 去结算操作失败");
                showToastMessage("去结算失败", 2.0);
                return;
            } else {
                LOG("my_callback: 去结算点击成功");
                showToastMessage("去结算成功", 1.0);
            }
        });
    } else {
        LOG("❌ 跳转到商品详情页失败");
    }
}

void orderBuffer(const char *serialNumber) {
    // 参数验证
    if (!serialNumber || strlen(serialNumber) == 0) {
        LOG("orderBuffer: serialNumber 参数无效");
        showToastMessage("设备序列号无效", 2.0);
        return;
    }

    LOG("orderBuffer: 开始获取垫单sku，serialNumber: %s", serialNumber);

    // 构建请求配置
    CryptoRequestConfig config = {
        .path = "/tools/v1",
        .action = "buffersku",
        .serial_number = serialNumber,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);
    if (!result.success) {
        showToastMessage("服务器请求失败", 2.0);
        crypto_request_free_result(&result);
        return;
    }
    if (!result.response_data) {
        showToastMessage("服务器响应数据为空", 2.0);
        crypto_request_free_result(&result);
        return;
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        showToastMessage("服务器响应数据为空", 2.0);
        crypto_request_free_result(&result);
        return;
    }
    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        const char* error = message ? message : "未知错误";
        showToastMessage(error, 2.0);
        crypto_request_free_result(&result);
        return;
    }
    cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!data) {
        showToastMessage("服务器响应数据为空", 2.0);
        crypto_request_free_result(&result);
        return;
    }
    bool status = JSONUtils_GetBoolValue(data, "status", false);
    if (!status) {
        showToastMessage("缺少垫单sku，请及时上传", 2.0);
        crypto_request_free_result(&result);
        return;
    }
    const char* skuId = JSONUtils_GetStringValue(data, "skuId");
    if (!skuId || strlen(skuId) == 0) {
        showToastMessage("返回垫单sku异常", 2.0);
        crypto_request_free_result(&result);
        return;
    }

    // 创建 skuId 的安全副本，避免野指针
    char* skuIdCopy = strdup(skuId);
    if (!skuIdCopy) {
        LOG("orderBuffer: 内存分配失败，无法复制 skuId");
        showToastMessage("内存分配失败", 2.0);
        crypto_request_free_result(&result);
        return;
    }

    LOG("orderBuffer: 获取到 skuId: %s", skuIdCopy);
    showToastMessage(skuIdCopy, 1.0);

    // 先释放 JSON 资源
    crypto_request_free_result(&result);

    // 使用安全的副本调用异步函数
    jumpWarePageSuccess(skuIdCopy, my_callback);

    // 注意：skuIdCopy 的内存会在 jumpWarePageSuccess 内部处理
}



/**
 * 商品管理器主入口函数
 * @param buttonId 按钮标识符
 * @param serialNumber 设备序列号
 * @return 操作是否成功
 */
bool wareManager(const char *buttonId, const char *serialNumber) {
    if (strcmp(buttonId, "addToCart") == 0) {
        return performWareAction(G4WareActionAddToCart);
    // } else if (strcmp(buttonId, "gotoCart") == 0) {
        // return performWareAction(G4WareActionGotoCart);
    // } else if (strcmp(buttonId, "gotoChat") == 0) {
        // return performWareAction(G4WareActionGotoChat);
    } else if (strcmp(buttonId, "checkout") == 0) {
        return performWareAction(G4WareActionCheckout);
    } else if (strcmp(buttonId, "favorite") == 0) {
        return performWareAction(G4WareActionFavorite);
    } else if (strcmp(buttonId, "comments") == 0) {
        scrollToCommentSemanticTagDetail();
    } else if (strcmp(buttonId, "orderBuffer") == 0) {
        // 执行自动垫单操作
        // openURLSchemeWarePage("100114777752");
        orderBuffer(serialNumber);
    }
    return true;
}