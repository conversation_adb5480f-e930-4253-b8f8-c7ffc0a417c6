#ifndef CONTROLLER_H
#define CONTROLLER_H

#include <stdbool.h>
#include <objc/runtime.h>
#include <objc/message.h>

/**
 * @brief 复制字符串到新的内存空间
 * @param source 源字符串，不能为NULL
 * @return 新分配的字符串副本，调用者负责释放内存
 * @note 使用malloc分配内存，调用者必须使用free()释放返回的指针
 * @warning 如果source为NULL或内存分配失败，返回NULL
 */
char* copy_string(const char* source);

/**
 * @brief 获取当前应用的顶层视图控制器
 * @return 顶层视图控制器对象，如果获取失败返回nil
 * @note 通过UIApplication的keyWindow获取rootViewController，然后递归查找最顶层的控制器
 * @warning 必须在主线程调用，否则可能返回nil
 */
id getTopViewController(void);

/**
 * @brief 获取对象的类名
 * @param viewController 目标对象，可以为nil
 * @return 类名字符串，调用者负责释放内存；如果对象为nil返回NULL
 * @note 使用NSStringFromClass获取类名，然后转换为C字符串
 * @warning 返回的字符串需要调用者使用free()释放
 */
char* getClassName(id viewController);

/**
 * @brief 模拟点击当前视图控制器的返回按钮
 * @note 尝试调用navigationController的popViewController方法
 * @warning 如果当前控制器没有导航控制器或不在导航栈中，此方法无效果
 */
void callBackButtonClicked(void);

/**
 * @brief 检查目标对象是否响应指定的选择器
 * @param target 目标对象，不能为nil
 * @param sel_name 选择器名称字符串，不能为NULL
 * @return 如果对象响应该选择器则返回对应的SEL，否则返回NULL
 * @note 使用respondsToSelector:方法进行检查
 * @warning 传入无效的target或sel_name会导致返回NULL
 */
SEL selectorIfRespondsTo(id target, const char *sel_name);

/**
 * @brief 获取对象的实例变量值
 * @param target 目标对象，不能为nil
 * @param ivar_name 实例变量名称，不能为NULL
 * @return 实例变量的值，如果获取失败返回nil
 * @note 使用runtime的object_getIvar函数获取实例变量
 * @warning 只能获取对象类型的实例变量，基本类型需要特殊处理
 */
id getIvar(id target, const char *ivar_name);

/**
 * @brief 获取视图控制器的字符串类型属性值
 * @param viewController 目标视图控制器，不能为nil
 * @param varName 属性名称，不能为NULL
 * @return 属性值的C字符串表示，如果获取失败返回NULL
 * @note 通过KVC方式获取属性值，然后转换为字符串
 * @warning 返回的字符串指向对象内部存储，不要修改或释放
 */
const char* getVar(id viewController, const char *varName);

/**
 * @brief 检查对象是否为指定类的实例
 * @param obj 要检查的对象，可以为nil
 * @param className 类名字符串，不能为NULL
 * @return 如果对象是指定类的实例返回true，否则返回false
 * @note 使用isKindOfClass:方法进行检查，支持继承关系
 * @warning 如果obj为nil或className无效，返回false
 */
bool isKindOfClass(id obj, const char *className);

/**
 * @brief 显示模态对话框消息
 * @param message 要显示的消息内容，不能为NULL
 * @param duration 显示持续时间（秒），0表示需要用户手动关闭
 * @note 在主线程显示UIAlertController，会阻塞用户交互直到关闭
 * @warning 必须在主线程调用，message不能为NULL
 */
void showDialogMessage(const char *message, double duration);

/**
 * @brief 显示Toast提示消息
 * @param message 要显示的消息内容，不能为NULL
 * @param duration 显示持续时间（秒），建议1.0-3.0秒
 * @note 显示轻量级的提示消息，不会阻塞用户交互，自动消失
 * @warning 必须在主线程调用，message不能为NULL
 */
void showToastMessage(const char *message, double duration);

/**
 * @brief 输入操作结果结构体
 * @note 用于返回文本输入操作的结果和错误信息
 */
typedef struct {
    bool status;         ///< 操作状态：true表示成功，false表示失败
    char* error;         ///< 错误信息字符串，成功时为NULL，失败时包含错误描述
} InputResult;

/**
 * @brief 向当前焦点输入框输入文本
 * @param text 要输入的文本内容，不能为NULL
 * @param fromClipboard 是否从剪贴板输入：true表示先复制到剪贴板再粘贴，false表示直接输入
 * @return InputResult结构体，包含操作状态和错误信息
 * @note 自动查找当前第一响应者（输入框），然后输入文本
 * @warning 调用者必须使用freeInputTextResult()释放返回结果的内存
 */
InputResult handleInpuText(const char* text, bool fromClipboard);

/**
 * @brief 释放InputResult结构体的内存资源
 * @param result 要释放的InputResult指针，可以为NULL
 * @note 释放result->error指向的内存，但不释放result本身
 * @warning 传入NULL指针是安全的，不会产生错误
 */
void freeInputTextResult(InputResult* result);

/**
 * @brief 将文本写入iOS系统剪贴板
 * @param text 要写入剪贴板的文本，不能为NULL
 * @note 使用UIPasteboard.generalPasteboard设置文本内容
 * @warning 必须在主线程调用，text不能为NULL
 */
void setClipboardTextIOS(const char *text);

/**
 * @brief 从iOS系统剪贴板读取文本
 * @return 剪贴板中的文本内容，如果剪贴板为空返回NULL
 * @note 使用UIPasteboard.generalPasteboard获取文本内容
 * @warning 必须在主线程调用，返回的字符串需要调用者使用free()释放
 */
char *getClipboardTextIOS(void);

/**
 * @brief 处理京东OAuth认证管理
 * @param controllerClassName 当前视图控制器的类名，不能为NULL
 * @return 认证是否成功：true表示成功，false表示失败
 * @note 根据当前控制器类型执行相应的OAuth认证流程
 * @warning controllerClassName必须是有效的类名字符串
 */
bool jdOAuthManager(const char* controllerClassName);

/**
 * @brief 用户管理结果结构体
 * @note 用于返回用户Cookie获取操作的结果
 */
typedef struct {
    bool status;         ///< 操作状态：true表示成功获取，false表示失败
    char* pin;           ///< 用户PIN，成功时包含用户标识，失败时为NULL
    char* wskey;         ///< WebSocket密钥，成功时包含认证密钥，失败时为NULL
    char* error;         ///< 错误信息，成功时为NULL，失败时包含错误描述
} UserManager;

/**
 * @brief 获取当前用户的Cookie信息
 * @return UserManager结构体，包含用户PIN、wskey和操作状态
 * @note 从京东应用中提取用户的认证信息，包括PIN和WebSocket密钥
 * @warning 调用者必须使用freeCookieResult()释放返回结果的内存
 * @see freeCookieResult()
 */
UserManager getCookie(void);

/**
 * @brief 释放UserManager结构体的内存资源
 * @param result 要释放的UserManager指针，可以为NULL
 * @note 释放result->pin、result->wskey和result->error指向的内存，但不释放result本身
 * @warning 传入NULL指针是安全的，不会产生错误
 * @see getCookie()
 */
void freeCookieResult(UserManager* result);

// 订单列表 
// bool openURLSchemeOrderList(int listType);

// 打开评价中心
// bool openURLSchemeCommontCenter();

void addressManager(const char *buttonId, const char *serialNumber);
void searchManager(const char *buttonId, const char *serialNumber, const char *keyword, const char *wareId, const char *buyCount);
bool wareManager(const char *buttonId, const char *serialNumber);
bool orderManager(const char *buttonId, const char *serialNumber);
bool loginManager(const char *buttonId, const char *serialNumber);
bool paymentManager(const char *buttonId, const char *serialNumber);
#endif /* CONTROLLER_H */
