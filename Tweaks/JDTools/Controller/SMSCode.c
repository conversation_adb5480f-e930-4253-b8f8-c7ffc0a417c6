#include <objc/runtime.h>
#include <objc/message.h>
#include "Log.h"
#include <stdbool.h>
#include <dispatch/dispatch.h>
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <unistd.h>
#include <objc/NSObjCRuntime.h>
#include <regex.h>
#include <sys/time.h>
#include "Controller.h"
#include "SocketHTTP.h"

// 全局控制标志 - 简单直接
static volatile bool g_should_stop = false;

// 自动根据页面输入验证码
// 短信登录页面 验证码
// 18:34:26.874034+0800	JD4iPhone	 topVC 类名: JDPhoneLoginFirstViewController
// 18:35:16.648521+0800	JD4iPhone	 topVC 类名: JDPhoneLoginFirstViewController

// 新用户注册
// 18:35:59.258779+0800	JD4iPhone	 topVC 类名: JDRegisterVerifyMessageCodeViewController
// 18:36:35.154156+0800	JD4iPhone	 topVC 类名: JDRegisterVerifyMessageCodeViewController

// 自动填入验证码
void accessCaptchaTextField(const char *smsCode) {
    id topVC = getTopViewController();
    if (!topVC) {
        showToastMessage("页面状态获取失败，请手动粘贴验证码", 2.0);
        return;
    }
    if (!isKindOfClass(topVC, "JDPhoneLoginFirstViewController") && !isKindOfClass(topVC, "JDRegisterVerifyMessageCodeViewController")) {
        // showToastMessage("不是验证码页面，无法自动输入", 2.0);
        return;
    }
    // 打印topVC的类名
    // showToastMessage("尝试自动输入验证码", 1.0);

    id messageCodeView = getIvar(topVC, "_messageCodeView");
    if (!messageCodeView) {
        LOG(" 获取 _messageCodeView 失败");
        return;
    }

    id captchaTextField = getIvar(messageCodeView, "_captchaTextField");
    if (!captchaTextField) {
        LOG(" 获取 _captchaTextField 失败");
        return;
    }

    SEL setTextSel = sel_registerName("insertText:");
    id text = ((id (*)(Class, SEL, const char *))objc_msgSend)(objc_getClass("NSString"),
                                                               sel_registerName("stringWithUTF8String:"),
                                                               smsCode);
    ((void (*)(id, SEL, id))objc_msgSend)(captchaTextField, setTextSel, text);

    LOG(" 已设置验证码字段为: %s", smsCode);

}


// 提取6位数验证码的辅助函数
char* extractVerificationCode(const char* text) {
    if (!text) {
        LOG("输入文本为空");
        return NULL;
    }

    LOG("开始提取验证码，文本: %s", text);

    regex_t regex;
    int ret = regcomp(&regex, "[0-9]{6}", REG_EXTENDED);
    if (ret != 0) {
        LOG("正则表达式编译失败");
        return NULL;
    }

    regmatch_t match;
    ret = regexec(&regex, text, 1, &match, 0);

    char* result = NULL;
    if (ret == 0) {
        int code_length = match.rm_eo - match.rm_so;
        if (code_length == 6) {
            result = malloc(7);
            if (result) {
                strncpy(result, text + match.rm_so, 6);
                result[6] = '\0';
                LOG("提取到验证码: %s", result);
            } else {
                LOG("内存分配失败");
            }
        } else {
            LOG("匹配长度不正确: %d", code_length);
        }
    } else {
        LOG("未找到6位数验证码");
    }

    regfree(&regex);
    return result;
}

// 停止当前请求
void cancelVerificationCodeRequest() {
    g_should_stop = true;
    LOG("停止当前请求");
}

// 检查是否有活跃的验证码请求
bool isVerificationCodeRequestActive() {
    return !g_should_stop;
}

// 获取验证码 - 抢占式执行
char* getVerificationCodeFromURL(const char* url, int timeout_seconds, int interval_seconds) {
    if (!url) return NULL;

    // ✅ 复制URL字符串，确保在整个函数执行期间安全
    char* url_copy = strdup(url);
    if (!url_copy) {
        LOG("URL复制失败");
        return NULL;
    }

    if (timeout_seconds <= 0) timeout_seconds = 60;
    if (interval_seconds <= 0) interval_seconds = 1;

    // 立即停止任何正在执行的请求
    g_should_stop = true;
    LOG("停止旧请求，开始新请求");

    // 短暂等待确保之前的请求停止
    usleep(100000); // 100ms

    // 重置标志，开始新请求
    g_should_stop = false;

    LOG("开始执行: %s", url_copy);

    time_t start_time = time(NULL);
    int count = 0;
    (void)count;

    while (!g_should_stop) {
        count++;

        // 被新请求抢占，立即退出
        if (g_should_stop) {
            LOG("被新请求抢占，退出");
            free(url_copy);
            return NULL;
        }

        // 超时检查
        if (time(NULL) - start_time >= timeout_seconds) {
            LOG("超时退出");
            break;
        }

        LOG("第 %d 次请求", count);

        // 记录请求开始时间
        struct timeval request_start, request_end;
        gettimeofday(&request_start, NULL);

        // 发起HTTP请求
        char* response = NULL;
        int statusCode = 0;
        bool success = socket_get_request_sync(url_copy, NULL, &response, &statusCode);

        // 记录请求结束时间并计算耗时
        gettimeofday(&request_end, NULL);
        long request_time_ms = (request_end.tv_sec - request_start.tv_sec) * 1000 +
                              (request_end.tv_usec - request_start.tv_usec) / 1000;
        (void)request_time_ms;
        LOG("第 %d 次请求完成，耗时: %ld ms，状态码: %d",
               count, request_time_ms, statusCode);

        // HTTP请求完成后立即检查是否被抢占
        if (g_should_stop) {
            LOG("HTTP请求完成后被抢占");
            if (response) free(response);
            free(url_copy);
            return NULL;
        }

        if (success && response) {
            
            LOG("响应内容: %s", response);

            // 解析 "yes|123456" 或 "no|waiting" 格式
            char* separator = strchr(response, '|');
            if (separator) {
                *separator = '\0'; // 分割字符串
                char* status = response;
                char* content = separator + 1;

                if (strcmp(status, "yes") == 0) {
                    char* code = extractVerificationCode(content);
                    if (code) {
                        LOG("✅ 成功获取验证码: %s (总耗时: %ld ms)",
                               code, request_time_ms);
                        free(response);
                        free(url_copy);
                        return code; // 成功返回
                    } else {
                        LOG("❌ 状态为yes但未能提取验证码 (耗时: %ld ms)",
                               request_time_ms);
                    }
                } else {
                    showToastMessage(content, 2.0);
                    LOG("⏳ 状态为: %s，继续等待 (耗时: %ld ms)",
                           status, request_time_ms);
                }
            } else {
                LOG("❌ 响应格式错误，未找到分隔符 '|' (耗时: %ld ms)",
                       request_time_ms);
            }
            free(response);
        } else {
            LOG("❌ 请求失败 (耗时: %ld ms)", request_time_ms);
            if (response) free(response);
        }

        // 等待间隔时间，期间检查是否被抢占
        for (int i = 0; i < interval_seconds * 10; i++) {
            if (g_should_stop) {
                LOG("等待期间被抢占");
                free(url_copy);
                return NULL;
            }
            usleep(100000); // 100ms
        }
    }

    LOG("结束，共 %d 次请求", count);
    free(url_copy);
    return NULL;
}

// 高级封装函数：自动获取验证码并处理结果
// url: 验证码获取URL
// timeout: 超时时间（秒），默认60秒
// interval: 请求间隔（秒），默认2秒
// 返回值: 成功返回true，失败返回false
bool fetchVerificationCodeAsync(const char* url, int timeout, int interval) {
    if (!url) {
        LOG("URL为空");
        showToastMessage("URL参数错误", 2.0);
        return false;
    }

    // 验证URL格式
    if (strncmp(url, "http://", 7) != 0 && strncmp(url, "https://", 8) != 0) {
        LOG("URL格式错误: %s", url);
        showToastMessage("URL格式错误，必须以http://或https://开头", 2.0);
        return false;
    }

    // 检查URL长度
    size_t url_len = strlen(url);
    if (url_len < 10 || url_len > 2048) {
        LOG("URL长度异常: %zu", url_len);
        showToastMessage("URL长度异常", 2.0);
        return false;
    }

    // 设置默认参数
    if (timeout <= 0) timeout = 60;
    if (interval <= 0) interval = 2;

    LOG("开始异步获取验证码");
    LOG("URL: %s, 超时: %d秒, 间隔: %d秒", url, timeout, interval);

    showToastMessage("开始读取验证码", 1.0);

    // 先取消任何正在进行的验证码请求
    cancelVerificationCodeRequest();

    // 在后台线程执行验证码获取
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        char* code = getVerificationCodeFromURL(url, timeout, interval);

        // 回到主线程处理结果
        dispatch_async(dispatch_get_main_queue(), ^{
            if (code) {
                LOG("✅ 成功获取验证码: %s", code);

                // 显示成功消息
                char successMsg[256];
                snprintf(successMsg, sizeof(successMsg), "✅ 验证码: %s", code);
                showToastMessage(successMsg, 2.0);

                // 如果获取验证码之后就尝试自动填入验证码，会判断是否在标准的输入验证码页面。
                // 不管成功还是失败都需要将验证码写入粘贴板
                accessCaptchaTextField(code);

                // 将验证码写入剪贴板
                setClipboardTextIOS(code);

                free(code);
            } else {
                LOG("❌ 获取验证码失败或被取消");
                showToastMessage("获取验证码失败/超时", 2.0);
            }
        });
    });

    return true;
}
