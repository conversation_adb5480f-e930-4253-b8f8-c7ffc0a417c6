#include "Controller.h"
#include "Log.h"
#include <objc/runtime.h>
#include <objc/message.h>
#include <dispatch/dispatch.h>

// 使用静态变量缓存 ToastManager 类
// 确保它被初始化为 NULL，以便首次检查
static Class _cachedToastManagerClass = NULL;
/**
 * @brief 显示对话框消息
 *
 * 在主线程中显示对话框消息，确保内存安全
 *
 * @param message 要显示的消息文本，不能为NULL
 * @param duration 显示持续时间（秒）
 */
void showToastMessage(const char *message, double duration) {
    if (!message) {
        LOG("消息参数为空");
        return;
    }

    LOG("showToastMessage: %s", message);

    // 创建消息的安全副本，避免异步执行时内存失效
    size_t messageLen = strlen(message);
    char *messageCopy = (char *)malloc(messageLen + 1);
    if (!messageCopy) {
        LOG("内存分配失败");
        return;
    }
    strcpy(messageCopy, message);


    dispatch_async(dispatch_get_main_queue(), ^{
        // 第一次调用时获取并缓存 ToastManager 类
        if (!_cachedToastManagerClass) {
            _cachedToastManagerClass = objc_getClass("Toast");
            if (!_cachedToastManagerClass) {
                LOG("ToastManager类未找到");
                free(messageCopy);
                return;
            }
        }

        // 使用缓存的类
        Class ToastManagerClass = _cachedToastManagerClass;

        // 构造 NSString *message
        // 同样，NSStringClass 也可以被缓存，但它是一个非常常见的类，通常性能影响可以忽略
        SEL strSel = sel_registerName("stringWithUTF8String:");
        Class NSStringClass = (Class)objc_getClass("NSString");
        if (!NSStringClass) {
            LOG("NSString类未找到");
            free(messageCopy);
            return;
        }
        


        // 获取 show:duration: 方法
        SEL showSel = sel_registerName("show:duration:");

        id nsMessage = ((id (*)(id, SEL, const char *))objc_msgSend)((id)NSStringClass, strSel, messageCopy);
        if (!nsMessage) {
            LOG("NSString创建失败");
            free(messageCopy);
            return;
        }

        LOG("NSString创建成功: %p", nsMessage);
        // 调用 [ToastManager show:message duration:duration]
        ((void (*)(id, SEL, id, double))objc_msgSend)((id)ToastManagerClass, showSel, nsMessage, duration);

        LOG("对话框显示调用完成");

        // 释放内存
        free(messageCopy);
    });
}