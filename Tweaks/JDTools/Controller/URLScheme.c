// URLScheme.m
#include "Log.h"
#include <objc/runtime.h>
#include <objc/message.h>
#include "Controller.h"
#include <stdio.h>
#include <string.h>
#include "Base64.h"


// URL编码字符映射表 - 修复线程安全问题
static void url_encode_char(char c, char* output, size_t output_size) {
    if (!output || output_size < 4) {
        LOG("输出缓冲区无效");
        return;
    }

    switch (c) {
        case ' ': strcpy(output, "%20"); break;
        case '!': strcpy(output, "%21"); break;
        case '"': strcpy(output, "%22"); break;
        case '#': strcpy(output, "%23"); break;
        case '$': strcpy(output, "%24"); break;
        case '%': strcpy(output, "%25"); break;
        case '&': strcpy(output, "%26"); break;
        case '\'': strcpy(output, "%27"); break;
        case '(': strcpy(output, "%28"); break;
        case ')': strcpy(output, "%29"); break;
        case '*': strcpy(output, "%2A"); break;
        case '+': strcpy(output, "%2B"); break;
        case ',': strcpy(output, "%2C"); break;
        case '/': strcpy(output, "%2F"); break;
        case ':': strcpy(output, "%3A"); break;
        case ';': strcpy(output, "%3B"); break;
        case '<': strcpy(output, "%3C"); break;
        case '=': strcpy(output, "%3D"); break;
        case '>': strcpy(output, "%3E"); break;
        case '?': strcpy(output, "%3F"); break;
        case '@': strcpy(output, "%40"); break;
        case '[': strcpy(output, "%5B"); break;
        case '\\': strcpy(output, "%5C"); break;
        case ']': strcpy(output, "%5D"); break;
        case '^': strcpy(output, "%5E"); break;
        case '`': strcpy(output, "%60"); break;
        case '{': strcpy(output, "%7B"); break;
        case '|': strcpy(output, "%7C"); break;
        case '}': strcpy(output, "%7D"); break;
        case '~': strcpy(output, "%7E"); break;
        default:
            if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || (c >= '0' && c <= '9') ||
                c == '-' || c == '_' || c == '.' || c == '~') {
                output[0] = c;
                output[1] = '\0';
            } else {
                snprintf(output, output_size, "%%%02X", (unsigned char)c);
            }
            break;
    }
}

// URL编码函数 - 修复内存管理和线程安全问题
char* url_encode(const char* input) {
    if (!input) {
        LOG("输入字符串为 null");
        return NULL;
    }

    size_t input_len = strlen(input);
    if (input_len == 0) {
        LOG("输入字符串为空");
        char* result = malloc(1);
        if (result) {
            result[0] = '\0';
        } else {
            LOG("空字符串内存分配失败");
        }
        return result;
    }

    // 预估最大长度（每个字符最多编码为3个字符）
    size_t max_len = input_len * 3 + 1;
    char* result = malloc(max_len);
    if (!result) {
        LOG("内存分配失败，请求大小: %zu", max_len);
        return NULL;
    }

    result[0] = '\0';
    size_t result_len = 0;
    char encoded_char[4]; // 线程安全的局部缓冲区

    for (size_t i = 0; i < input_len; i++) {
        // 使用线程安全的编码函数
        url_encode_char(input[i], encoded_char, sizeof(encoded_char));
        size_t encoded_len = strlen(encoded_char);

        // 检查缓冲区是否足够（留出终止符空间）
        if (result_len + encoded_len >= max_len) {
            LOG("缓冲区溢出风险，当前长度: %zu，需要: %zu，最大: %zu",
                   result_len, encoded_len, max_len);
            free(result);
            return NULL;
        }

        // 安全地追加编码字符
        strncat(result, encoded_char, max_len - result_len - 1);
        result_len += encoded_len;
    }

    LOG("编码完成，原长度: %zu，编码后长度: %zu", input_len, result_len);
    return result;
}

bool openURLScheme(const char *params) {
    LOG("开始打开URL Scheme");

    // 验证输入参数
    if (!params) {
        LOG("params 参数为 null");
        return false;
    }

    if (strlen(params) == 0) {
        LOG("params 参数为空字符串");
        return false;
    }

    // 检查参数长度，避免缓冲区溢出
    size_t params_len = strlen(params);
    const size_t max_url_len = 2048; // 设置合理的URL最大长度
    const char* url_prefix = "openapp.jdmobile://virtual?params=";
    size_t prefix_len = strlen(url_prefix);

    if (params_len + prefix_len >= max_url_len) {
        LOG("URL长度超出限制: %zu", params_len + prefix_len);
        return false;
    }

    // 动态分配URL缓冲区
    char* fullURL = malloc(params_len + prefix_len + 1);
    if (!fullURL) {
        LOG("URL缓冲区内存分配失败");
        return false;
    }

    snprintf(fullURL, params_len + prefix_len + 1, "%s%s", url_prefix, params);
    LOG("完整URL: %s", fullURL);

    // 获取 JDBOpenURLModule 类
    Class cls = objc_getClass("JDBOpenURLModule");
    if (!cls) {
        LOG("类 JDBOpenURLModule 不存在");
        free(fullURL);
        return false;
    }

    // 获取方法选择子
    SEL sel = sel_registerName("application:openURL:sourceApplication:annotation:");
    if (!sel) {
        LOG("无法注册方法选择子");
        free(fullURL);
        return false;
    }

    // 获取元类判断类方法是否存在
    Class metaClass = object_getClass((id)cls);
    if (!metaClass) {
        LOG("无法获取元类");
        free(fullURL);
        return false;
    }

    if (!class_respondsToSelector(metaClass, sel)) {
        LOG("类方法不响应选择子: application:openURL:sourceApplication:annotation:");
        free(fullURL);
        return false;
    }

    // 创建 NSString *urlString
    Class NSStringCls = objc_getClass("NSString");
    if (!NSStringCls) {
        LOG("无法获取 NSString 类");
        free(fullURL);
        return false;
    }

    SEL stringWithUTF8Sel = sel_registerName("stringWithUTF8String:");
    id nsStr = ((id (*)(Class, SEL, const char *))objc_msgSend)(NSStringCls, stringWithUTF8Sel, fullURL);
    if (!nsStr) {
        LOG("创建 NSString 失败");
        free(fullURL);
        return false;
    }

    // 创建 NSURL *url
    Class NSURLCls = objc_getClass("NSURL");
    if (!NSURLCls) {
        LOG("无法获取 NSURL 类");
        free(fullURL);
        return false;
    }

    SEL URLWithStringSel = sel_registerName("URLWithString:");
    id nsUrl = ((id (*)(Class, SEL, id))objc_msgSend)(NSURLCls, URLWithStringSel, nsStr);
    if (!nsUrl) {
        LOG("创建 NSURL 失败");
        free(fullURL);
        return false;
    }

    // 获取 [UIApplication sharedApplication]
    Class UIApplicationCls = objc_getClass("UIApplication");
    if (!UIApplicationCls) {
        LOG("无法获取 UIApplication 类");
        free(fullURL);
        return false;
    }

    SEL sharedAppSel = sel_registerName("sharedApplication");
    id sharedApp = ((id (*)(Class, SEL))objc_msgSend)(UIApplicationCls, sharedAppSel);
    if (!sharedApp) {
        LOG("获取 sharedApplication 失败");
        free(fullURL);
        return false;
    }

    // 调用类方法 +application:openURL:sourceApplication:annotation:
    BOOL result = ((BOOL (*)(id, SEL, id, id, id, id))objc_msgSend)((id)cls, sel, sharedApp, nsUrl, nil, nil);

    // 清理内存
    free(fullURL);
    return result;
}

// bool openURLSchemeOrderList(const char *listType) {

//     // 验证输入参数
//     if (!listType) {
//         LOG("[openURLSchemeOrderList] listType 为 null，使用默认值 '1'");
//         listType = "1"; // 默认显示全部订单
//     }

//     if (strlen(listType) == 0) {
//         LOG("[openURLSchemeOrderList] listType 为空字符串，使用默认值 '1'");
//         listType = "1";
//     }


//     LOG("[openURLSchemeOrderList] 使用 listType: %s", listType);

//     // 构建JSON参数字符串
//     // 预估长度：基础字符串 + listType 长度
//     const char* json_template = "{\"des\":\"orderlist\",\"category\":\"jump\",\"listType\":\"%s\"}";
//     size_t json_len = strlen(json_template) + strlen(listType) + 1;

//     char* params = malloc(json_len);
//     if (!params) {
//         LOG("[openURLSchemeOrderList] JSON参数内存分配失败");
//         return false;
//     }

//     int ret = snprintf(params, json_len, json_template, listType);
//     if (ret < 0 || (size_t)ret >= json_len) {
//         LOG("[openURLSchemeOrderList] JSON参数构建失败");
//         free(params);
//         return false;
//     }

//     LOG("[openURLSchemeOrderList] JSON参数: %s", params);

//     // URL编码
//     char* encoded_params = url_encode(params);
//     free(params); // 释放原始JSON字符串

//     if (!encoded_params) {
//         LOG("[openURLSchemeOrderList] URL编码失败");
//         return false;
//     }

//     LOG("[openURLSchemeOrderList] 编码后参数: %s", encoded_params);

//     // 打开URL
//     bool result = openURLScheme(encoded_params);
//     free(encoded_params); // 释放编码后的字符串

//     LOG("[openURLSchemeOrderList] 订单列表打开结果: %s", result ? "成功" : "失败");
//     return result;
// }

bool openURLSchemeOrderList(int listType) {

    // 如果传入的是非法值，可以在这里判断并给默认值
    if (listType <= 0) {
        LOG("[openURLSchemeOrderList] listType <= 0，使用默认值 1");
        listType = 1;
    }

    LOG("[openURLSchemeOrderList] 使用 listType: %d", listType);

    // 构建JSON参数字符串
    const char* json_template = "{\"des\":\"orderlist\",\"category\":\"jump\",\"listType\":\"%d\"}";
    size_t json_len = snprintf(NULL, 0, json_template, listType) + 1;  // 计算长度

    char* params = malloc(json_len);
    if (!params) {
        LOG("[openURLSchemeOrderList] JSON参数内存分配失败");
        return false;
    }

    int ret = snprintf(params, json_len, json_template, listType);
    if (ret < 0 || (size_t)ret >= json_len) {
        LOG("[openURLSchemeOrderList] JSON参数构建失败");
        free(params);
        return false;
    }

    LOG("[openURLSchemeOrderList] JSON参数: %s", params);

    // URL编码
    char* encoded_params = url_encode(params);
    free(params); // 释放原始JSON字符串

    if (!encoded_params) {
        LOG("[openURLSchemeOrderList] URL编码失败");
        return false;
    }

    LOG("[openURLSchemeOrderList] 编码后参数: %s", encoded_params);

    // 打开URL
    bool result = openURLScheme(encoded_params);
    free(encoded_params); // 释放编码后的字符串

    LOG("[openURLSchemeOrderList] 订单列表打开结果: %s", result ? "成功" : "失败");
    return result;
}


bool openURLSchemeCommontCenter() {

    const char* params = "{\"des\":\"commentCenter\",\"category\":\"jump\"}";

    // URL编码
    char* encoded_params = url_encode(params);

    if (!encoded_params) {
        LOG("URL编码失败");
        return false;
    }

    // 打开URL
    bool result = openURLScheme(encoded_params);
    free(encoded_params); // 释放编码后的字符串

    return result;
}


bool openURLSchemeWarePage(const char *skuId) {

    // 验证输入参数
    if (!skuId) {
        return false;
    }

    // 构建JSON参数字符串
    // 预估长度：基础字符串 + skuId 长度
    const char* json_template = "{\"des\":\"productDetail\",\"category\":\"jump\",\"skuId\":\"%s\"}";
    size_t json_len = strlen(json_template) + strlen(skuId) + 1;

    char* params = malloc(json_len);
    if (!params) {
        LOG("[openURLSchemeOrderList] JSON参数内存分配失败");
        return false;
    }

    int ret = snprintf(params, json_len, json_template, skuId);
    if (ret < 0 || (size_t)ret >= json_len) {
        LOG("[openURLSchemeOrderList] JSON参数构建失败");
        free(params);
        return false;
    }

    LOG("[openURLSchemeOrderList] JSON参数: %s", params);

    // URL编码
    char* encoded_params = url_encode(params);
    free(params); // 释放原始JSON字符串

    if (!encoded_params) {
        LOG("[openURLSchemeOrderList] URL编码失败");
        return false;
    }

    LOG("[openURLSchemeOrderList] 编码后参数: %s", encoded_params);

    // 打开URL
    bool result = openURLScheme(encoded_params);
    free(encoded_params); // 释放编码后的字符串

    LOG("[openURLSchemeOrderList] 订单列表打开结果: %s", result ? "成功" : "失败");
    return result;
}