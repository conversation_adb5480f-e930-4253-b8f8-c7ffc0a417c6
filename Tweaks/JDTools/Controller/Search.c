#include <objc/runtime.h>
#include <objc/message.h>
#include "Log.h"
#include <objc/NSObjCRuntime.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>
#include <dispatch/dispatch.h>
#include "Controller.h"
#include <stdbool.h>

/**
 * 搜索结果集合项结构体
 * 用于封装搜索页面的集合视图相关数据
 */
typedef struct {
    id items;           // 搜索结果数据源数组
    id collectionView;  // 集合视图对象
    NSInteger count;    // 结果数量
    bool success;       // 操作是否成功
} SearchCollectionItems;

/**
 * 搜索建议列表结构体
 * 用于封装搜索建议相关的UI组件和数据
 */
typedef struct {
    id suggestView;     // 建议视图容器
    id tableView;       // 建议列表表格视图
    id cells;           // 可见的表格单元格数组
    NSInteger count;    // 建议项数量
    bool success;       // 操作是否成功
} SuggestList;


/**
 * 将C字符串转换为NSString对象
 * @param cstr C字符串指针
 * @return NSString对象，如果输入为NULL则返回nil
 */
id NSStringFromCString(const char *cstr) {
    if (!cstr) {
        LOG("NSStringFromCString: 输入参数为空");
        return nil;
    }

    return ((id (*)(Class, SEL, const char*))objc_msgSend)(
        objc_getClass("NSString"),
        sel_registerName("stringWithUTF8String:"),
        cstr
    );
}

/**
 * 获取搜索输入框对象
 * 通过反射机制从顶层视图控制器中获取搜索输入框
 * @param topVC 顶层视图控制器
 * @return 搜索输入框对象，失败返回NULL
 */
id getSearchField(id topVC) {
    if (!topVC) {
        LOG("topVC参数为空");
        return NULL;
    }

    // 获取 _searchActionBar 实例变量
    Class topVCClass = object_getClass(topVC);
    Ivar searchActionBarIvar = class_getInstanceVariable(topVCClass, "_searchActionBar");
    id searchActionBar = searchActionBarIvar ? object_getIvar(topVC, searchActionBarIvar) : NULL;
    if (!searchActionBar) {
        LOG("获取 searchActionBar 失败");
        showToastMessage("获取 searchActionBar 失败", 1.0);
        return NULL;
    }

    // 获取 _searchField 实例变量
    Class searchActionBarClass = object_getClass(searchActionBar);
    Ivar searchFieldIvar = class_getInstanceVariable(searchActionBarClass, "_searchField");
    id searchField = searchFieldIvar ? object_getIvar(searchActionBar, searchFieldIvar) : NULL;
    if (!searchField) {
        LOG("获取 searchField 失败");
        showToastMessage("获取 searchField 失败", 1.0);
        return NULL;
    }

    LOG("成功获取搜索输入框");
    return searchField;
}


/**
 * 在搜索输入框中插入关键词
 * @param topVC 顶层视图控制器
 * @param keyword 要插入的关键词
 * @return 操作是否成功
 */
bool searchFieldInsertKeyword(id topVC, const char *keyword) {
    if (!keyword) {
        LOG("keyword参数为空");
        return false;
    }

    id searchField = getSearchField(topVC);
    if (!searchField) {
        LOG("获取搜索输入框失败");
        return false;
    }

    SEL selector = selectorIfRespondsTo(searchField, "insertText:");
    if (selector) {
        ((void (*)(id, SEL, id))objc_msgSend)(searchField, selector, NSStringFromCString(keyword));
        LOG("成功插入关键词到搜索框");
        return true;
    }

    LOG("搜索框不支持insertText方法");
    return false;
}

/**
 * 清空搜索输入框中的文本
 * @param topVC 顶层视图控制器
 * @return 操作是否成功
 */
bool searchFieldClearText(id topVC) {
    id searchField = getSearchField(topVC);
    if (!searchField) {
        LOG("获取搜索输入框失败");
        return false;
    }

    SEL selector = selectorIfRespondsTo(searchField, "clearText");
    if (selector) {
        ((void (*)(id, SEL))objc_msgSend)(searchField, selector);
        LOG("成功清空搜索框文本");
        return true;
    }

    LOG("搜索框不支持clearText方法");
    return false;
}

/**
 * 全选搜索输入框中的文本
 * @param topVC 顶层视图控制器
 * @return 操作是否成功
 */
bool searchFieldSelectAll(id topVC) {
    id searchField = getSearchField(topVC);
    if (!searchField) {
        LOG("获取搜索输入框失败");
        return false;
    }

    SEL selector = selectorIfRespondsTo(searchField, "selectAll");
    if (selector) {
        ((void (*)(id, SEL))objc_msgSend)(searchField, selector);
        LOG("成功全选搜索框文本");
        return true;
    }

    LOG("搜索框不支持selectAll方法");
    return false;
}

/**
 * 在搜索列表页面模拟点击搜索框，触发进入输入状态
 * 用于从搜索结果页面返回到搜索输入状态
 * @param topVC 顶层视图控制器
 */
void triggerSearchFakeBoxDidClick(id topVC) {
    if (!topVC) {
        LOG("topVC参数为空");
        return;
    }

    // 获取 _searchBar 实例变量
    Ivar searchBarIvar = class_getInstanceVariable(object_getClass(topVC), "_searchBar");
    id searchBar = searchBarIvar ? object_getIvar(topVC, searchBarIvar) : NULL;
    if (!searchBar) {
        LOG("获取 searchBar 失败");
        return;
    }

    // 获取 _searchFakeBox 实例变量
    Ivar searchFakeBoxIvar = class_getInstanceVariable(object_getClass(searchBar), "_searchFakeBox");
    id searchFakeBox = searchFakeBoxIvar ? object_getIvar(searchBar, searchFakeBoxIvar) : NULL;
    if (!searchFakeBox) {
        LOG("获取 searchFakeBox 失败");
        return;
    }

    // 构造方法选择器并调用
    SEL didClickSel = sel_registerName("searchFakeBox:searchTextDidClick:");
    ((void (*)(id, SEL, id, id))objc_msgSend)(searchBar, didClickSel, searchFakeBox, NULL);

    LOG("成功触发搜索框点击事件");
}

/**
 * 获取搜索结果集合视图的相关数据
 * 从视图控制器中提取搜索结果数据源和集合视图对象
 * @param topVC 顶层视图控制器
 * @return SearchCollectionItems结构体，包含搜索结果相关数据
 */
SearchCollectionItems getCollectionItems(id topVC) {
    SearchCollectionItems result = {0};

    if (!topVC) {
        LOG("topVC参数为空");
        result.success = false;
        return result;
    }

    Class topVCClass = object_getClass(topVC);

    // 获取 _CollectionItems 实例变量（搜索结果数据源）
    Ivar itemsIvar = class_getInstanceVariable(topVCClass, "_CollectionItems");
    if (!itemsIvar) {
        LOG("获取 _CollectionItems 实例变量失败");
        result.success = false;
        return result;
    }

    id items = object_getIvar(topVC, itemsIvar);
    if (!items) {
        LOG("获取 CollectionItems 对象失败");
        result.success = false;
        return result;
    }
    result.items = items;

    // 获取搜索结果数量
    SEL countSel = sel_registerName("count");
    NSInteger count = ((NSInteger (*)(id, SEL))objc_msgSend)(items, countSel);
    if (count == 0) {
        LOG("搜索结果数量为0");
        result.success = false;
        return result;
    }
    result.count = count;
    LOG("获取到搜索结果数量: %ld", (long)count);

    // 获取 _CollectionView 实例变量（集合视图）
    Ivar collectionIvar = class_getInstanceVariable(topVCClass, "_collectionView");
    if (!collectionIvar) {
        // 尝试大写的变量名
        collectionIvar = class_getInstanceVariable(topVCClass, "_CollectionView");
    }
    id collectionView = collectionIvar ? object_getIvar(topVC, collectionIvar) : NULL;
    result.collectionView = collectionView;
    result.success = true;

    LOG("成功获取搜索结果集合数据");
    return result;
}

/**
 * 在搜索结果中随机选择一个商品进入详情页
 * 自动跳过广告位，优先选择后面的商品（通常质量更好）
 * @param topVC 顶层视图控制器
 * @return 操作是否成功
 */
bool selectSearchItemInCollectionView(id topVC) {
    if (!topVC) {
        LOG("topVC参数为空");
        return false;
    }

    SearchCollectionItems result = getCollectionItems(topVC);
    if (!result.success || !result.items || result.count == 0) {
        LOG("获取搜索结果失败或结果为空");
        return false;
    }

    id items = result.items;
    int count = result.count;
    id collectionView = result.collectionView;

    LOG("开始随机选择商品，总数量: %d", count);

    // 准备必要的方法选择器
    Class nsIndexPathClass = objc_getClass("NSIndexPath");
    SEL indexPathSel = sel_registerName("indexPathForRow:inSection:");
    SEL objectAtIndexSel = sel_registerName("objectAtIndex:");
    SEL didSelectSel = sel_registerName("collectionView:didSelectItemAtIndexPath:");
    SEL respondsToSel = sel_registerName("respondsToSelector:");

    // 尝试选择商品，最多尝试count次
    for (int attempt = 0; attempt < count; attempt++) {
        // 优先选择后面的商品（通常质量更好），如果总数超过10个，从后10个中选择
        uint32_t base = count > 10 ? (uint32_t)(count - 10) : 0;
        NSInteger index = arc4random_uniform((uint32_t)(count < 10 ? count : 10)) + base;

        id item = ((id (*)(id, SEL, NSInteger))objc_msgSend)(items, objectAtIndexSel, index);
        if (!item) {
            LOG("第%d次尝试：索引%ld的商品为空", attempt + 1, (long)index);
            continue;
        }

        // 检查商品类型是否正确
        const char *className = class_getName(object_getClass(item));
        if (!strstr(className, "FSSearchProductViewModel")) {
            LOG("第%d次尝试：索引%ld的商品类型不匹配: %s", attempt + 1, (long)index, className);
            continue;
        }

        // 检查是否为广告位并自动跳过
        SEL adVisibleSel = selectorIfRespondsTo(item, "adVisible");
        if (adVisibleSel) {
            BOOL isAd = ((BOOL (*)(id, SEL))objc_msgSend)(item, adVisibleSel);
            if (isAd) {
                LOG("第%d次尝试：索引%ld是广告位，自动跳过", attempt + 1, (long)index);
                showToastMessage("自动跳过广告位", 1.0);
                continue;
            }
        }

        // 尝试点击选中商品
        id indexPath = ((id (*)(Class, SEL, NSInteger, NSInteger))objc_msgSend)(nsIndexPathClass, indexPathSel, index, 0);
        BOOL canRespond = ((BOOL (*)(id, SEL, SEL))objc_msgSend)(topVC, respondsToSel, didSelectSel);
        if (canRespond) {
            ((void (*)(id, SEL, id, id))objc_msgSend)(topVC, didSelectSel, collectionView, indexPath);
            LOG("成功选中第%ld个商品", (long)index);
            return true;
        } else {
            LOG("第%d次尝试：topVC不支持didSelectItemAtIndexPath方法", attempt + 1);
        }
    }

    LOG("所有尝试均失败，无法选中任何商品");
    return false;
}

/**
 * 通过设置wareId直接跳转到指定商品
 * 遍历搜索结果，找到合适的商品项并设置wareId后模拟点击
 * @param topVC 顶层视图控制器
 * @param wareId 目标商品的wareId
 * @return 操作是否成功
 */
bool selectSearchItemBySetWareId(id topVC, const char *wareId) {
    if (!topVC) {
        LOG("topVC参数为空");
        return false;
    }

    if (!wareId) {
        LOG("wareId参数为空");
        return false;
    }

    LOG("开始通过wareId跳转商品: %s", wareId);

    SearchCollectionItems result = getCollectionItems(topVC);
    if (!result.success || !result.items || result.count == 0) {
        LOG("获取搜索结果失败或结果为空");
        return false;
    }

    id items = result.items;
    int count = result.count;
    id collectionView = result.collectionView;

    LOG("搜索结果总数: %d", count);

    // 准备必要的方法选择器
    SEL objectAtIndexSel = sel_registerName("objectAtIndex:");
    SEL classSel = sel_registerName("class");
    SEL setWareIdSel = sel_registerName("setWareId:");
    SEL respondsToSelectorSel = sel_registerName("respondsToSelector:");
    SEL clickSel = sel_registerName("collectionView:didSelectItemAtIndexPath:");

    Class nsIndexPathCls = objc_getClass("NSIndexPath");
    SEL indexPathSel = sel_registerName("indexPathForRow:inSection:");

    // 遍历搜索结果寻找合适的商品项
    for (int i = 0; i < count; i++) {
        id item = ((id (*)(id, SEL, NSInteger))objc_msgSend)(items, objectAtIndexSel, i);
        if (!item) {
            LOG("索引%d的商品项为空", i);
            continue;
        }

        // 检查商品类型
        Class itemCls = ((Class (*)(id, SEL))objc_msgSend)(item, classSel);
        const char *clsName = class_getName(itemCls);
        if (!clsName || !strstr(clsName, "FSSearchProductViewModel")) {
            LOG("索引%d的商品类型不匹配: %s", i, clsName ? clsName : "NULL");
            continue;
        }

        // 获取商品模型对象
        Ivar modelIvar = class_getInstanceVariable(itemCls, "_fsProductModel");
        id fsProductModel = modelIvar ? object_getIvar(item, modelIvar) : NULL;
        if (!fsProductModel) {
            LOG("索引%d的商品模型对象为空", i);
            continue;
        }

        // 检查是否支持设置wareId
        BOOL canSet = ((BOOL (*)(id, SEL, SEL))objc_msgSend)(fsProductModel, respondsToSelectorSel, setWareIdSel);
        if (!canSet) {
            LOG("索引%d的商品模型不支持setWareId方法", i);
            continue;
        }

        // 设置wareId
        ((void (*)(id, SEL, id))objc_msgSend)(fsProductModel, setWareIdSel, NSStringFromCString(wareId));
        LOG("成功为索引%d的商品设置wareId: %s", i, wareId);

        // 构造indexPath并模拟点击
        id indexPath = ((id (*)(Class, SEL, NSUInteger, NSUInteger))objc_msgSend)(nsIndexPathCls, indexPathSel, (NSUInteger)i, 0);
        BOOL canClick = ((BOOL (*)(id, SEL, SEL))objc_msgSend)(topVC, respondsToSelectorSel, clickSel);
        if (canClick) {
            ((void (*)(id, SEL, id, id))objc_msgSend)(topVC, clickSel, collectionView, indexPath);
            LOG("成功点击索引%d的商品", i);
            return true;
        } else {
            LOG("topVC不支持点击方法");
        }
    }

    LOG("未找到合适的商品项进行wareId设置");
    return false;
}


/**
 * 获取搜索建议列表的相关UI组件和数据
 * 从视图控制器中提取建议视图、表格视图和可见单元格
 * @param topVC 顶层视图控制器
 * @return SuggestList结构体，包含建议列表相关数据
 */
SuggestList getSuggestList(id topVC) {
    SuggestList result = {0};

    if (!topVC) {
        LOG("topVC参数为空");
        result.success = false;
        return result;
    }

    Class topVCClass = object_getClass(topVC);

    // 获取建议视图容器
    // Ivar suggestViewIvar = class_getInstanceVariable(topVCClass, "_gSuggestView");
    // id suggestView = suggestViewIvar ? object_getIvar(topVC, suggestViewIvar) : NULL;
    Ivar suggestViewIvar = NULL;
    // _gSuggestView 新版本
    // _suggestView 旧版本
    Ivar tmp = class_getInstanceVariable(topVCClass, "_gSuggestView");
    if (tmp) {
        suggestViewIvar = tmp;
    } else {
        suggestViewIvar = class_getInstanceVariable(topVCClass, "_suggestView");
    }
    id suggestView = suggestViewIvar ? object_getIvar(topVC, suggestViewIvar) : NULL;

    if (!suggestView) {
        LOG("获取建议视图失败");
        result.success = false;
        return result;
    }
    result.suggestView = suggestView;
    LOG("成功获取建议视图");

    // 获取表格视图
    Class suggestViewClass = object_getClass(suggestView);
    Ivar tableViewIvar = class_getInstanceVariable(suggestViewClass, "_tableView");
    id tableView = tableViewIvar ? object_getIvar(suggestView, tableViewIvar) : NULL;
    if (!tableView) {
        LOG("获取建议表格视图失败");
        result.success = false;
        return result;
    }
    result.tableView = tableView;
    LOG("成功获取建议表格视图");

    // 获取可见单元格数组
    Class tableViewClass = object_getClass(tableView);
    Ivar cellsIvar = class_getInstanceVariable(tableViewClass, "_visibleCells");
    id cells = cellsIvar ? object_getIvar(tableView, cellsIvar) : NULL;
    if (!cells) {
        LOG("获取可见单元格数组失败");
        result.success = false;
        return result;
    }
    result.cells = cells;

    // 检查单元格数组是否支持count方法
    SEL countSel = sel_registerName("count");
    if (!((BOOL (*)(id, SEL, SEL))objc_msgSend)(cells, sel_registerName("respondsToSelector:"), countSel)) {
        LOG("单元格数组不支持count方法");
        result.success = false;
        return result;
    }

    // 获取建议项数量
    NSUInteger count = ((NSUInteger (*)(id, SEL))objc_msgSend)(cells, countSel);
    result.count = count;
    LOG("成功获取建议列表，数量: %lu", (unsigned long)count);

    result.success = true;
    return result;
}


/**
 * 在搜索建议列表中匹配关键词或修改建议项后提交搜索
 * 优先寻找完全匹配的建议项，如果没有则修改第一个建议项的内容
 * @param topVC 顶层视图控制器
 * @param keyword 要匹配或设置的关键词
 * @return 操作是否成功
 */
bool setSuggestWareNameInSearch(id topVC, const char *keyword) {
    if (!keyword) {
        LOG("setSuggestWareNameInSearch: keyword参数为空");
        return false;
    }

    LOG("开始在建议列表中匹配关键词: %s", keyword);

    SuggestList result = getSuggestList(topVC);
    if (!result.success) {
        LOG("获取建议列表失败");
        return false;
    }

    id suggestView = result.suggestView;
    id tableView = result.tableView;
    id cells = result.cells;
    NSInteger count = result.count;

    if (count == 0) {
        LOG("建议列表为空");
        showToastMessage("没有匹配结果", 1.0);
        return false;
    }

    SEL objectAtIndexSel = sel_registerName("objectAtIndex:");
    id firstMatchedCell = NULL;
    NSUInteger firstMatchedIndex = 0;

    // 第一轮：寻找完全匹配的建议项
    for (NSUInteger i = 0; i < count; i++) {
        id cell = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(cells, objectAtIndexSel, i);
        if (!cell) {
            LOG("索引%lu的建议单元格为空", (unsigned long)i);
            continue;
        }

        // 检查单元格类型
        if (!strstr(object_getClassName(cell), "FSSearchSuggestCell")) {
            LOG("索引%lu的单元格类型不匹配: %s", (unsigned long)i, object_getClassName(cell));
            continue;
        }

        // 获取关键词对象
        Ivar keyWordIvar = class_getInstanceVariable(object_getClass(cell), "_keyWord");
        id keyWordObj = keyWordIvar ? object_getIvar(cell, keyWordIvar) : NULL;
        if (!keyWordObj) {
            LOG("索引%lu的关键词对象为空", (unsigned long)i);
            continue;
        }

        // 获取关键词文本并比较
        SEL textSel = sel_registerName("text");
        id text = ((id (*)(id, SEL))objc_msgSend)(keyWordObj, textSel);
        // ============= 尝试打印关键词信息 =================
        const char *cstr = NULL;
        (void)cstr;
        if (text) {
            SEL utf8Sel = sel_registerName("UTF8String");
            if (((BOOL (*)(id, SEL, SEL))objc_msgSend)(text, sel_registerName("respondsToSelector:"), utf8Sel)) {
                cstr = ((const char * (*)(id, SEL))objc_msgSend)(text, utf8Sel);
            }
        }
        LOG("当前列表关键词: %s", cstr);

        // ============= 尝试打印关键词信息 =================

        SEL isEqualSel = sel_registerName("isEqualToString:");

        if (text && ((BOOL (*)(id, SEL, id))objc_msgSend)(text, isEqualSel, NSStringFromCString(keyword))) {
            LOG("找到完全匹配的关键词，索引: %lu", (unsigned long)i);

            // 构造indexPath并点击
            Class NSIndexPathClass = objc_getClass("NSIndexPath");
            SEL indexPathSel = sel_registerName("indexPathForRow:inSection:");
            id indexPath = ((id (*)(Class, SEL, NSUInteger, NSUInteger))objc_msgSend)(NSIndexPathClass, indexPathSel, i, 0);

            SEL clickSel = sel_registerName("tableView:didSelectRowAtIndexPath:");
            if (((BOOL (*)(id, SEL, SEL))objc_msgSend)(suggestView, sel_registerName("respondsToSelector:"), clickSel)) {
                ((void (*)(id, SEL, id, id))objc_msgSend)(suggestView, clickSel, tableView, indexPath);
                LOG("成功点击完全匹配的建议项");
                return true;
            } else {
                LOG("建议视图不支持点击方法");
            }
        }

        // 记录第一个有效的单元格作为备选
        if (!firstMatchedCell) {
            firstMatchedCell = cell;
            firstMatchedIndex = i;
        }
    }

    // 第二轮：没有找到完全匹配，尝试修改第一个建议项
    if (firstMatchedCell) {
        LOG("未找到完全匹配，尝试修改第一个建议项，索引: %lu", (unsigned long)firstMatchedIndex);

        Ivar itemIvar = class_getInstanceVariable(object_getClass(firstMatchedCell), "_item");
        id item = itemIvar ? object_getIvar(firstMatchedCell, itemIvar) : NULL;
        if (!item) {
            LOG("第一个建议项的item对象为空");
        } else {
            SEL setSuggestSel = sel_registerName("setSuggestWareName:");
            if (((BOOL (*)(id, SEL, SEL))objc_msgSend)(item, sel_registerName("respondsToSelector:"), setSuggestSel)) {
                ((void (*)(id, SEL, id))objc_msgSend)(item, setSuggestSel, NSStringFromCString(keyword));
                LOG("成功设置第一个建议项的关键词");

                // 构造indexPath并点击
                Class NSIndexPathClass = objc_getClass("NSIndexPath");
                SEL indexPathSel = sel_registerName("indexPathForRow:inSection:");
                id indexPath = ((id (*)(Class, SEL, NSUInteger, NSUInteger))objc_msgSend)(NSIndexPathClass, indexPathSel, firstMatchedIndex, 0);

                SEL clickSel = sel_registerName("tableView:didSelectRowAtIndexPath:");
                if (((BOOL (*)(id, SEL, SEL))objc_msgSend)(suggestView, sel_registerName("respondsToSelector:"), clickSel)) {
                    ((void (*)(id, SEL, id, id))objc_msgSend)(suggestView, clickSel, tableView, indexPath);
                    LOG("成功点击修改后的建议项");
                    return true;
                } else {
                    LOG("建议视图不支持点击方法");
                }
            } else {
                LOG("第一个建议项不支持setSuggestWareName方法");
            }
        }
    }

    LOG("关键词匹配和设置均失败");
    return false;
}

/**
 * 处理搜索关键词的主要逻辑
 * 根据当前页面状态执行相应的搜索操作，包括页面跳转、关键词输入和建议匹配
 * 使用内存安全的方式处理异步操作中的字符串参数
 * @param topVC 顶层视图控制器
 * @param keyword 搜索关键词
 * @return 操作是否成功启动
 */
bool handleSearchKeyword(id topVC, const char *keyword) {
    if (!topVC) {
        LOG("handleSearchKeyword: topVC参数为空");
        return false;
    }

    if (!keyword) {
        LOG("handleSearchKeyword: keyword参数为空");
        return false;
    }

    // 创建keyword的安全副本，确保在异步操作中内存安全
    // 使用malloc分配内存并复制字符串内容
    size_t keywordLen = strlen(keyword);
    char *keywordCopy = malloc(keywordLen + 1);
    if (!keywordCopy) {
        LOG("handleSearchKeyword: 内存分配失败");
        return false;
    }
    strcpy(keywordCopy, keyword);
    LOG("创建关键词安全副本: %s", keywordCopy);

    double delayTime = 0.0; // 默认无延时
    // 获取当前页面的类名
    const char *currentClassName = object_getClassName(topVC);
    LOG("当前页面类名: %s", currentClassName);

    // 根据当前页面状态执行相应的逻辑
    if (strcmp(currentClassName, "FSSearchActionViewController") == 0) {
        LOG("当前已在搜索输入页面");
        delayTime = 0.0;

    } else if (strcmp(currentClassName, "FinalSearchListViewController") == 0) {
        LOG("当前在搜索结果页面，触发返回输入状态");
        triggerSearchFakeBoxDidClick(topVC);
        delayTime = 0.5;

    } else {
        LOG("当前不在搜索页面，尝试跳转到搜索页面");
        bool success = jdOAuthManager("FSSearchActionViewController");
        if (!success) {
            LOG("跳转到搜索页面失败");
            showToastMessage("跳转到搜索状态失败", 1.0);
            free(keywordCopy); // 释放内存
            return false;
        }
        delayTime = 1.5;
    }

    LOG("等待 %.1f 秒后执行搜索操作", delayTime);

    // 第一个异步操作：等待页面准备完成后输入关键词
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 重新获取当前的topVC，确保页面状态正确
        id currentTopVC = getTopViewController();
        if (!currentTopVC) {
            LOG("第一阶段：无法获取当前topVC");
            showToastMessage("获取页面状态失败", 1.0);
            free(keywordCopy); // 释放内存
            return;
        }

        // 验证keywordCopy的有效性
        if (!keywordCopy || strlen(keywordCopy) == 0) {
            LOG("第一阶段：关键词副本无效");
            showToastMessage("关键词参数异常", 1.0);
            free(keywordCopy); // 释放内存
            return;
        }

        // 输入关键词到搜索框
        bool inputSuccess = searchFieldInsertKeyword(currentTopVC, keywordCopy);
        if (!inputSuccess) {
            LOG("第一阶段：输入关键词失败");
            showToastMessage("输入关键词失败，请重试", 1.0);
            free(keywordCopy); // 释放内存
            return;
        }

        LOG("第一阶段：关键词输入成功，等待建议列表加载");

        // 第二个异步操作：等待建议列表加载完成后进行匹配
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 再次获取当前的topVC
            id finalTopVC = getTopViewController();
            if (!finalTopVC) {
                LOG("第二阶段：无法获取当前topVC");
                showToastMessage("页面状态异常", 1.0);
                free(keywordCopy); // 释放内存
                return;
            }

            // 最后一次验证keywordCopy
            if (!keywordCopy || strlen(keywordCopy) == 0) {
                LOG("第二阶段：关键词副本无效");
                showToastMessage("关键词参数丢失", 1.0);
                free(keywordCopy); // 释放内存
                return;
            }

            // 执行建议匹配和提交
            bool matchSuccess = setSuggestWareNameInSearch(finalTopVC, keywordCopy);
            if (!matchSuccess) {
                LOG("第二阶段：关键词匹配失败");
                showToastMessage("关键词匹配失败，请重试", 1.0);
            } else {
                LOG("第二阶段：关键词匹配成功");
            }

            // 操作完成，释放内存
            free(keywordCopy);
        });
    });

    return true;
}


/**
 * 搜索管理器主入口函数
 * 根据按钮ID执行不同的搜索操作：随机选择、目标跳转或关键词搜索
 * @param buttonId 按钮标识符（"random"、"target"或其他）
 * @param serialNumber 设备序列号（用于日志记录）
 * @param keyword 搜索关键词
 * @param wareId 目标商品ID
 * @param buyCount 购买数量（暂未使用）
 */
void searchManager(const char *buttonId, const char *serialNumber, const char *keyword, const char *wareId, const char *buyCount) {
    // 参数验证
    if (!buttonId) {
        LOG("searchManager: buttonId参数为空");
        showToastMessage("操作参数错误", 1.0);
        return;
    }

    LOG("搜索管理器启动 - 按钮ID: %s, 设备序列号: %s",
           buttonId, serialNumber ? serialNumber : "未提供");

    // 获取当前顶层视图控制器
    id topVC = getTopViewController();
    if (!topVC) {
        LOG("获取顶层视图控制器失败");
        showToastMessage("获取页面控制器失败", 1.0);
        return;
    }

    // LOG("当前页面类型: %s", object_getClassName(topVC));

    // 根据按钮ID执行相应操作
    if (strcmp(buttonId, "random") == 0) {
        if (!isKindOfClass(topVC, "FinalSearchListViewController")) {
            LOG("随机选择操作不在搜索结果页面");
            showToastMessage("不在搜索结果页", 1.0);
            return;
        }

        LOG("执行随机选择商品操作");
        bool success = selectSearchItemInCollectionView(topVC);
        if (success) {
            LOG("随机选择商品操作成功");
        } else {
            LOG("随机选择商品操作失败");
            showToastMessage("随机选择失败", 1.0);
        }

    } else if (strcmp(buttonId, "target") == 0) {
        if (!wareId) {
            LOG("目标跳转操作缺少wareId参数");
            showToastMessage("缺少商品ID参数", 1.0);
            return;
        }

        LOG("执行目标商品跳转操作，wareId: %s", wareId);
        bool success = selectSearchItemBySetWareId(topVC, wareId);
        if (success) {
            LOG("目标商品跳转操作成功");
        } else {
            LOG("目标商品跳转操作失败");
            showToastMessage("商品跳转失败", 1.0);
        }

    } else {
        if (!keyword) {
            LOG("关键词搜索操作缺少keyword参数");
            showToastMessage("缺少搜索关键词", 1.0);
            return;
        }

        LOG("执行关键词搜索操作，关键词: %s", keyword);
        bool success = handleSearchKeyword(topVC, keyword);
        if (success) {
            LOG("关键词搜索操作启动成功");
        } else {
            LOG("关键词搜索操作启动失败");
            showToastMessage("搜索启动失败", 1.0);
        }
    }
}