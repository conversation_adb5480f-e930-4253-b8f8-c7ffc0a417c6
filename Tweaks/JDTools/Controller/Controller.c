#include "Controller.h"
#include <string.h>
#include "Log.h"
#include <objc/runtime.h>
#include <objc/message.h>
#include "StringCryptor_v2.h"

char* copy_string(const char* source) {
    if (!source) return NULL;
    char* result = malloc(strlen(source) + 1);
    if (result) strcpy(result, source);
    return result;
}

// 获取顶层视图控制器
id getTopViewController(void) {
    // 加密数据 for "PDComponentUtils"
    // 实际数据应由你的加密工具生成
    static const uint8_t kPDComponentUtilsEncrypted[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0xA4,0xA8,0xEF,0xEF,0x90,0x12,0x18,0x09,0x20,0x09,0x2F,0xAF,0x6F,0x5C,0x41,0xB4,0x04,0x1F,0xEA,0x15};
    // 加密数据 for "getCurrentVC"
    static const uint8_t kGetCurrentVCEncrypted[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x3F,0xB8,0x61,0x46,0x5A,0x2E,0x63,0xC6,0xE4,0x75,0xDB,0x50,0x2A,0x11,0xCA,0x4D};

    char *PDComponentUtils = decrypt_string_v2(kPDComponentUtilsEncrypted, sizeof(kPDComponentUtilsEncrypted));
    if (!PDComponentUtils) {
        return NULL;
    }

    Class pdComponentUtils = objc_getClass(PDComponentUtils);
    if (!pdComponentUtils) {
        free(PDComponentUtils);
        return NULL;
    }
    char *currentVC = decrypt_string_v2(kGetCurrentVCEncrypted, sizeof(kGetCurrentVCEncrypted));
    if (!currentVC) {
        free(PDComponentUtils);
        return NULL;
    }
    SEL getCurrentVCSel = sel_registerName(currentVC);

    // 检查类是否响应该方法
    Method classMethod = class_getClassMethod(pdComponentUtils, getCurrentVCSel);
    if (!classMethod) {
        free(PDComponentUtils);
        free(currentVC);
        return NULL;
    }
    // 调用类方法获取顶层视图控制器
    id (*msgSend)(id, SEL) = (id (*)(id, SEL))objc_msgSend;
    id viewController = msgSend((id)pdComponentUtils, getCurrentVCSel);
    if (!viewController) {
        free(PDComponentUtils);
        free(currentVC);
        return NULL;
    }

    free(PDComponentUtils);
    free(currentVC);

    return viewController;
}

char* getClassName(id viewController) {
    const char *name = object_getClassName(viewController);
    if (!name) return NULL;

    // 返回 const char * 是安全的，因为 object_getClassName 返回的字符串不需要你手动管理内存
    return (char *)name;
}

void callBackButtonClicked(void) {
    id topVC = getTopViewController();  // 你已经实现的函数
    if (!topVC) {
        LOG("获取 topVC 失败");
        return;
    }

    // 注册 selector
    SEL backSel = sel_registerName("backButtonClicked");

    // 判断是否响应该方法
    if (!((BOOL (*)(id, SEL, SEL))objc_msgSend)(topVC, sel_registerName("respondsToSelector:"), backSel)) {
        LOG("topVC 不响应 backButtonClicked 方法");
        return;
    }

    // 调用方法，无参数
    ((void (*)(id, SEL))objc_msgSend)(topVC, backSel);

    LOG("已调用 backButtonClicked 方法");
}

SEL selectorIfRespondsTo(id target, const char *sel_name) {
    if (!target || !sel_name) return NULL;

    SEL selector = sel_registerName(sel_name);
    SEL respondsSel = sel_registerName("respondsToSelector:");

    BOOL result = ((BOOL (*)(id, SEL, SEL))objc_msgSend)(target, respondsSel, selector);

    return result ? selector : NULL;
}

id getIvar(id target, const char *ivar_name) {
    if (!target || !ivar_name) return NULL;

    Class targetClass = object_getClass(target);
    if (!targetClass) return NULL;

    Ivar ivar = class_getInstanceVariable(targetClass, ivar_name);
    if (!ivar) return NULL;
    
    return object_getIvar(target, ivar);
}

bool isKindOfClass(id obj, const char *className) {
    if (!obj || !className) return false;

    Class targetClass = objc_getClass(className);
    if (!targetClass) return false;

    SEL isKindOfClassSEL = sel_registerName("isKindOfClass:");
    BOOL result = ((BOOL (*)(id, SEL, Class))objc_msgSend)(obj, isKindOfClassSEL, targetClass);

    return result ? true : false;
}

const char* getVar(id viewController, const char *varName) {
    if (!viewController || !varName) return NULL;

    Ivar instance = class_getInstanceVariable(object_getClass(viewController), varName);
    if (!instance) return NULL;

    id var = object_getIvar(viewController, instance);
    if (!var) return NULL;

    // 转为c字符串
    SEL utf8Sel = sel_registerName("UTF8String");

    return ((const char * (*)(id, SEL))objc_msgSend)(var, utf8Sel);
}