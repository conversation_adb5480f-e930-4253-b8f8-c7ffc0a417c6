#include "Controller.h"
#include "URLScheme.h"
// #include "Requestor.h"
#include <string.h>
#include "Log.h"
#include <stdio.h>
#include <dispatch/dispatch.h>
#include <objc/NSObjCRuntime.h>
#include <objc/runtime.h>
#include <objc/message.h>
#include <stdlib.h>
#include "CryptoRequest.h"
#include "JSONUtils.h"
#include "/Users/<USER>/Documents/Main/Tweak/Tweaks/JDTools/productInfo.h"
#include "Controller.h"

typedef struct {
    bool status;        // 操作状态
    id wareId;          // 商品ID
    id orderId;         // 订单ID
    char *error;         // 错误信息
} ShareOrderInfo;

ShareOrderInfo getShareOrderInfo(id topVC) {
    ShareOrderInfo result = {0};

    if (!topVC) {
        result.status = false;
        result.error = strdup("获取页面失败");
        return result;
    }

    LOG("topVC: %p", topVC);

    // 获取 _orderId
    Class topVCClass = object_getClass(topVC);
    if (!topVCClass) {
        result.error = strdup("页面Class类型错误");
        result.status = false;
        return result;
    }

    // topVCClass
    LOG("topVCClass: %p", topVCClass);

    Ivar orderIdIvar = class_getInstanceVariable(topVCClass, "_orderId");
    if (!orderIdIvar) {
        result.error = strdup("订单ID读取失败");
        result.status = false;
        return result;
    }
    LOG("orderIdIvar: %p", orderIdIvar);

    Ivar wareIdIvar  = class_getInstanceVariable(topVCClass, "_wareId");
    if (!wareIdIvar) {
        result.error = strdup("商品ID读取失败");
        result.status = false;
        return result;
    }
    LOG("wareIdIvar: %p", wareIdIvar);

    if (!orderIdIvar || !wareIdIvar) {
        result.error = strdup("订单原始信息读取失败");
        result.status = false;
        return result;
    }

    id nsOrderId = object_getIvar(topVC, orderIdIvar);
    id nsWareId  = object_getIvar(topVC, wareIdIvar);

    if (!nsOrderId || !nsWareId) {
        result.error = strdup("订单信息读取失败");
        result.status = false;
        return result;
    }

    LOG("nsOrderId: %p", nsOrderId);
    LOG("nsWareId: %p", nsWareId);

    result.status = true;
    result.orderId = nsOrderId;
    result.wareId  = nsWareId;
    // result.status = false;
    result.error = strdup("获取成功");
    return result;
}

void freeShareOrderInfo(ShareOrderInfo* info) {
    if (!info) return;

    // 释放错误信息字符串（这是用 strdup 分配的 C 内存）
    if (info->error) {
        free(info->error);
        info->error = NULL;
    }

    // 注意：wareId 和 orderId 是 Objective-C 对象，不需要手动释放
    // 它们由 ARC 或者 retain/release 机制管理
    info->wareId = nil;
    info->orderId = nil;
    info->status = false;
}

// 自动读取当前页面的wareId和orderId，并且通过序列号向服务器获取评语内容自动填入
bool handleInputCommentWithOrderInfo(int type, const char* serialNumber) {
    // return true;
    if (!serialNumber) return false;

    LOG("handleInputCommentWithOrderInfo %s", serialNumber);
    // 1. 获取当前页面信息
    id topVC = getTopViewController();
    if (!topVC) {
        showToastMessage("获取当前页面失败", 1.0);
        return false;
    }
    if (!isKindOfClass(topVC, "ShareOrderPublishController")) {
        showToastMessage("当前不是评价页面", 1.0);
        return false;
    }

    // 获取订单信息
    ShareOrderInfo orderInfo = getShareOrderInfo(topVC);
    if (!orderInfo.status) {
        showDialogMessage(orderInfo.error, 5.0);
        freeShareOrderInfo(&orderInfo);
        return false;
    }
    id orderId = orderInfo.orderId;
    id wareId = orderInfo.wareId;

    // 显示一个toast显示wareId
    LOG("orderId: %p", orderId);
    LOG("wareId: %p", wareId);

    
    if (!orderId || !wareId) {
        freeShareOrderInfo(&orderInfo);
        showToastMessage("订单信息为空", 1.0);
        return false;
    }

    // 获取 UTF8String 选择子
    SEL utf8Sel = sel_registerName("UTF8String");
    LOG("utf8Sel: %p", utf8Sel);

    // 安全地获取字符串并创建副本
    const char *orderIdStr = ((const char * (*)(id, SEL))objc_msgSend)(orderId, utf8Sel);
    const char *wareIdStr  = ((const char * (*)(id, SEL))objc_msgSend)(wareId, utf8Sel);

    // 验证字符串是否有效
    if (!orderIdStr || !wareIdStr) {
        freeShareOrderInfo(&orderInfo);
        showDialogMessage("订单信息字符串获取失败", 5.0);
        return false;
    }

    LOG("orderIdStr: %s", orderIdStr);
    LOG("wareIdStr: %s", wareIdStr);

    showToastMessage(orderIdStr, 1.0);

    // 创建字符串副本，避免悬空指针问题
    char *orderIdCopy = strdup(orderIdStr);
    char *wareIdCopy = strdup(wareIdStr);

    if (!orderIdCopy || !wareIdCopy) {
        if (orderIdCopy) free(orderIdCopy);
        if (wareIdCopy) free(wareIdCopy);
        freeShareOrderInfo(&orderInfo);
        showDialogMessage("内存分配失败", 5.0);
        return false;
    }

    LOG("orderId: %s", orderIdCopy);
    LOG("wareId: %s", wareIdCopy);

    char request_data[256] = {0};
    // char request_data = NULL;
    snprintf(request_data, sizeof(request_data), "{\"skuId\": \"%s\", \"type\": %d}", wareIdCopy, type);

    // 现在可以安全地释放订单信息，因为我们已经复制了需要的字符串
    freeShareOrderInfo(&orderInfo);

    // CryptoRequestConfig config = {
    //     // .server_url = "http://47.108.169.125:9000/tools/v1",
    //     .request_data = request_data
    // };
    CryptoRequestConfig config = {
        .path = "/tools/v1",
        .action = "comment",
        .request_data = request_data,
        .serial_number = serialNumber,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);
    // 释放 request_data
    // free(request_data);
    // 释放字符串副本
    free(orderIdCopy);
    free(wareIdCopy);
    if (!result.success) {
        showToastMessage("服务器请求失败", 2.0);
        crypto_request_free_result(&result);
        return false;
    }
    if (!result.response_data) {
        showToastMessage("服务器响应数据为空", 2.0);
        crypto_request_free_result(&result);
        return false;
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        showToastMessage("服务器响应数据为空", 2.0);
        crypto_request_free_result(&result);
        return false;
    }
    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        const char* error = message ? message : "未知错误";
        showToastMessage(error, 2.0);
        crypto_request_free_result(&result);
        return false;
    }
    cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!data) {
        showToastMessage("服务器响应数据为空", 2.0);
        crypto_request_free_result(&result);
        return false;
    }
    bool status = JSONUtils_GetBoolValue(data, "status", false);
    if (!status) {
        showToastMessage("缺少评语内容，请及时上传", 2.0);
        crypto_request_free_result(&result);
        return false;
    }
    const char* comment = JSONUtils_GetStringValue(data, "comment");
    if (!comment) {
        showToastMessage("返回评价内容异常", 2.0);
        crypto_request_free_result(&result);
        return false;
    }

    LOG("评论: %s", comment);

    InputResult inputResult = handleInpuText(comment, false);


    // 使用完后释放资源
    freeInputTextResult(&inputResult);

    crypto_request_free_result(&result);

    return true;
}

// 获取当前页面ID 1=全部 2=代付款 3=待发货 4=已完成 5=已取消
int getOrderListUItypeId(id topVC) {
    if (!topVC) return -1;
    if (!isKindOfClass(topVC, "JDOrderListVC")) return -1;

    id currentPageModel = getIvar(topVC, "_currentPageModel");
    if (!currentPageModel) return -1;

    LOG("currentPageModel: %p", currentPageModel);

    Ivar listUITypeIvar = class_getInstanceVariable(object_getClass(currentPageModel), "_listUIType");
    if (!listUITypeIvar) {
        return -1;
    }
    ptrdiff_t offset = ivar_getOffset(listUITypeIvar);
    char *bytes = (char *)currentPageModel;

    int value = *(int *)(bytes + offset);
    if (!value) return -1;

    return value;

}

// 跳转成功回调
typedef void (*ConfirmOrderCallback)(bool success);

// 跳转到待收货列表成功后执行的操作
void afterJumpListSuccess(int index, ConfirmOrderCallback callback) {
    if (!callback) return;

    if (!openURLSchemeOrderList(index)) {
        showToastMessage("跳转待收货页面失败", 1.0);
        callback(false);
        return;
    }

    showToastMessage("等待页面数据加载中...", 2.0);

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        id topVC = getTopViewController();
        if (!topVC) {
            showToastMessage("获取页面控制器失败", 1.0);
            callback(false);
            return;
        }
        if (!isKindOfClass(topVC, "JDOrderListVC")) {
            showToastMessage("当前页面不是订单列表页", 1.0);
            callback(false);
            return;
        }

        int listUIType = getOrderListUItypeId(topVC);
        if (listUIType != index) {
            showToastMessage("当前不是待收货列表页", 1.0);
            callback(false);
            return;
        }

        callback(true);
    });
}

// 订单信息结构体
typedef struct {
    int orderType;
    int orderStatusId;
    int wareCount;

    char *orderId;
    char *orderStatusShow;
    char *price;
    char *listPrice;
    char *venderId;
    char *shopId;
    char *shopName;
} OrderItemInfo;

// 获取订单列表回调
typedef void (*OrderListCallback)(OrderItemInfo *items, size_t count);

// 获取订单列表
void getOrderItems(id topVC, OrderListCallback callback) {
    if (!topVC || !callback) return;
    if (!isKindOfClass(topVC, "JDOrderListVC")) {
        callback(NULL, 0);
        return;
    }

    id currentPageModel = getIvar(topVC, "_currentPageModel");
    if (!currentPageModel) {
        callback(NULL, 0);
        return;
    }

    Ivar dataSourceIvar = class_getInstanceVariable(object_getClass(currentPageModel), "_dataSourceArray");
    if (!dataSourceIvar) {
        callback(NULL, 0);
        return;
    }

    id dataSourceArray = object_getIvar(currentPageModel, dataSourceIvar);
    if (!dataSourceArray) {
        callback(NULL, 0);
        return;
    }

    NSUInteger count = ((NSUInteger (*)(id, SEL))objc_msgSend)(dataSourceArray, sel_registerName("count"));
    if (count == 0) {
        callback(NULL, 0);
        return;
    }

    OrderItemInfo *items = calloc(count, sizeof(OrderItemInfo));
    if (!items) {
        callback(NULL, 0);
        return;
    }

    for (NSUInteger i = 0; i < count; i++) {
        id item = ((id (*)(id, SEL, NSUInteger))objc_msgSend)(dataSourceArray, sel_registerName("objectAtIndex:"), i);
        if (!item) continue;

        OrderItemInfo *info = &items[i];

        // 提取 int
        #define GET_INT_FIELD(obj, name, out) do { \
            Ivar ivar = class_getInstanceVariable(object_getClass(obj), name); \
            if (ivar) { \
                ptrdiff_t offset = ivar_getOffset(ivar); \
                char *bytes = (char *)obj; \
                out = *(int *)(bytes + offset); \
            } \
        } while(0)

        GET_INT_FIELD(item, "_orderType", info->orderType);
        GET_INT_FIELD(item, "_orderStatusId", info->orderStatusId);
        GET_INT_FIELD(item, "_wareCount", info->wareCount);

        #undef GET_INT_FIELD

        // 提取 NSString*
        #define GET_STR_FIELD(obj, name, out) do { \
            Ivar ivar = class_getInstanceVariable(object_getClass(obj), name); \
            if (ivar) { \
                id strObj = object_getIvar(obj, ivar); \
                if (strObj) { \
                    const char *tmp = ((const char *(*)(id, SEL))objc_msgSend)(strObj, sel_registerName("UTF8String")); \
                    out = tmp ? strdup(tmp) : NULL; \
                } \
            } \
        } while(0)

        GET_STR_FIELD(item, "_orderId", info->orderId);
        GET_STR_FIELD(item, "_orderStatusShow", info->orderStatusShow);
        GET_STR_FIELD(item, "_price", info->price);
        GET_STR_FIELD(item, "_listPrice", info->listPrice);
        GET_STR_FIELD(item, "_venderId", info->venderId);
        GET_STR_FIELD(item, "_shopId", info->shopId);
        GET_STR_FIELD(item, "_shopName", info->shopName);

        #undef GET_STR_FIELD
    }

    callback(items, count);

    // 释放内存
    for (NSUInteger i = 0; i < count; i++) {
        OrderItemInfo *info = &items[i];
        free(info->orderId);
        free(info->orderStatusShow);
        free(info->price);
        free(info->listPrice);
        free(info->venderId);
        free(info->shopId);
        free(info->shopName);
    }
    free(items);
}

// 一键收货执行获取订单列表的回调
void myOrderListCallback(OrderItemInfo *items, size_t count) {
    LOG("共 %zu 个订单", count);
    for (size_t i = 0; i < count; i++) {
        OrderItemInfo *info = &items[i];
        (void)info;
        LOG("订单 %zu:", i+1);
        LOG("  orderType: %d", info->orderType);
        LOG("  orderStatusId: %d", info->orderStatusId);
        LOG("  wareCount: %d", info->wareCount);
        LOG("  orderId: %s", info->orderId ?: "");
        LOG("  orderStatusShow: %s", info->orderStatusShow ?: "");
        LOG("  price: %s", info->price ?: "");
        LOG("  listPrice: %s", info->listPrice ?: "");
        LOG("  venderId: %s", info->venderId ?: "");
        LOG("  shopId: %s", info->shopId ?: "");
        LOG("  shopName: %s", info->shopName ?: "");
    }
}


// 执行一键确认收货
void oneClickConfirmOrder(bool success) {
    if (success) {
        LOG("✅ 成功跳转到待收货");
        // 延时1秒执行
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 读取订单列表
            id topVC = getTopViewController();
            getOrderItems(topVC, myOrderListCallback);
        });
    } else {
        LOG("❌ 跳转失败");
    }
}

// ----------------------------------------------------------------------

bool orderManager(const char *buttonId, const char *serialNumber) {
    if (strcmp(buttonId, "wait4Delivery") == 0) {
        openURLSchemeOrderList(3);
    } else if (strcmp(buttonId, "commentCenter") == 0) {
        openURLSchemeCommontCenter();
    } else if (strcmp(buttonId, "specialComment") == 0) {
        // 指定评论
        handleInputCommentWithOrderInfo(1, serialNumber);
    } else if (strcmp(buttonId, "generalComment") == 0) {
        // 通用评语
        handleInputCommentWithOrderInfo(2, serialNumber);
    } else if (strcmp(buttonId, "confirmOrder") == 0) {
        // 执行一键多线程确认收货
        showToastMessage("功能未开启", 1.0);
        // afterJumpListSuccess(1, oneClickConfirmOrder);
        // id topVC = getTopViewController();
        // getOrderList(topVC);
        // confirmOrderWithIdOrderType(@(238462090612), @(22));
    }
    return true;
}

 // 一键收货
    // 逻辑: 
    /**
     * 1.通过URLScheme直接载入待收货列表中
     * 2.获取
     */

    // 这是我的测试代码，但是我需要你帮我重新封装一下，
    // 不在使用getTopViewController来获取topVC,
    // 而是直接反射获取JDOrderListVC类，这个类下面的confirmManager
    // 同时传入的值为两个，orderid和ordertype

    // id topVC = getTopViewController();
    // SEL mgrSel = sel_registerName("confirmManager");
    // id manager = nil;
    // if ([topVC respondsToSelector:mgrSel]) {
    //     manager = ((id (*)(id, SEL))objc_msgSend)(topVC, mgrSel);
    // }
    // NSDictionary *params = @{
    //     @"orderId": @(318426883606),
    //     @"orderType": @(22)
    // };
    // SEL confirmSel = sel_registerName("confirmOrderWithParams:");
    // if ([manager respondsToSelector:confirmSel]) {
    //     ((void (*)(id, SEL, id))objc_msgSend)(manager, confirmSel, params);
    // }