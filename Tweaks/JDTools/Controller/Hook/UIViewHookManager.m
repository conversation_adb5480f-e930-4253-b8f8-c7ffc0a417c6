#import "UIViewHookManager.h"
#import <UIKit/UIKit.h>
#import <objc/runtime.h>
#import <objc/message.h>
#import "JDControllerUtils.h"

static void (*original_addSubview)(id self, SEL _cmd, UIView *view);


void describeViewHierarchy(UIView *view, NSString *prefix) {
    if (!view) return;

    NSMutableString *description = [NSMutableString stringWithFormat:@"%@%@ (Frame: %@)",
                                   prefix,
                                   NSStringFromClass([view class]),
                                   NSStringFromCGRect(view.frame)];

    BOOL didTriggerAction = NO;

    // 检查 UILabel（或 JDLabel）
    if ([view isKindOfClass:[UILabel class]]) {
        UILabel *label = (UILabel *)view;
        if (label.text.length > 0) {
            [description appendFormat:@" Text: '%@'", label.text];

            if ([label.text hasPrefix:@"详细地址应属于"] || [label.text hasPrefix:@"活动参与太热情，正在努力加载"]) {
                // NSLog(@"✅ 匹配到提示文本: %@", label.text);

                UIView *parent = view.superview;
                if (parent) {
                    for (UIView *sub in parent.subviews) {
                        if ([sub isKindOfClass:[UIButton class]]) {
                            UIButton *btn = (UIButton *)sub;
                            NSString *title = [btn titleForState:UIControlStateNormal];
                            // NSLog(@"🔍 检查按钮: %@", title);

                            if ([title isEqualToString:@"不用"] || [title isEqualToString:@"好的"]) {
                                // NSLog(@"🎯 模拟点击按钮: %@", title);

                                // 方案 1: 直接触发点击事件
                                [btn sendActionsForControlEvents:UIControlEventTouchUpInside];

                                // 方案 2: 如果有自定义 selector 可用
                                // SEL customSel = sel_registerName("switchAction:");
                                // if ([btn respondsToSelector:customSel]) {
                                //     ((void (*)(id, SEL, id))objc_msgSend)(btn, customSel, btn);
                                //     NSLog(@"📣 调用 switchAction: %@", title);
                                // }

                                didTriggerAction = YES;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
    // 其他类型打印
    else if ([view isKindOfClass:[UIButton class]]) {
        UIButton *button = (UIButton *)view;
        NSString *buttonTitle = [button titleForState:UIControlStateNormal];
        if (buttonTitle.length > 0) {
            [description appendFormat:@" Title: '%@'", buttonTitle];
        }
        if (button.currentTitle.length > 0) {
            [description appendFormat:@" CurrentTitle: '%@'", button.currentTitle];
        }
    }
    else if ([view isKindOfClass:[UITextField class]]) {
        UITextField *textField = (UITextField *)view;
        if (textField.text.length > 0) {
            [description appendFormat:@" Text: '%@'", textField.text];
        } else if (textField.placeholder.length > 0) {
            [description appendFormat:@" Placeholder: '%@'", textField.placeholder];
        }
    }
    else if ([view isKindOfClass:[UITextView class]]) {
        UITextView *textView = (UITextView *)view;
        if (textView.text.length > 0) {
            [description appendFormat:@" Text: '%@'", textView.text];
        }
    }

    // 输出当前 view 的描述
    // NSLog(@"%@", description);

    // 递归子视图（如果没有触发点击才继续）
    if (!didTriggerAction) {
        for (UIView *subview in view.subviews) {
            describeViewHierarchy(subview, [prefix stringByAppendingString:@"  "]);
        }
    }
}

// 新的实现
static void replaced_addSubview(id self, SEL _cmd, UIView *view) {
    // NSLog(@"✅ [UIView addSubview:] -> %@ 添加了 %@", self, view);
    
    // 打印 view 内容
    describeViewHierarchy(view, @" ");
    
    // 调用原始实现
    if (original_addSubview) {
        original_addSubview(self, _cmd, view);
    }
}


@implementation UIViewHookManager

+ (void)start {
    Class cls = objc_getClass("UIView");
    SEL sel = @selector(addSubview:);
    Method method = class_getInstanceMethod(cls, sel);
    if (method) {
        original_addSubview = (void *)method_getImplementation(method);
        method_setImplementation(method, (IMP)replaced_addSubview);
    } else {
        NSLog(@"❌ 未找到 UIView 的 addSubview 方法，Hook 失败");
    }
}
@end
