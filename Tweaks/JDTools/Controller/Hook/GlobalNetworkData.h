#import <Foundation/Foundation.h>
#import "DataRecordManager.h"

extern DataRecordManager *s_networkMonitorInstance;

// 声明全局变量（使用 extern 关键字）
extern NSMutableDictionary<NSString *, NSMutableDictionary *> *g_collectedNetworkData;

// 可选：提供便捷的访问函数
NSMutableDictionary<NSString *, NSMutableDictionary *> *getGlobalNetworkData(void);

void initializeGlobalNetworkData(void);


/**
调用方式
// #import "GlobalNetworkData.h"

static DataRecordManager *s_networkMonitorInstance = nil;


__attribute__((constructor)) static void initializeCPLResourceIdentityHooks() {
    // 启动数据收集
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 初始化全局网络数据
        initializeGlobalNetworkData();
        // 创建 AFURLSessionManager 实例，传入全局变量
        s_networkMonitorInstance = [[DataRecordManager alloc] initWithDataStore:g_collectedNetworkData];
        // 启动网络监控
        [s_networkMonitorInstance startMonitoring];
    });

}

// 任何文件中获取数据方式
NSMutableDictionary *data = getGlobalNetworkData();
NSMutableDictionary *specificData = data[@"personinfoBusiness"];
NSLog(@"personinfoBusiness------------->: %@", specificData);

*/