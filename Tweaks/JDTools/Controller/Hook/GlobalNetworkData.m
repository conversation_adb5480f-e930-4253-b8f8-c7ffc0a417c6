#import "GlobalNetworkData.h"

// 定义全局变量（实际分配内存）
NSMutableDictionary<NSString *, NSMutableDictionary *> *g_collectedNetworkData = nil;

// 便捷访问函数
NSMutableDictionary<NSString *, NSMutableDictionary *> *getGlobalNetworkData(void) {
    if (!g_collectedNetworkData) {
        initializeGlobalNetworkData();
    }
    return g_collectedNetworkData;
}

void initializeGlobalNetworkData(void) {
    if (!g_collectedNetworkData) {
        g_collectedNetworkData = [NSMutableDictionary dictionary];
    }
}