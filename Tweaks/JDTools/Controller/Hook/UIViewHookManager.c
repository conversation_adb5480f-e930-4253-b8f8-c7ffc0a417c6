#include "UIViewHookManager.h"
#include <objc/runtime.h> // Objective-C Runtime C API
#include <objc/message.h> // Objective-C 消息发送 C API
#include <stdio.h>        // for printf
#include <string.h>       // for strstr, strcmp
#include <stdlib.h>       // for malloc, free
#include <syslog.h>

// 定义一些Objective-C类型
typedef struct objc_object *id;
typedef struct objc_selector *SEL;
typedef struct objc_class *Class;

// C 函数指针，用于保存原始的 addSubview 实现
static void (*original_addSubview_ptr)(id self, SEL _cmd, id view_arg);

// 辅助函数：判断一个类是否是另一个类的子类（包括自身）
static bool is_subclass_of_class(Class cls, Class superclass_cls) {
    if (!cls || !superclass_cls) {
        return false;
    }
    if (cls == superclass_cls) {
        return true;
    }
    Class current_cls = cls;
    while ((current_cls = class_getSuperclass(current_cls)) != NULL) {
        if (current_cls == superclass_cls) {
            return true;
        }
    }
    return false;
}

// 辅助函数：获取OC对象的类名C字符串
const char* get_class_name(id obj) {
    if (!obj) return "NULL";
    return class_getName(object_getClass(obj));
}

// 辅助函数：将OC NSString转换为C char*
// 注意：返回的指针生命周期与原始NSString对象相同，仅用于立即打印
const char* oc_string_to_c_string(id oc_str) {
    if (!oc_str) return "";
    SEL utf8String_sel = sel_registerName("UTF8String");
    if (!utf8String_sel) {
        return "ERROR: UTF8String selector not found";
    }
    const char* c_str = ((const char*(*)(id, SEL))objc_msgSend)(oc_str, utf8String_sel);
    return c_str ? c_str : "";
}

// 递归描述视图层级（简化版，不包含坐标）
void describeViewHierarchy_c(id view, const char *prefix) {
    if (!view) return;

    // const char *class_name = get_class_name(view);

    // 仅打印视图的类名
    // syslog(LOG_NOTICE, "%s%s", prefix, class_name);

    bool didTriggerAction = false;

    Class UILabel_cls = objc_getClass("UILabel");
    if (UILabel_cls && is_subclass_of_class(object_getClass(view), UILabel_cls)) {
        SEL text_sel = sel_registerName("text");
        id label_text_oc = ((id (*)(id, SEL))objc_msgSend)(view, text_sel);
        const char *label_text = oc_string_to_c_string(label_text_oc);

        if (label_text && strlen(label_text) > 0) {
            // syslog(LOG_DEBUG, " Text: '%s'", label_text);

            if (strstr(label_text, "详细地址应属于") || strstr(label_text, "活动参与太热情，正在努力加载")) {
                syslog(LOG_DEBUG, " ✅ 匹配到提示文本: %s", label_text);

                SEL superview_sel = sel_registerName("superview");
                id parent_view = ((id (*)(id, SEL))objc_msgSend)(view, superview_sel);

                if (parent_view) {
                    SEL subviews_sel = sel_registerName("subviews");
                    id subviews_array = ((id (*)(id, SEL))objc_msgSend)(parent_view, subviews_sel);
                    
                    Class UIButton_cls = objc_getClass("UIButton");
                    if (UIButton_cls && subviews_array) {
                        SEL count_sel = sel_registerName("count");
                        unsigned long count = ((unsigned long (*)(id, SEL))objc_msgSend)(subviews_array, count_sel);
                        SEL objectAtIndex_sel = sel_registerName("objectAtIndex:");

                        for (unsigned long i = 0; i < count; i++) {
                            id sub = ((id (*)(id, SEL, unsigned long))objc_msgSend)(subviews_array, objectAtIndex_sel, i);
                            
                            if (sub && is_subclass_of_class(object_getClass(sub), UIButton_cls)) {
                                id button = sub;
                                SEL title_for_state_sel = sel_registerName("titleForState:");
                                id button_title_oc = ((id (*)(id, SEL, unsigned long))objc_msgSend)(button, title_for_state_sel, 0 /* UIControlStateNormal */);
                                const char *button_title = oc_string_to_c_string(button_title_oc);

                                if (button_title && (strcmp(button_title, "不用") == 0 || strcmp(button_title, "好的") == 0)) {
                                    // syslog(LOG_NOTICE, " 🎯 模拟点击按钮: %s", button_title);
                                    
                                    SEL send_actions_sel = sel_registerName("sendActionsForControlEvents:");
                                    ((void (*)(id, SEL, unsigned long))objc_msgSend)(button, send_actions_sel, 1 << 6 /* UIControlEventTouchUpInside */);

                                    didTriggerAction = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 递归子视图（如果没有触发点击才继续）
    if (!didTriggerAction) {
        SEL subviews_sel = sel_registerName("subviews");
        id subviews_array = ((id (*)(id, SEL))objc_msgSend)(view, subviews_sel);
        
        if (subviews_array) {
            SEL count_sel = sel_registerName("count");
            unsigned long count = ((unsigned long (*)(id, SEL))objc_msgSend)(subviews_array, count_sel);
            SEL objectAtIndex_sel = sel_registerName("objectAtIndex:");

            for (unsigned long i = 0; i < count; i++) {
                id subview_in_array = ((id (*)(id, SEL, unsigned long))objc_msgSend)(subviews_array, objectAtIndex_sel, i);
                char new_prefix[256];
                snprintf(new_prefix, sizeof(new_prefix), "%s  ", prefix);
                describeViewHierarchy_c(subview_in_array, new_prefix);
            }
        }
    }
}

// 新的 addSubview 实现（C 函数）
static void replaced_addSubview_c(id self, SEL _cmd, id view_arg) {
    // syslog(LOG_DEBUG, "✅ [UIView addSubview:] -> %s 添加了 %s\n", get_class_name(self), get_class_name(view_arg));

    // 打印 view 内容
    describeViewHierarchy_c(view_arg, " ");

    // 调用原始实现
    if (original_addSubview_ptr) {
        original_addSubview_ptr(self, _cmd, view_arg);
    }
}

// 启动 Hook 的 C 函数
void start_uiview_hook(void) {
    Class cls = objc_getClass("UIView");
    if (!cls) {
        syslog(LOG_NOTICE, "未找到 UIView 类，Hook 失败");
        return;
    }

    SEL sel = sel_registerName("addSubview:");
    Method method = class_getInstanceMethod(cls, sel);
    if (method) {
        original_addSubview_ptr = (void *)method_getImplementation(method);
        method_setImplementation(method, (IMP)replaced_addSubview_c);
    } else {
        syslog(LOG_NOTICE, "未找到 UIView 的 addSubview 方法，Hook 失败");
    }
}