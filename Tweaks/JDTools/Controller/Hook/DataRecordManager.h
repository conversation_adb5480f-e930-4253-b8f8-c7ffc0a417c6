#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DataRecordManager : NSObject

/**
 * 存储网络数据的字典。
 * 此属性为只读，表示在初始化后不能替换字典对象本身。
 * 字典内容将由监控器动态更新。
 *
 * 结构：
 * 键 (Key)：functionId (NSString) 或对于没有 functionId 的请求，使用 "__NoFunctionId__"。
 * 值 (Value)：NSMutableDictionary。每个值代表该 functionId 下的最新请求/响应记录。
 * 记录键 (Record Keys)："requestURL"、"requestHeaders"、"requestBody"、"statusCode"、"responseBody"、"error"、"errorCode"、"errorDomain"。
 */
@property (nonatomic, strong, readonly) NSMutableDictionary<NSString *, NSMutableDictionary *> *dataStore;

/**
 * DataRecordManager 的指定初始化方法。
 * 使用一个预先存在的 NSMutableDictionary 来初始化网络数据监控器，用于数据存储。
 *
 * @param dataStore 存储所有收集到的网络数据的可变字典。
 * 它将填充 functionId 键，每个键对应一个最新的请求/响应记录字典。
 * 此字典将被监控器强引用。
 * @return DataRecordManager 的一个初始化实例。
 */
- (instancetype)initWithDataStore:(NSMutableDictionary<NSString *, NSMutableDictionary *> *)dataStore NS_DESIGNATED_INITIALIZER;

/**
 * 默认初始化方法不可用。请使用 initWithDataStore: 代替。
 */
- (instancetype)init NS_UNAVAILABLE;

/**
 * 开始监控 DataRecordManager 的网络请求。
 * 此方法使用 MobileSubstrate 应用方法 Hook。
 * 通常设计为在应用程序生命周期内只调用一次。
 * 收集到的数据将存储在初始化时提供的 `dataStore` 中。
 *
 * 注意：如果使用不同实例多次调用，只有第一次调用会应用 Hook，
 * 并且该 Hook 将把数据导向第一次调用 `startMonitoring` 的实例的 `dataStore`。
 */
- (void)startMonitoring;


- (void)updateStatus:(BOOL)status;

@end

NS_ASSUME_NONNULL_END

/**
    调用方式:
    #import "DataRecordManager.h"

    static NSMutableDictionary<NSString *, NSMutableDictionary *> *g_collectedNetworkData;

    // 持有 DataRecordManager 实例，确保它在应用程序生命周期内保持活跃。
    // 静态变量是 Tweak 中常见的持有方式，以防止实例被过早释放。
    static DataRecordManager *s_networkMonitorInstance = nil;

    // 打印日志，确认 Tweak 已开始初始化
    NSLog(@"[CPLResourceIdentityHooks] 正在初始化 DataRecordManager...");

   __attribute__((constructor)) static void initializeCPLResourceIdentityHooks() {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 打印日志，确认 Tweak 已开始初始化
                NSLog(@"[CPLResourceIdentityHooks] 正在初始化 DataRecordManager...");

                // 1. 初始化你的日志字典。
                // 注意：这里的类型声明必须与 DataRecordManager.h 和 .m 中的 dataStore 类型一致。
                g_collectedNetworkData = [NSMutableDictionary dictionary];

                // 2. 创建 DataRecordManager 实例，并传入你创建的字典。
                // 将其赋值给静态变量 s_networkMonitorInstance，以保持其强引用，防止被 ARC 释放。
                s_networkMonitorInstance = [[DataRecordManager alloc] initWithDataStore:g_collectedNetworkData];
                
                // 3. 启动网络监控。
                // 这将应用 DataRecordManager 上的 Hook。
                [s_networkMonitorInstance startMonitoring];

                NSLog(@"[CPLResourceIdentityHooks] DataRecordManager 设置完成。数据将收集到 g_collectedNetworkData。");

                // 从此以后，你可以通过访问全局变量 g_collectedNetworkData 来读取所有收集到的最新网络数据。
                // 例如，你可以在其他地方（如一个自定义 UI 按钮点击事件，或者定时器回调）调用一个函数来处理这些数据。

            });
    };
*/