#import "AFURLSessionManager.h"
#import <substrate.h>
#import <objc/runtime.h>

// --- Hook 函数的内部全局状态 ---
// 这个全局指针将由 AFURLSessionManager 实例设置，
// 指向调用者提供的 dataStore。这是连接
// C 风格 Hook 函数与 Objective-C 实例字典的桥梁。
// 类型已从 NSMutableArray<NSDictionary *> * 更改为 NSMutableDictionary *，以存储单个最新记录。
static NSMutableDictionary<NSString *, NSMutableDictionary *> *s_activeNetworkDataStore = nil;

// 一个 dispatch_once token，用于确保 MSHookMessageEx 在应用程序的整个生命周期内只应用一次。
static dispatch_once_t s_hookApplyOnceToken;

// --- Block 类型定义 (从 AFNetworking 复制) ---
// 这些定义至关重要，必须与 AFNetworking 库的内部类型精确匹配。
// 如果你的目标应用程序使用不同的 AFNetworking 版本，可能需要进行调整。
typedef void (^AFURLSessionTaskProgressBlock)(NSProgress *progress);
typedef void (^AFURLSessionDataTaskCompletionHandler)(NSURLResponse *response, id responseObject, NSError *error);

// --- 原始方法指针 ---
// 这个指针将保存 AFURLSessionManager 原始方法的实现。
// 其签名必须与我们 Hook 的方法完全匹配。
static NSURLSessionDataTask *(*original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_)(
    id self,
    SEL _cmd,
    NSURLRequest *request,
    AFURLSessionTaskProgressBlock _Nullable uploadProgress,
    AFURLSessionTaskProgressBlock _Nullable downloadProgress,
    AFURLSessionDataTaskCompletionHandler _Nullable completionHandler);



// 辅助函数：从 URL 查询参数中提取 functionId。
static NSString *extractFunctionIdFromURL(NSString *urlString) {
    NSURLComponents *urlComponents = [NSURLComponents componentsWithString:urlString];
    for (NSURLQueryItem *item in urlComponents.queryItems) {
        if ([item.name isEqualToString:@"functionId"]) {
            return item.value;
        }
    }
    return nil;
}

// 辅助函数：将 NSData 转换为 UTF8 NSString。如果数据为 nil 或无法进行 UTF8 解码，则返回 nil。
static NSString *stringFromData(NSData *data) {
    if (!data) return nil;
    return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
}

// 辅助函数：将 JSON 对象（或 NSData）转换为美化格式的 JSON NSString。
static NSString *jsonStringFromObject(id object) {
    if (!object) return nil;

    id jsonObject = object;     // 假设输入已经是 JSON 对象 (NSDictionary/NSArray)
    NSData *originalData = nil; // 存储原始 NSData，如果输入 'object' 是 NSData 类型

    // 如果输入 'object' 是 NSData，尝试首先将其解析为 JSON 对象。
    if ([object isKindOfClass:[NSData class]]) {
        originalData = object;
        NSError *parseError = nil;
        // NSJSONReadingAllowFragments 对于处理顶级数组或非字典 JSON 很重要。
        jsonObject = [NSJSONSerialization JSONObjectWithData:object options:NSJSONReadingAllowFragments error:&parseError];
        if (parseError) {
            // 如果解析失败，则它不是有效的 JSON 数据或不是我们期望的 JSON 结构。
            // 如果可能，返回其字符串表示，否则返回其描述。
            return stringFromData(originalData) ?: [originalData description];
        }
    }

    // 此时，'jsonObject' 应该是 NSDictionary 或 NSArray（如果成功解析/原始）。
    // 检查它是否是有效的 JSON 对象类型以进行序列化。
    if ([NSJSONSerialization isValidJSONObject:jsonObject]) {
        NSError *jsonError = nil;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:NSJSONWritingPrettyPrinted error:&jsonError];
        if (jsonData && !jsonError) {
            return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        } else {
            // 重新序列化失败，返回对象的描述作为备用。
            return [jsonObject description];
        }
    }
    
    // 如果 'object' 既不是 NSData 也不是有效的 JSON 对象（例如，NSString, NSNumber），
    // 则返回其描述。
    return [object description];
}

// --- 新方法实现 (实际的 Hook 逻辑) ---
// 这个 C 风格的函数将替代 AFURLSessionManager 的原始方法。
// 它使用全局设置的 's_activeNetworkDataStore' 来访问调用者的字典。
static NSURLSessionDataTask *new_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_(
    id self,
    SEL _cmd,
    NSURLRequest *request,
    AFURLSessionTaskProgressBlock _Nullable uploadProgressBlock,
    AFURLSessionTaskProgressBlock _Nullable downloadProgressBlock,
    AFURLSessionDataTaskCompletionHandler _Nullable completionHandler) {

    // 检查监控器实例是否已设置全局数据存储。
    if (s_activeNetworkDataStore) {
        // --- 数据收集逻辑 ---
        NSString *requestURLString = request.URL.absoluteString;
        NSString *functionId = extractFunctionIdFromURL(requestURLString);
        NSLog(@"[record]: %@", functionId);

        // 为当前请求创建一个可变字典，存储其所有数据
        // 这个 currentRecord 将是最终被存储的单个记录
        NSMutableDictionary *currentRecord = [NSMutableDictionary dictionary]; 
        
        currentRecord[@"requestURL"] = requestURLString;
        if (request.allHTTPHeaderFields) {
            currentRecord[@"requestHeaders"] = request.allHTTPHeaderFields;
        }
        if (request.HTTPBody) {
            currentRecord[@"requestBody"] = stringFromData(request.HTTPBody) ?: @"[二进制数据或非UTF8编码]";
        }

        // 包装原始的 completionHandler，以便在请求完成后收集响应数据。
        AFURLSessionDataTaskCompletionHandler newCompletionHandler = ^(NSURLResponse *response, id responseObject, NSError *error) {
            
            if ([response isKindOfClass:[NSHTTPURLResponse class]]) {
                NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
                currentRecord[@"statusCode"] = @(httpResponse.statusCode); // 记录 HTTP 状态码
            }
            
            if (responseObject) {
                currentRecord[@"responseBody"] = jsonStringFromObject(responseObject);
            } else if (error) {
                // 如果有错误，记录其详细信息。
                currentRecord[@"error"] = error.localizedDescription ?: @"未知错误";
                currentRecord[@"errorCode"] = @(error.code);
                currentRecord[@"errorDomain"] = error.domain;
            }

            // --- 关键修改：直接覆盖记录，而不是追加到数组 ---
            // 将收集到的数据存储到全局字典（它指向调用者的字典）。
            // 使用 @synchronized 确保修改共享字典时的线程安全。
            @synchronized (s_activeNetworkDataStore) {
                NSString *keyToStore = functionId ?: @"__NoFunctionId__"; 
                // 直接赋值，覆盖旧记录。每个 functionId 只保留最新的一条数据。
                s_activeNetworkDataStore[keyToStore] = currentRecord; 
            }
            
            // 可以选择性地打印一条日志，确认数据被收集
            // NSLog(@"[数据收集器] 已为 %@ 覆盖并存储最新数据。", functionId ?: @"无functionId请求");


            // 调用原始的 completionHandler，确保应用程序的正常流程继续。
            if (completionHandler) {
                completionHandler(response, responseObject, error);
            }
        };

        // 调用原始的 AFURLSessionManager 方法，传入我们新的 completionHandler。
        // 进度 Block 按照简化要求直接传递。
        NSURLSessionDataTask *task = original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_(
            self,
            _cmd,
            request,
            uploadProgressBlock,   // 原始上传进度 Block
            downloadProgressBlock, // 原始下载进度 Block
            newCompletionHandler   // 我们包装的完成回调 Block
        );
        
        return task;

    } else {
        // 如果 s_activeNetworkDataStore 为 nil，表示 AFURLSessionManager.startMonitoring
        // 尚未调用，或 Hook 初始化失败。
        // 在这种情况下，只需调用原始方法，不进行任何数据收集。
        return original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_(
            self,
            _cmd,
            request,
            uploadProgressBlock,
            downloadProgressBlock,
            completionHandler
        );
    }
}


// --- AFURLSessionManager 类实现 ---
@implementation AFURLSessionManager

// 通过带下划线的成员变量合成 dataStore 属性。
// 这确保 _dataStore 由 ARC 管理并正确保留。
@synthesize dataStore = _dataStore;

// 指定初始化方法
- (instancetype)initWithDataStore:(NSMutableDictionary<NSString *, NSMutableDictionary *> *)dataStore { // 参数类型已修改
    self = [super init];
    if (self) {
        // 将调用者的字典分配给我们的实例属性。
        // 这是一个强引用，因此监控器实例会持有该字典。
        _dataStore = dataStore;
    }
    return self;
}

// 重写默认的 init 方法，强制使用指定初始化方法。
- (instancetype)init {
    @throw [NSException exceptionWithName:NSInternalInconsistencyException
                                   reason:@"-init 不是 AFURLSessionManager 的有效初始化方法。请改用 -initWithDataStore:。"
                                 userInfo:nil];
    return nil;
}

// 启动监控过程的方法。
- (void)startMonitoring {
    // 将全局静态指针设置为此实例的 dataStore。
    // 这使得调用者的字典可供 C 风格的 Hook 函数访问。
    // 注意：如果多个 AFURLSessionManager 实例调用 startMonitoring，
    // s_activeNetworkDataStore 将指向最后调用它的实例的 dataStore。
    // 但是，MSHookMessageEx 本身只应用一次。
    s_activeNetworkDataStore = self.dataStore;

    // 使用 dispatch_once 确保 MSHookMessageEx 只在应用程序的整个生命周期内调用一次，
    // 无论创建了多少 AFURLSessionManager 实例或调用了多少次 startMonitoring。
    dispatch_once(&s_hookApplyOnceToken, ^{
        Class targetClass = objc_getClass("AFURLSessionManager");
        if (!targetClass) {
            NSLog(@"[AFURLSessionManager] ❌ 初始化失败：未找到 'AFURLSessionManager' 类。网络监控将不会启动。");
            s_activeNetworkDataStore = nil; // 如果未找到目标类，则清除全局存储
            return;
        }

        // 定义要 Hook 的方法的选择器。
        SEL selector = @selector(dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:);
        Method method = class_getInstanceMethod(targetClass, selector);

        if (method) {
            // 使用 MobileSubstrate 应用 Hook。
            // 用我们的 new_ 函数替换原始方法的实现，
            // 并保存原始实现的指针。
            MSHookMessageEx(targetClass, selector, (IMP)new_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_, (IMP *)&original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_);
        } else {
            NSLog(@"[AFURLSessionManager] ❌ Hook 方法失败：-[%s %s]。这通常意味着方法签名不正确或 AFNetworking 版本已更改。",
                  class_getName(targetClass), sel_getName(selector));
            s_activeNetworkDataStore = nil; // 如果 Hook 失败，则清除全局存储
        }
    });
}

@end

同样的实现这个功能，主要逻辑不变，主要是不能使用substrate，修改之后给我一个完整的代码。
不需要提供头文件，也不要修改主方法的名称，因为我的头文件已经存在。