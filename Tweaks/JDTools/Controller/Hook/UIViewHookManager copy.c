#include "UIViewHookManager.h"
#include <objc/runtime.h> // Objective-C Runtime C API
#include <objc/message.h> // Objective-C 消息发送 C API
#include <stdio.h>        // for printf (作为 NSLog 的替代)
#include <string.h>       // for strstr, strcmp
#include <stdlib.h>       // for malloc, free

// 定义一些Objective-C类型，因为纯C文件无法直接使用@class或id
typedef struct objc_object *id;
typedef struct objc_selector *SEL;
typedef struct objc_class *Class;
typedef struct CGRect CGRect; // 定义CGRect结构体，通常在CoreGraphics/CoreGraphics.h中定义
typedef struct CGSize CGSize;
typedef struct CGPoint CGPoint;

// 简化版的CGRect定义，实际在UIKit或CoreGraphics中
struct CGPoint {
    double x;
    double y;
};

struct CGSize {
    double width;
    double height;
};

struct CGRect {
    CGPoint origin;
    CGSize size;
};

// C 函数指针，用于保存原始的 addSubview 实现
static void (*original_addSubview_ptr)(id self, SEL _cmd, id view_arg);

// 辅助函数：判断一个类是否是另一个类的子类（包括自身）
// 这是对Objective-C [Class isSubclassOfClass:] 的C语言实现
static bool is_subclass_of_class(Class cls, Class superclass_cls) {
    if (!cls || !superclass_cls) {
        return false;
    }
    // 如果是同一个类，也认为是子类
    if (cls == superclass_cls) {
        return true;
    }
    // 遍历继承链
    Class current_cls = cls;
    while ((current_cls = class_getSuperclass(current_cls)) != NULL) {
        if (current_cls == superclass_cls) {
            return true;
        }
    }
    return false;
}


// 辅助函数，用于将OC对象转为C字符串，需要额外实现或依赖Foundation库
// 在纯C环境中，直接打印OC字符串需要更复杂的C-OC桥接
// 这里简化处理，假设有方法能获取C字符串，或者直接打印OC对象的description
const char* get_class_name(id obj) {
    if (!obj) return "NULL";
    return class_getName(object_getClass(obj));
}

// 这是一个简化版本，通常需要将OC NSString转换为C char*
// 在实际项目中，这可能需要更复杂的处理，例如使用 [str UTF8String]
// 警告：这个函数返回的指针在下次调用时可能失效，或者在OC对象释放后失效。
// 仅用于快速调试打印，生产环境需谨慎。
const char* oc_string_to_c_string(id oc_str) {
    if (!oc_str) return "";
    // 获取NSString的UTF8String方法选择器
    SEL utf8String_sel = sel_registerName("UTF8String");
    if (!utf8String_sel) {
        return "ERROR: UTF8String selector not found";
    }
    
    // 通过objc_msgSend调用UTF8String方法
    const char* c_str = ((const char*(*)(id, SEL))objc_msgSend)(oc_str, utf8String_sel);
    return c_str ? c_str : "";
}


// 递归描述视图层级（纯C + Runtime API版）
// 注意：这个函数会比Objective-C版本复杂很多，因为所有方法调用都需要通过objc_msgSend
void describeViewHierarchy_c(id view, const char *prefix) {
    if (!view) return;

    // 获取类名
    const char *class_name = get_class_name(view);

    // 获取frame（需要通过objc_msgSend获取frame属性）
    // 注意：获取结构体返回值需要使用 objc_msgSend_stret，这在C中更复杂且依赖ABI
    // 为了简化，这里我们假设能获取到frame的字符串表示，或者直接跳过精确打印frame
    // 如果需要精确打印，你需要更复杂的Runtime调用和结构体处理
    // 例如：CGRect frame; ((void (*)(CGRect*, id, SEL))objc_msgSend_stret)(&frame, view, sel_registerName("frame"));
    char frame_str[100];
    snprintf(frame_str, sizeof(frame_str), "{x=?.?, y=?.?, w=?.?, h=?.?}"); // 占位符，实际值需要通过objc_msgSend_stret获取

    printf("%s%s (Frame: %s)", prefix, class_name, frame_str);

    bool didTriggerAction = false;

    // 检查 UILabel (或其子类)
    Class UILabel_cls = objc_getClass("UILabel");
    if (UILabel_cls && is_subclass_of_class(object_getClass(view), UILabel_cls)) {
        SEL text_sel = sel_registerName("text");
        id label_text_oc = ((id (*)(id, SEL))objc_msgSend)(view, text_sel);
        const char *label_text = oc_string_to_c_string(label_text_oc);

        if (label_text && strlen(label_text) > 0) {
            printf(" Text: '%s'", label_text);

            if (strstr(label_text, "详细地址应属于") || strstr(label_text, "活动参与太热情，正在努力加载")) {
                printf(" ✅ 匹配到提示文本: %s\n", label_text);

                SEL superview_sel = sel_registerName("superview");
                id parent_view = ((id (*)(id, SEL))objc_msgSend)(view, superview_sel);

                if (parent_view) {
                    SEL subviews_sel = sel_registerName("subviews");
                    id subviews_array = ((id (*)(id, SEL))objc_msgSend)(parent_view, subviews_sel);
                    
                    Class UIButton_cls = objc_getClass("UIButton");
                    if (UIButton_cls && subviews_array) {
                        // 遍历NSArray需要知道其count和objectAtIndex:方法
                        // 这是通过Objective-C Runtime C API遍历NSArray的典型方式
                        SEL count_sel = sel_registerName("count");
                        unsigned long count = ((unsigned long (*)(id, SEL))objc_msgSend)(subviews_array, count_sel);
                        SEL objectAtIndex_sel = sel_registerName("objectAtIndex:");

                        for (unsigned long i = 0; i < count; i++) {
                            id sub = ((id (*)(id, SEL, unsigned long))objc_msgSend)(subviews_array, objectAtIndex_sel, i);
                            
                            // 检查子视图是否是UIButton或其子类
                            if (sub && is_subclass_of_class(object_getClass(sub), UIButton_cls)) {
                                id button = sub;
                                SEL title_for_state_sel = sel_registerName("titleForState:");
                                // UIControlStateNormal 通常是 0
                                id button_title_oc = ((id (*)(id, SEL, unsigned long))objc_msgSend)(button, title_for_state_sel, 0 /* UIControlStateNormal */);
                                const char *button_title = oc_string_to_c_string(button_title_oc);

                                if (button_title && (strcmp(button_title, "不用") == 0 || strcmp(button_title, "好的") == 0)) {
                                    printf(" 🎯 模拟点击按钮: %s\n", button_title);
                                    
                                    // 模拟点击事件: [btn sendActionsForControlEvents:UIControlEventTouchUpInside];
                                    SEL send_actions_sel = sel_registerName("sendActionsForControlEvents:");
                                    // UIControlEventTouchUpInside 的值为 1 << 6
                                    ((void (*)(id, SEL, unsigned long))objc_msgSend)(button, send_actions_sel, 1 << 6 /* UIControlEventTouchUpInside */);

                                    didTriggerAction = true;
                                    break; // 找到并点击一个按钮后退出循环
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    // 其他类型（纯C中处理这些会更复杂，需要更多objc_msgSend调用）
    // 例如检查UIButton：
    else if (object_getClass(view) && is_subclass_of_class(object_getClass(view), objc_getClass("UIButton"))) {
        id button = view;
        SEL title_for_state_sel = sel_registerName("titleForState:");
        id button_title_oc = ((id (*)(id, SEL, unsigned long))objc_msgSend)(button, title_for_state_sel, 0 /* UIControlStateNormal */);
        const char *button_title = oc_string_to_c_string(button_title_oc);
        if (button_title && strlen(button_title) > 0) {
            printf(" Title: '%s'", button_title);
        }
        // 对于 currentTitle，也需要类似方法调用
        SEL current_title_sel = sel_registerName("currentTitle");
        id current_title_oc = ((id (*)(id, SEL))objc_msgSend)(button, current_title_sel);
        const char *current_title = oc_string_to_c_string(current_title_oc);
        if (current_title && strlen(current_title) > 0) {
            printf(" CurrentTitle: '%s'", current_title);
        }
    }
    // 例如检查UITextField：
    else if (object_getClass(view) && is_subclass_of_class(object_getClass(view), objc_getClass("UITextField"))) {
        id textField = view;
        SEL text_sel = sel_registerName("text");
        id text_oc = ((id (*)(id, SEL))objc_msgSend)(textField, text_sel);
        const char *text = oc_string_to_c_string(text_oc);
        if (text && strlen(text) > 0) {
            printf(" Text: '%s'", text);
        } else {
            SEL placeholder_sel = sel_registerName("placeholder");
            id placeholder_oc = ((id (*)(id, SEL))objc_msgSend)(textField, placeholder_sel);
            const char *placeholder = oc_string_to_c_string(placeholder_oc);
            if (placeholder && strlen(placeholder) > 0) {
                printf(" Placeholder: '%s'", placeholder);
            }
        }
    }
    // 例如检查UITextView：
    else if (object_getClass(view) && is_subclass_of_class(object_getClass(view), objc_getClass("UITextView"))) {
        id textView = view;
        SEL text_sel = sel_registerName("text");
        id text_oc = ((id (*)(id, SEL))objc_msgSend)(textView, text_sel);
        const char *text = oc_string_to_c_string(text_oc);
        if (text && strlen(text) > 0) {
            printf(" Text: '%s'", text);
        }
    }


    printf("\n"); // 打印描述后换行

    // 递归子视图（如果没有触发点击才继续）
    if (!didTriggerAction) {
        SEL subviews_sel = sel_registerName("subviews");
        id subviews_array = ((id (*)(id, SEL))objc_msgSend)(view, subviews_sel);
        
        if (subviews_array) {
            // 遍历NSArray在纯C中是复杂的，这里依然是简化示例
            // 实际需要：获取count，循环调用objectAtIndex:
            SEL count_sel = sel_registerName("count");
            unsigned long count = ((unsigned long (*)(id, SEL))objc_msgSend)(subviews_array, count_sel);
            SEL objectAtIndex_sel = sel_registerName("objectAtIndex:");

            for (unsigned long i = 0; i < count; i++) {
                id subview_in_array = ((id (*)(id, SEL, unsigned long))objc_msgSend)(subviews_array, objectAtIndex_sel, i);
                char new_prefix[256];
                snprintf(new_prefix, sizeof(new_prefix), "%s  ", prefix);
                describeViewHierarchy_c(subview_in_array, new_prefix);
            }
        }
    }
}


// 新的 addSubview 实现（C 函数）
static void replaced_addSubview_c(id self, SEL _cmd, id view_arg) {
    // printf 替代 NSLog
    printf("✅ [UIView addSubview:] -> %s 添加了 %s\n", get_class_name(self), get_class_name(view_arg));

    // 打印 view 内容
    describeViewHierarchy_c(view_arg, " ");

    // 调用原始实现
    if (original_addSubview_ptr) {
        original_addSubview_ptr(self, _cmd, view_arg);
    }
}

// 启动 Hook 的 C 函数
void start_uiview_hook(void) {
    Class cls = objc_getClass("UIView");
    if (!cls) {
        printf("❌ 未找到 UIView 类，Hook 失败\n");
        return;
    }

    SEL sel = sel_registerName("addSubview:");
    Method method = class_getInstanceMethod(cls, sel);
    if (method) {
        // 获取原始实现
        original_addSubview_ptr = (void *)method_getImplementation(method);
        // 替换实现
        method_setImplementation(method, (IMP)replaced_addSubview_c);
        printf("✅ UIView 的 addSubview 方法 Hook 成功\n");
    } else {
        printf("❌ 未找到 UIView 的 addSubview 方法，Hook 失败\n");
    }
}