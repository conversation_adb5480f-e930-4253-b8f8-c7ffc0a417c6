// #import <Foundation/Foundation.h>


// @interface UIViewHookManager : NSObject

// /**
//  * 启动UIView addSubview Hook监控
//  */
// + (void)start;


// @end



#ifndef UIViewHookManager_h
#define UIViewHookManager_h

#include <stdbool.h> // for bool type

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 启动 UIView 的 addSubview 方法的 Hook。
 * 此函数使用 Objective-C Runtime C API 进行方法替换。
 * 请确保在 Objective-C 环境中调用此函数。
 */
void start_uiview_hook(void);
// void startUIViewAddSubviewHook(void);

#ifdef __cplusplus
}
#endif

#endif /* UIViewHookManager_h */

/**

// 监听添加窗口
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIViewHookManager start];
    });*/
    