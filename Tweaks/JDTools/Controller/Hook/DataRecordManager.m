#import "DataRecordManager.h" // 导入 DataRecordManager 的头文件，该类负责网络数据记录的管理
#import <objc/runtime.h>       // 导入 Objective-C 运行时头文件，提供动态操作类和方法的功能（如方法交换）
#import <objc/message.h>      // 导入 Objective-C 消息发送头文件，通常与运行时操作配合使用
#import "QR.h"

static BOOL isHowQRCode = NO;

// 静态全局变量：用于存储活跃的网络请求数据记录的字典。
// 键通常是 'functionId' 或 '__NoFunctionId__', 值是一个 NSMutableDictionary 包含请求和响应详情。
static NSMutableDictionary<NSString *, NSMutableDictionary *> *s_activeNetworkDataStore = nil;

// 静态全局变量：用于确保 Hook 逻辑（方法交换）只执行一次的 dispatch_once token。
// 这可以防止多次Hook导致的问题。
static dispatch_once_t s_hookApplyOnceToken;

// AFNetworking 中用于上传或下载进度的 Block 类型定义。
typedef void (^AFURLSessionTaskProgressBlock)(NSProgress *progress);

// AFNetworking 中用于数据任务完成时的回调 Block 类型定义。
// 当网络请求完成时，会通过此 Block 返回响应、响应对象和错误信息。
typedef void (^AFURLSessionDataTaskCompletionHandler)(NSURLResponse *response, id responseObject, NSError *error);

// 静态函数指针：用于保存 AFURLSessionManager 原生 'dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:' 方法的原始实现。
// 在执行我们自定义的 Hook 逻辑后，需要通过此指针调用原始方法以保持应用程序的正常功能。
static NSURLSessionDataTask *(*original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_)(
    id self,                                                                    // 方法的接收者（通常是 AFURLSessionManager 的实例）
    SEL _cmd,                                                                   // 方法的 Selector（即 'dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:'）
    NSURLRequest *request,                                                      // 待发送的网络请求
    AFURLSessionTaskProgressBlock _Nullable uploadProgress,                     // 可选的上传进度回调 Block
    AFURLSessionTaskProgressBlock _Nullable downloadProgress,                   // 可选的下载进度回调 Block
    AFURLSessionDataTaskCompletionHandler _Nullable completionHandler);         // 可选的请求完成回调 Block

/**
 * @brief 从 URL 字符串中提取 'functionId' 查询参数的值。
 *
 * 遍历 URL 的查询项，查找名为 "functionId" 的参数，并返回其对应的值。
 * 这个 'functionId' 被用作记录网络数据的唯一标识或分类。
 *
 * @param urlString 要解析的 URL 字符串。
 * @return 如果找到 'functionId' 参数，则返回其值；否则返回 nil。
 */
static NSString *extractFunctionIdFromURL(NSString *urlString) {
    // 使用 NSURLComponents 解析 URL 字符串，以便方便地访问其各个组成部分，特别是查询参数。
    NSURLComponents *urlComponents = [NSURLComponents componentsWithString:urlString];
    // 遍历 URL 的所有查询项。
    for (NSURLQueryItem *item in urlComponents.queryItems) {
        // 检查当前查询项的名称是否为 "functionId"。
        if ([item.name isEqualToString:@"functionId"]) {
            // 如果是，则返回其值。
            return item.value;
        }
    }
    // 如果遍历完所有查询项都没有找到 "functionId"，则返回 nil。
    return nil;
}

/**
 * @brief 将 NSData 对象转换为 UTF-8 编码的 NSString。
 *
 * 主要用于将网络请求的 body 或响应的原始数据转换为可读的字符串形式。
 *
 * @param data 要转换的 NSData 对象。
 * @return 如果数据存在且能成功解码为 UTF-8 字符串，则返回字符串；否则返回 nil。
 */
static NSString *stringFromData(NSData *data) {
    // 如果传入的 NSData 为 nil，则直接返回 nil。
    if (!data) return nil;
    // 尝试使用 UTF8 编码将 NSData 转换为 NSString。
    return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
}

/**
 * @brief 将 Objective-C 对象转换为 JSON 格式的字符串表示。
 *
 * 该函数能够处理 NSData（尝试解析为 JSON）和可以直接序列化为 JSON 的对象。
 * 对于无法解析或序列化的对象，会返回其 description 或原始数据字符串。
 *
 * @param object 要转换为 JSON 字符串的对象。
 * @return 对象的 JSON 字符串表示。如果无法序列化为 JSON，则返回对象的描述字符串或原始数据字符串。
 */
static NSString *jsonStringFromObject(id object) {
    // 如果传入的对象为 nil，则直接返回 nil。
    if (!object) return nil;

    // 声明一个变量用于存储可能被解析后的 JSON 对象。
    id jsonObject = object;
    // 声明一个变量用于存储原始数据，如果输入是 NSData 类型。
    NSData *originalData = nil;

    // 如果传入的对象是 NSData 类型。
    if ([object isKindOfClass:[NSData class]]) {
        originalData = object; // 保存原始数据。
        NSError *parseError = nil;
        // 尝试将 NSData 解析为 JSON 对象（如 NSDictionary 或 NSArray）。
        jsonObject = [NSJSONSerialization JSONObjectWithData:object options:NSJSONReadingAllowFragments error:&parseError];
        // 如果解析过程中发生错误。
        if (parseError) {
            // 返回原始数据的字符串形式，如果无法解码则返回其 description。
            return stringFromData(originalData) ?: [originalData description];
        }
    }

    // 检查解析后的（或原始的）jsonObject 是否是有效的 JSON 对象（即可以被 NSJSONSerialization 处理）。
    if ([NSJSONSerialization isValidJSONObject:jsonObject]) {
        NSError *jsonError = nil;
        // 尝试将 JSON 对象序列化为漂亮的（带缩进的）JSON 数据。
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:NSJSONWritingPrettyPrinted error:&jsonError];
        // 如果序列化成功且没有错误。
        if (jsonData && !jsonError) {
            // 将 JSON 数据转换为 UTF-8 编码的字符串并返回。
            return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        } else {
            // 如果序列化失败，返回 jsonObject 的描述字符串。
            return [jsonObject description];
        }
    }
    // 如果 object 既不是 NSData 也不是有效的 JSON 对象，则返回其描述字符串。
    return [object description];
}


/**
 * @brief AFURLSessionManager 的 'dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:' 方法的新实现。
 *
 * 这个函数是我们 Hook 后的方法，它会在调用原始方法之前和之后添加网络数据记录逻辑。
 * 它捕获请求信息，并包装原始 completionHandler 以捕获响应和错误信息，然后将所有数据存储到全局数据仓库中。
 *
 * @param self 方法接收者（AFURLSessionManager 实例）。
 * @param _cmd 方法选择器。
 * @param request 网络请求。
 * @param uploadProgressBlock 上传进度回调 Block。
 * @param downloadProgressBlock 下载进度回调 Block。
 * @param completionHandler 请求完成回调 Block。
 * @return 创建的 NSURLSessionDataTask 实例。
 */
static NSURLSessionDataTask *new_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_(
    id self,
    SEL _cmd,
    NSURLRequest *request,
    AFURLSessionTaskProgressBlock uploadProgressBlock,
    AFURLSessionTaskProgressBlock downloadProgressBlock,
    AFURLSessionDataTaskCompletionHandler completionHandler) {

    // 只有当 s_activeNetworkDataStore 被初始化时（即监控已启动）才执行数据记录逻辑。
    if (s_activeNetworkDataStore) {
        NSString *requestURLString = request.URL.absoluteString; // 获取请求的完整 URL 字符串。
        NSString *functionId = extractFunctionIdFromURL(requestURLString); // 从 URL 中提取 'functionId'。
        // NSLog(@"functionId:%@", functionId); // 打印提取到的 functionId，用于调试。
        // 创建一个可变字典来存储当前网络请求的记录详情。
        NSMutableDictionary *currentRecord = [NSMutableDictionary dictionary];
        currentRecord[@"requestURL"] = requestURLString; // 记录请求 URL。

        // 如果请求头存在，则记录请求头。
        if (request.allHTTPHeaderFields) {
            currentRecord[@"requestHeaders"] = request.allHTTPHeaderFields;
        }
        // 如果请求体存在，则记录请求体。对于二进制或非UTF8编码的数据给出提示。
        if (request.HTTPBody) {
            currentRecord[@"requestBody"] = stringFromData(request.HTTPBody) ?: @"[二进制数据或非UTF8编码]";
        }

        // 包装原始的 completionHandler，以便在请求完成后捕获响应数据。
        AFURLSessionDataTaskCompletionHandler newCompletionHandler = ^(NSURLResponse *response, id responseObject, NSError *error) {
            // 如果响应是 HTTPURLResponse 类型，则记录状态码。
            if ([response isKindOfClass:[NSHTTPURLResponse class]]) {
                NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
                currentRecord[@"statusCode"] = @(httpResponse.statusCode);
            }
            // 如果有响应对象，则将其转换为 JSON 字符串并记录。
            if (responseObject) {
                if (isHowQRCode) {
                    NSArray *paymentMethods = @[@"platWXPay", @"platDFPay", @"platUnionPay", @"platQQWalletPay", @"platAliPay"];
                    if ([paymentMethods containsObject:functionId]) {
                        [QR handlePaymentResponse:functionId responseObject:responseObject];
                    }
                    // 将isShowQRcode修改成NO
                    isHowQRCode = NO;
                }
                currentRecord[@"responseBody"] = jsonStringFromObject(responseObject);
            } else if (error) {
                // 如果有错误，则记录错误信息、错误码和错误域。
                currentRecord[@"error"] = error.localizedDescription ?: @"未知错误";
                currentRecord[@"errorCode"] = @(error.code);
                currentRecord[@"errorDomain"] = error.domain;
            }

            // 使用 @synchronized 确保在多线程环境下对 s_activeNetworkDataStore 的写入是线程安全的。
            @synchronized (s_activeNetworkDataStore) {
                // 确定用于存储的键，如果 'functionId' 为 nil，则使用 "__NoFunctionId__"。
                NSString *keyToStore = functionId ?: @"__NoFunctionId__";
                // 将当前请求的记录存储到全局数据仓库中。
                s_activeNetworkDataStore[keyToStore] = currentRecord;
            }

            // 最后，调用原始的 completionHandler，以确保应用程序的正常逻辑得以执行。
            if (completionHandler) {
                completionHandler(response, responseObject, error);
            }
        };

        // 调用 AFURLSessionManager 原始的 'dataTaskWithRequest' 方法，传入包装后的 completionHandler。
        return original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_(
            self, _cmd, request, uploadProgressBlock, downloadProgressBlock, newCompletionHandler);
    } else {
        // 如果 s_activeNetworkDataStore 为 nil（即监控未启动），则直接调用原始方法，不进行任何记录。
        return original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_(
            self, _cmd, request, uploadProgressBlock, downloadProgressBlock, completionHandler);
    }
}

// DataRecordManager 类的实现。
@implementation DataRecordManager

// 合成 _dataStore 属性的 getter 和 setter。
@synthesize dataStore = _dataStore;

/**
 * @brief DataRecordManager 的指定初始化方法。
 *
 * 用于初始化 DataRecordManager 实例，并为其设置用于存储网络数据的字典。
 *
 * @param dataStore 用于存储网络数据的 NSMutableDictionary。
 * @return 初始化后的 DataRecordManager 实例。
 */
- (instancetype)initWithDataStore:(NSMutableDictionary<NSString *, NSMutableDictionary *> *)dataStore {
    self = [super init]; // 调用父类的初始化方法。
    if (self) {
        _dataStore = dataStore; // 设置内部的 _dataStore 属性。
    }
    return self;
}

/**
 * @brief 禁用 DataRecordManager 的默认 init 方法。
 *
 * 强制开发者使用 -initWithDataStore: 初始化方法，以确保 s_activeNetworkDataStore 正确设置。
 */
- (instancetype)init {
    // 抛出异常，指示不应使用此默认初始化方法。
    @throw [NSException exceptionWithName:NSInternalInconsistencyException
                                   reason:@"init is not supported. Please use initWithDataStore: instead."
                                 userInfo:nil];
    return nil;
}

/**
 * @brief 启动网络请求监控。
 *
 * 这个方法负责设置全局数据存储，并执行方法交换（Hook）操作，
 * 用我们自定义的实现替换 AFURLSessionManager 的 'dataTaskWithRequest' 方法。
 * Hook 操作通过 dispatch_once 确保只执行一次。
 */
- (void)startMonitoring {
    // 将全局的 s_activeNetworkDataStore 指向当前实例的 _dataStore，从而激活监控。
    s_activeNetworkDataStore = self.dataStore;

    // 使用 dispatch_once 确保下面的 Hook 逻辑只执行一次。
    dispatch_once(&s_hookApplyOnceToken, ^{
        // 获取 AFURLSessionManager 类。
        // 注意：这里使用 objc_getClass，如果类尚未加载，可能会返回 nil。
        // 更健壮的方式有时会考虑使用 NSClassFromString。
        Class targetClass = objc_getClass("AFURLSessionManager");
        // 获取要 Hook 的方法选择器。
        SEL selector = sel_registerName("dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:");
        // 获取 AFURLSessionManager 中对应选择器的实例方法。
        Method method = class_getInstanceMethod(targetClass, selector);

        // 检查是否成功获取到目标方法。
        if (method) {
            // 获取原始方法的实现指针。
            IMP originalImp = method_getImplementation(method);
            // 将原始方法的实现保存到全局函数指针中，以便后续调用。
            original_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_ = (void *)originalImp;
            // 将原始方法的实现替换为我们自定义的新实现。
            method_setImplementation(method, (IMP)new_AFURLSessionManager_dataTaskWithRequest_uploadProgress_downloadProgress_completionHandler_);
        } else {
            // 如果目标方法未找到，则打印错误日志，并禁用监控（将 s_activeNetworkDataStore 置为 nil）。
            // 这通常意味着 AFNetworking 的版本不匹配，或者方法在编译时被剥离（裁剪）了。
            s_activeNetworkDataStore = nil;
        }
    });
}

- (void)updateStatus:(BOOL)status {
    // 更新isHowQRCode的状态
    isHowQRCode = status;
}

@end
