#import "QR.h"
#import "QRCode.h"
#import "Dialog.h"
// + (void)show:(NSString *)message duration:(NSTimeInterval)duration;


@implementation QR

// 解析响应对象的辅助方法
+ (NSDictionary *)parseResponseObject:(id)responseObject {
    if ([responseObject isKindOfClass:[NSDictionary class]]) {
        return (NSDictionary *)responseObject;
    }
    
    NSData *jsonData = nil;
    
    if ([responseObject isKindOfClass:[NSString class]]) {
        NSString *jsonString = (NSString *)responseObject;
        jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([responseObject isKindOfClass:[NSData class]]) {
        jsonData = (NSData *)responseObject;
    } else {
        NSLog(@"不支持的响应类型: %@", [responseObject class]);
        return nil;
    }
    
    if (!jsonData) {
        return nil;
    }
    
    NSError *parseError = nil;
    NSDictionary *responseDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&parseError];
    
    if (parseError) {
        NSLog(@"JSON解析失败: %@", parseError.localizedDescription);
        return nil;
    }
    
    if (![responseDict isKindOfClass:[NSDictionary class]]) {
        NSLog(@"解析结果不是字典类型: %@", [responseDict class]);
        return nil;
    }
    
    return responseDict;
}

// 添加支付信息处理方法
+ (void)handlePaymentResponse:(NSString *)functionId responseObject:(id)responseObject {
    @try {
        NSDictionary *responseDict = [self parseResponseObject:responseObject];
        
        if (!responseDict) {
            [Dialog show:@"支付响应解析失败" duration:5.0];
            return;
        }
        
        // 检查响应状态
        NSString *code = responseDict[@"code"];
        if (![code isEqualToString:@"0"]) {
            [Dialog show:[NSString stringWithFormat:@"支付请求失败，错误码: %@", code] duration:5.0];
            return;
        }

        // 判断是否不支持
        // errorCode: "-1"
        if ([responseDict[@"errorCode"] isEqualToString:@"-1"]) {
            [Dialog show:responseDict[@"errorMsg"] duration:5.0];
            return;
        }
        if ([functionId isEqualToString:@"platWXPay"]) {
            [self platWXPay:responseDict];
        } else if ([functionId isEqualToString:@"platDFPay"]) {
            [self platDFPay:responseDict];
        } else if ([functionId isEqualToString:@"platUnionPay"]) {
            [self platUnionPay:responseDict];
        } else if ([functionId isEqualToString:@"platQQWalletPay"]) {
            [self platQQWalletPay:responseDict];
        }
        
    } @catch (NSException *exception) {
        [Dialog show:[NSString stringWithFormat:@"解析支付信息时发生异常: %@", exception.reason] duration:5.0];
        return;
    }
}

// 微信支付
+ (void)platWXPay:(NSDictionary *)responseDict {
    
    NSDictionary *payInfo = responseDict[@"payInfo"];
    if (!payInfo || ![payInfo isKindOfClass:[NSDictionary class]]) {
        [Dialog show:@"payInfo字段不存在或格式错误" duration:5.0];
        return;
    }

    NSMutableString *val = [NSMutableString string];

    [payInfo enumerateKeysAndObjectsUsingBlock:^(id key, id value, BOOL *stop) {
        NSString *stringKey = [NSString stringWithFormat:@"%@", key];
        NSString *stringValue = [NSString stringWithFormat:@"%@", value];
        if ([stringKey isKindOfClass:[NSString class]] && [stringKey isEqualToString:@"package"]) {
            [val appendFormat:@"%@=%@&", key, @"Sign%3DWXPay"];
        } else {
            [val appendFormat:@"%@=%@&", key, stringValue];
        }
    }];
    // 删除最后一个 '&'
    if (val.length > 0) {
        [val deleteCharactersInRange:NSMakeRange(val.length - 1, 1)];
    }

    // 拼接最终的 URL
    NSString *urlString = [NSString stringWithFormat:@"weixin://app/wxe75a2e68877315fb/pay/?%@", val];
    NSURL *url = [NSURL URLWithString:urlString];
    [QRCode show:url.absoluteString title:@"微信支付二维码" message:@"使用苹果自带相机扫码"];
    return;
}

// 微信代付
+ (void)platDFPay:(NSDictionary *)responseDict {
    NSDictionary *shareInfo = responseDict[@"shareInfo"];
    if (!shareInfo || ![shareInfo isKindOfClass:[NSDictionary class]]) {
        [Dialog show:@"shareInfo字段不存在或格式错误" duration:5.0];
        return;
    }
    NSString *url = shareInfo[@"shareUrl"];
    [QRCode show:url title:@"微信代付二维码" message:@"使用微信扫一扫"];
    return;
}

// 云闪付代付
+ (void)platUnionPay:(NSDictionary *)responseDict {
    NSDictionary *payInfo = responseDict[@"payInfo"];
    if (!payInfo || ![payInfo isKindOfClass:[NSDictionary class]]) {
        [Dialog show:@"payInfo字段不存在或格式错误" duration:5.0];
        return;
    }

    NSString *payMessage = payInfo[@"payMessage"];
    if (!payMessage || ![payMessage isKindOfClass:[NSString class]]) {
        [Dialog show:@"payMessage字段不存在或格式错误" duration:5.0];
        return;
    }
    NSString *payload = [NSString stringWithFormat:@"tn=%@,resultURL=exit,scheme=,packageName=,usetestmode=false", payMessage];
    // 2. 将字符串转换成 NSData
    NSData *payloadData = [payload dataUsingEncoding:NSUTF8StringEncoding];
    // 3. Base64 编码
    NSString *base64Payload = [payloadData base64EncodedStringWithOptions:0];
    NSString *url = [NSString stringWithFormat:@"upwrp://uppayservice/?style=token&paydata=%@", base64Payload];
    [QRCode show:url title:@"云闪付代付二维码" message:@"使用百度app或者百度浏览器扫一扫"];
    return;
}

// QQ代付
+ (void)platQQWalletPay:(NSDictionary *)responseDict {
    NSDictionary *payInfo = responseDict[@"payInfo"];
    if (!payInfo || ![payInfo isKindOfClass:[NSDictionary class]]) {
        [Dialog show:@"payInfo字段不存在或格式错误" duration:5.0];
        return;
    }
    NSString *tokenId = payInfo[@"tokenId"];
    if (!tokenId || ![tokenId isKindOfClass:[NSString class]]) {
        [Dialog show:@"tokenId字段不存在或格式错误" duration:5.0];
        return;
    }
    NSString *url = [NSString stringWithFormat:@"https://qpay.qq.com/qr/%@", tokenId];
    [QRCode show:url title:@"QQ代付二维码" message:@"使用QQ扫一扫"];
    return;
}

// 支付宝代付
+ (void)platAliPay:(NSDictionary *)responseDict {
    [Dialog show:@"支付宝代付方法未适配" duration:5.0];
}

@end