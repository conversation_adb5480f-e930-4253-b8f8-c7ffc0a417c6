#include "Controller.h"
#include "Log.h"
#include <objc/runtime.h>
#include <objc/message.h>
#include "StringCryptor_v2.h"

// 加密数据 for "userManager"
static const uint8_t kUSM[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x8C,0x63,0x14,0xC8,0x91,0x9A,0x08,0xE0,0x1E,0xF2,0x30,0x64,0x0A,0x9F,0x60};
// 加密数据 for "sharedUserManager"
static const uint8_t kUSMshared[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x5F,0x86,0xFA,0xC6,0xD2,0xB6,0x41,0x74,0x27,0x69,0x12,0x4D,0x31,0x19,0x7D,0x07,0x67,0x52,0x83,0x20,0xE7};

// 加密数据 for "getUserPin"
static const uint8_t pined[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x3F,0xB8,0x61,0x36,0xAD,0xB9,0x6A,0x9F,0xC9,0x07,0x17,0x7C,0x9A,0x85};
// 加密数据 for "getUserA2String"
static const uint8_t wskeyed[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x3F,0xB8,0x61,0x36,0xAD,0xB9,0x6A,0xE7,0x80,0x6A,0x29,0xC6,0xC1,0x9E,0x75,0x43,0x9C,0x2E,0xD1};


// 导航到地址管理页面
bool jdOAuthManager(const char* controllerClassName) {
    
    // 1. 获取JDOAuthManager类
    Class jdOAuthManagerClass = objc_getClass("JDOAuthManager");
    if (!jdOAuthManagerClass) {
        LOG("JDOAuthManager类不存在");
        return false;
    }
    
    // 2. 直接调用类方法获取导航控制器
    SEL currentNavSel = sel_registerName("currentNavigationController");
    
    // 检查类方法是否存在
    if (!class_getClassMethod(jdOAuthManagerClass, currentNavSel)) {
        LOG("currentNavigationController方法不存在");
        return false;
    }
    // 直接调用类方法 - 修复：使用objc_msgSend直接调用，并正确转换类型
    id (*msgSendClass)(Class, SEL) = (id (*)(Class, SEL))objc_msgSend;
    id navController = msgSendClass(jdOAuthManagerClass, currentNavSel);
    
    if (!navController) {
        LOG("导航控制器为空");
        return false;
    }
    
    // 3. 获取地址列表视图控制器类 - 使用传入的类名
    Class vcClass = objc_getClass(controllerClassName);
    if (!vcClass) {
        LOG("无法获取类: %s", controllerClassName);
        return false;
    }
    
    // 4.1 调用alloc方法
    SEL allocSel = sel_registerName("alloc");
    id (*msgSendAlloc)(Class, SEL) = (id (*)(Class, SEL))objc_msgSend;
    id vcClassAlloc = msgSendAlloc(vcClass, allocSel);
    
    if (!vcClassAlloc) {
        return false;
    }
    // 4.2 调用init方法
    SEL initSel = sel_registerName("init");
    id (*msgSendInit)(id, SEL) = (id (*)(id, SEL))objc_msgSend;
    id viewVC = msgSendInit(vcClassAlloc, initSel);
    
    if (!viewVC) {
        return false;
    }
    
    // 5. 推入导航栈
    SEL pushSel = sel_registerName("pushViewController:animated:");
    
    // 直接调用pushViewController:animated:方法
    void (*msgSendPush)(id, SEL, id, BOOL) = (void (*)(id, SEL, id, BOOL))objc_msgSend;
    msgSendPush(navController, pushSel, viewVC, YES);
    
    LOG("跳转成功");
    return true;
}

static const char* get_utf8_string(id stringObj) {
    if (!stringObj) return NULL;
    return ((const char* (*)(id, SEL))objc_msgSend)(stringObj, sel_registerName("UTF8String"));
}

UserManager getCookie(void) {
    UserManager result = { false, NULL, NULL, NULL };

    char *UserManagerClassName = decrypt_string_v2(kUSM, sizeof(kUSM));
    if (!UserManagerClassName) {
        result.error = copy_string("解密失败");
        return result;
    }
    Class userManagerClass = objc_getClass(UserManagerClassName);
    if (!userManagerClass) {
        result.error = copy_string("获取Cookie类不存在");
        free(UserManagerClassName);
        return result;
    }
        
    char *shareduser = decrypt_string_v2(kUSMshared, sizeof(kUSMshared));
    if (!shareduser) {
        result.error = copy_string("解密失败");
        free(UserManagerClassName);
        return result;
    }
    SEL sharedSel = sel_registerName(shareduser);
    Method classMethod = class_getClassMethod(userManagerClass, sharedSel);
    if (!classMethod) {
        result.error = copy_string("获取Cookie方法不存在");
        free(UserManagerClassName);
        free(shareduser);
        return result;
    }

    id manager = ((id (*)(id, SEL))objc_msgSend)((id)userManagerClass, sharedSel);
    if (!manager) {
        result.error = copy_string("获取Cookie实例化失败");
        free(UserManagerClassName);
        free(shareduser);
        return result;
    }

    char *pineduser = decrypt_string_v2(pined, sizeof(pined));
    if (!pineduser) {
        result.error = copy_string("解密失败");
        free(UserManagerClassName);
        free(shareduser);
        return result;
    }
    SEL pinSel = sel_registerName(pineduser);
    id pinObj = ((id (*)(id, SEL))objc_msgSend)(manager, pinSel);
    if (pinObj) {
        const char* pinStr = get_utf8_string(pinObj);
        if (pinStr) result.pin = copy_string(pinStr);
    }
    char *wskeyeduser = decrypt_string_v2(wskeyed, sizeof(wskeyed));
    if (!wskeyeduser) {
        result.error = copy_string("解密失败");
        free(UserManagerClassName);
        free(shareduser);
        free(pineduser);
        return result;
    }
    SEL wskeySel = sel_registerName(wskeyeduser);
    id wskeyObj = ((id (*)(id, SEL))objc_msgSend)(manager, wskeySel);
    if (wskeyObj) {
        const char* wskeyStr = get_utf8_string(wskeyObj);
        if (wskeyStr) result.wskey = copy_string(wskeyStr);
    }

    if (result.pin && result.wskey) {
        result.status = true;
    } else {
        result.error = copy_string("请确认当前是否处于登陆状态");
        free(result.pin); result.pin = NULL;
        free(result.wskey); result.wskey = NULL;
        result.status = false;
    }
    free(UserManagerClassName);
    free(shareduser);
    free(pineduser);
    free(wskeyeduser);

    return result;
}

void freeCookieResult(UserManager* result) {
    if (!result) return;
    free(result->pin);
    free(result->wskey);
    free(result->error);
    result->pin = NULL;
    result->wskey = NULL;
    result->error = NULL;
}

// bool openURLWithJDBOpenURLModule(const char *urlCString) {
//     // 获取 JDBOpenURLModule 类
//     Class cls = objc_getClass("JDBOpenURLModule");
//     if (!cls) {
//         LOG("[openURLWithJDBOpenURLModule] 类 JDBOpenURLModule 不存在");
//         return false;
//     }

//     // 获取方法选择子
//     SEL sel = sel_registerName("application:openURL:sourceApplication:annotation:");

//     // 获取元类判断类方法是否存在
//     Class metaClass = object_getClass((id)cls);
//     if (!class_respondsToSelector(metaClass, sel)) {
//         LOG("[openURLWithJDBOpenURLModule] 类方法不响应选择子: application:openURL:sourceApplication:annotation:");
//         return false;
//     }

//     // 创建 NSString *urlString
//     Class NSStringCls = objc_getClass("NSString");
//     SEL stringWithUTF8Sel = sel_registerName("stringWithUTF8String:");
//     id nsStr = ((id (*)(Class, SEL, const char *))objc_msgSend)(NSStringCls, stringWithUTF8Sel, urlCString);

//     // 创建 NSURL *url
//     Class NSURLCls = objc_getClass("NSURL");
//     SEL URLWithStringSel = sel_registerName("URLWithString:");
//     id nsUrl = ((id (*)(Class, SEL, id))objc_msgSend)(NSURLCls, URLWithStringSel, nsStr);

//     // 获取 [UIApplication sharedApplication]
//     Class UIApplicationCls = objc_getClass("UIApplication");
//     SEL sharedAppSel = sel_registerName("sharedApplication");
//     id sharedApp = ((id (*)(Class, SEL))objc_msgSend)(UIApplicationCls, sharedAppSel);

//     // 调用类方法 +application:openURL:sourceApplication:annotation:
//     BOOL result = ((BOOL (*)(id, SEL, id, id, id, id))objc_msgSend)((id)cls, sel, sharedApp, nsUrl, nil, nil);

//     LOG("[openURLWithJDBOpenURLModule] 调用成功: %d", result);
//     return result;
// }
