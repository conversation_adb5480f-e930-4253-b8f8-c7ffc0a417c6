#ifndef SMSCODE_H
#define SMSCODE_H


// 循环请求获取验证码 - 简化版
// url: 请求地址
// timeout_seconds: 超时时间（秒）
// interval_seconds: 请求间隔（秒）
// 返回值: 验证码字符串（调用者需要释放），失败返回NULL
char* getVerificationCodeFromURL(const char* url, int timeout_seconds, int interval_seconds);

// 停止当前请求
void cancelVerificationCodeRequest();

// 检查是否有活跃的验证码请求
bool isVerificationCodeRequestActive();

// 提取6位数验证码的辅助函数
char* extractVerificationCode(const char* text);

// 高级封装函数：自动获取验证码并处理结果
// url: 验证码获取URL
// timeout: 超时时间（秒），默认60秒
// interval: 请求间隔（秒），默认2秒
// 返回值: 成功返回true，失败返回false
bool fetchVerificationCodeAsync(const char* url, int timeout, int interval);

// 带回调的验证码获取函数
// url: 验证码获取URL
// timeout: 超时时间（秒）
// interval: 请求间隔（秒）
// callback: 结果回调函数，参数为验证码字符串（成功）或NULL（失败）
// 返回值: 成功返回true，失败返回false
bool fetchVerificationCodeWithCallback(const char* url, int timeout, int interval, void (*callback)(const char* code));


// 自动输入验证码
void accessCaptchaTextField(const char *smsCode);


                                      
#endif