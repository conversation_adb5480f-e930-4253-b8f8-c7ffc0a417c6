#include "Controller.h"
#include <string.h>
#include "Log.h"
#include <objc/runtime.h>
#include <objc/message.h>

// 将文本写入iOS系统剪贴板
void setClipboardTextIOS(const char *text) {
    if (!text) return;

    // NSString *nsStr = [NSString stringWithUTF8String:text];
    id nsStr = ((id (*)(Class, SEL, const char *))objc_msgSend)(
        objc_getClass("NSString"),
        sel_registerName("stringWithUTF8String:"),
        text);

    // UIPasteboard *pb = [UIPasteboard generalPasteboard];
    id pb = ((id (*)(Class, SEL))objc_msgSend)(
        objc_getClass("UIPasteboard"),
        sel_registerName("generalPasteboard"));

    // [pb setString:nsStr];
    SEL setStringSel = sel_registerName("setString:");
    ((void (*)(id, SEL, id))objc_msgSend)(pb, setStringSel, nsStr);

    LOG("✅ Copied to clipboard: %s", text);
}


// 从 iOS 系统剪贴板读取文本
char *getClipboardTextIOS(void) {
    // 获取 UIPasteboard 对象
    id pb = ((id (*)(Class, SEL))objc_msgSend)(
        objc_getClass("UIPasteboard"),
        sel_registerName("generalPasteboard"));

    if (!pb) return NULL;

    // 获取剪贴板上的 NSString*
    SEL stringSel = sel_registerName("string");
    id nsStr = ((id (*)(id, SEL))objc_msgSend)(pb, stringSel);

    if (!nsStr) return NULL;

    // NSString 转 UTF8 C 字符串
    SEL utf8Sel = sel_registerName("UTF8String");
    const char *utf8Str = ((const char *(*)(id, SEL))objc_msgSend)(nsStr, utf8Sel);

    if (!utf8Str) return NULL;

    // 返回一个堆上的副本（调用者记得 free）
    return strdup(utf8Str);
}


InputResult handleInpuText(const char* text, bool fromClipboard) {
    InputResult result = { false, NULL };
    
    id app = ((id (*)(Class, SEL))objc_msgSend)(
        objc_getClass("UIApplication"), 
        sel_registerName("sharedApplication"));
    if (!app) {
        result.error = strdup("无法获取应用程序实例");
        showToastMessage("无法获取应用程序实例", 1);
        return result;
    }
    
    id keyWindow = ((id (*)(id, SEL))objc_msgSend)(
        app, sel_registerName("keyWindow"));
    if (!keyWindow) {
        result.error = strdup("无法获取keyWindow");
        showToastMessage("未检测到键盘", 1);
        return result;
    }
    
    id firstResponder = ((id (*)(id, SEL))objc_msgSend)(
        keyWindow, sel_registerName("firstResponder"));
    if (!firstResponder) {
        result.error = strdup("没有找到输入焦点");
        showToastMessage("请点击输入框", 1);
        return result;
    }

    id nsText = NULL;

    if (fromClipboard) {
        // 从剪贴板获取
        id pb = ((id (*)(Class, SEL))objc_msgSend)(
            objc_getClass("UIPasteboard"),
            sel_registerName("generalPasteboard"));

        nsText = ((id (*)(id, SEL))objc_msgSend)(
            pb, sel_registerName("string"));

        if (!nsText) {
            result.error = strdup("剪贴板没有内容");
            showToastMessage("剪贴板为空", 1);
            return result;
        }

    } else {
        // 从传入的 text 获取
        if (!text) {
            result.error = strdup("输入内容为空");
            showToastMessage("输入内容为空", 1);
            return result;
        }

        nsText = ((id (*)(Class, SEL, const char*))objc_msgSend)(
            objc_getClass("NSString"), 
            sel_registerName("stringWithUTF8String:"), 
            text);
    }

    long length = ((long (*)(id, SEL))objc_msgSend)(
        nsText, sel_registerName("length"));
    
    for (long i = 0; i < length; i++) {
        unsigned short character = ((unsigned short (*)(id, SEL, long))objc_msgSend)(
            nsText, sel_registerName("characterAtIndex:"), i);
        
        id charString = ((id (*)(Class, SEL, const unsigned short*, unsigned long))objc_msgSend)(
            objc_getClass("NSString"), 
            sel_registerName("stringWithCharacters:length:"), 
            &character, 
            1);
        
        ((void (*)(id, SEL, id))objc_msgSend)(
            firstResponder, 
            sel_registerName("insertText:"), 
            charString);
    }
    
    result.status = true;
    return result;
}

void freeInputTextResult(InputResult* result) {
    if (!result) return;
    free(result->error);
    result->error = NULL;
}


/**
const char* textToInput = "要输入的文字";
InputResult result = inpuText(textToInput, false);
InputResult result = inpuText(NULL, true);

if (result.status) {
    NSLOG(@"文字输入成功");
} else {
    NSLOG(@"输入失败: %s", result.error);
}
// 使用完后释放资源
freeInputTextResult(&result);
*/


