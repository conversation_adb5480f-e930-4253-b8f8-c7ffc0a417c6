//
//  ButtonClickHandler.h
//  JDTools
//
//  按钮点击处理模块 - 统一处理所有按钮点击事件
//

#ifndef ButtonClickHandler_h
#define ButtonClickHandler_h

#include <stdio.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 按钮点击参数结构体
 */
typedef struct {
    const char *buttonId;    // 按钮ID
    const char *windowId;    // 窗口ID，为了区分窗口
    const char *serialNumber;// 设备序列号
    const char *keyword;     // 搜索关键词
    const char *wareId;      // 商品ID
    const char *buyCount;    // 购买数量
    // 后续可以继续添加更多参数...
} ButtonClickParams;

/**
 * 按钮点击处理函数
 * @param params 按钮点击参数结构体
 * @return 处理是否成功
 */
bool handleButtonClick(const ButtonClickParams *params);

#ifdef __cplusplus
}
#endif

#endif /* ButtonClickHandler_h */
