# JDPayTweak Plugin Configuration
# This file contains all configuration for the JDPayTweak plugin

# DEBUG 控制变量，默认为 0（不输出日志） 1 输出日志
# DEBUG = 0

JDPayTweak_FILES = \
$(CRYPTOR_FILES) \
$(CJSON_FILES) \
$(HTTPSERVICE_FILES) \
$(REQUESTMANAGER_FILES) \
../../Core/Device/DeviceInfo.c \
../../Core/Window/Toast.m \
../../Core/Window/FloatingView.m \
../../Core/Window/FloatingWindow.m \
ButtonClickHandler.c \
JDPayTweak.m

# Buttons/Network/Network.m \
# Compiler flags for JDPayTweak
JDPayTweak_CFLAGS = -fobjc-arc

JDPayTweak_CFLAGS += \
$(CRYPTOR_CFLAGS) \
$(CJSON_CFLAGS) \
$(HTTPSERVICE_CFLAGS) \
$(REQUESTMANAGER_CFLAGS) \
-I Buttons \
-I Buttons/Base/ \
-I Controller \
-I./Service \
-I../../Core/Device/ \
-I../../Core/Log/ \
-I../../Core/Window/ \
-I../../Tweaks/JDTools/Controller/Hook \
-DDEBUG=$(DEBUG)

JDPayTweak_FRAMEWORKS = IOKit CoreFoundation
JDPayTweak_LDFLAGS += -framework Foundation -framework UIKit -lobjc



RANDOM_SEED := $(shell echo $$RANDOM)
JDPayTweak_CFLAGS += -fobjc-arc \
                    -O3 \
                    -flto \
                    -fvisibility=hidden \
                    -funroll-loops \
                    -fdata-sections \
                    -ffunction-sections \
                    -fomit-frame-pointer \
                    -finline-functions \
                    -fno-stack-protector \
                    -fno-common \
                    -fno-asynchronous-unwind-tables \
                    -fno-exceptions \
                    -ObjC \
                    -DANTI_PTRACE \
                    -DANTI_HOOK \
                    -DANTI_DUMP \
                    -DANTI_DEBUG \
                    -DENABLE_ENCRYPTION \
                    -DRANDOM_SEED=$(RANDOM_SEED)

JDPayTweak_LDFLAGS = \
-Wl,-S \
-Wl,-x \
-Wl,-dead_strip \
-F$(THEOS)/sdks/iPhoneOS14.5.sdk/System/Library/PrivateFrameworks \


JDPayTweak_FRAMEWORKS = UIKit Foundation SystemConfiguration Network CFNetwork CoreFoundation IOKit MobileCoreServices AppSupport
# 在链接时使用 strip 删除符号表和调试信息
JDPayTweak_POST_LINK = \
    strip -S --strip-all --remove-section=.note.gnu.build-id --remove-section=.comment --remove-section=__LLVM,__bitcode $@ && \
    \
    codesign --remove-signature $@ && \
    \
    codesign --force --sign - $@