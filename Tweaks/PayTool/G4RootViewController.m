#import "G4RootViewController.h"
#import <CoreFoundation/CoreFoundation.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include <syslog.h>
#include "Dialog.h"
#include "SocketHTTP.h"
#include "CryptoRequest.h"
#include "Log.h"


#define kMainSwitchStateKey @"G4MainSwitchState"                // 功能状态
#define kFeatureSwitchStatesKey @"G4FeatureSwitchStates"        // 功能选项
#define kSaveOrderCheckboxStateKey @"G4SaveOrderCheckboxState"  // 保存订单号复选框状态
#define kSaveCKCheckboxStateKey @"G4saveCKCheckboxState"        // 保存CK复选框状态
#define kSaveAmountCheckboxStateKey @"G4SaveAmountCheckboxState"  // 保存订单金额复选框状态

@interface G4RootViewController ()
@property (nonatomic, strong) NSArray *featureList;
@property (nonatomic, strong) NSMutableArray *featureSwitches;
@property (nonatomic, assign) BOOL saveOrderCheckboxState;      // 保存订单号复选框状态
@property (nonatomic, assign) BOOL saveCKCheckboxState;         // 复制CK复选框状态
@property (nonatomic, assign) BOOL saveAmountCheckboxState;      // 保存订单金额复选框状态
@property (nonatomic, strong) NSDate *lastRefreshTime;          // 记录上次点击刷新按钮的时间
@end


@implementation G4RootViewController

// 定义一个圆角的常量
static CGFloat const cornerRadius = 10;
// 是否授权: 控制功能状态
static BOOL hasAuthorized = NO;

// 插件id
// static NSString *const appId = @"com.g4.paytool";
// 插件版本号
// static NSString *const appVersion = @"1.0.3-1";
// 服务中心名称
// static NSString *const serviceName = @"com.g4.core.services.springboard";

NSArray *kFeatureList(void) {
    return @[
        @{@"title": @"云闪付代付", @"id": @"platUnionPay", @"icon": @"unionpay", @"desc": @"支持云闪付代付功能"},
        @{@"title": @"QQ代付", @"id": @"platQQWalletPay", @"icon": @"qqpay", @"desc": @"支持QQ钱包代付"},
        @{@"title": @"支付宝", @"id": @"platAliPay", @"icon": @"alipay", @"desc": @"使用支付宝扫一扫"},
        @{@"title": @"微信代付", @"id": @"platDFPay", @"icon": @"wechat", @"desc": @"支持微信好友代付"},
        @{@"title": @"微信支付", @"id": @"platWXPay", @"icon": @"wechat", @"desc": @"类似本机安装的微信直接付款"},
        @{@"title": @"复制CK", @"id": @"copyCookie", @"icon": @"copy", @"desc": @"复制CK到剪贴板"}
    ];
}

// 显示对话框并在点击后退出程序
- (void)showExitDialog:(NSString *)message {
    UIAlertController *alert = [UIAlertController 
                               alertControllerWithTitle:@"提示" 
                               message:message 
                               preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction *okAction = [UIAlertAction 
                              actionWithTitle:@"确定" 
                              style:UIAlertActionStyleDefault 
                              handler:^(UIAlertAction * _Nonnull action) {
                                  // 点击确定按钮后退出程序
                                  exit(0);
                              }];
    
    [alert addAction:okAction];
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    // 设置整体背景为很浅的灰色
    self.view.backgroundColor = [UIColor colorWithWhite:0.96 alpha:1.0];
    self.navigationController.navigationBarHidden = NO;

    self.title = @"京东代付助手";
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"退出" style:UIBarButtonItemStylePlain target:self action:@selector(exitButtonTapped:)];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"激活" style:UIBarButtonItemStylePlain target:self action:@selector(activateButtonTapped:)];

    CGFloat margin = 10; // 模块上下之间的间距
    CGFloat moduleWidth = self.view.bounds.size.width - margin * 2;
    // 获取状态栏高度 - iOS 13+ 专用
    CGFloat statusBarHeight = 0;
    UIWindowScene *windowScene = (UIWindowScene *)UIApplication.sharedApplication.connectedScenes.anyObject;
    if (windowScene && windowScene.statusBarManager) {
        statusBarHeight = windowScene.statusBarManager.statusBarFrame.size.height;
    } else {
        // 如果无法获取，使用默认值
        statusBarHeight = 44.0; // iPhone 默认状态栏高度
    }

    CGFloat topOffset = self.navigationController.navigationBar.frame.size.height + statusBarHeight;
    CGFloat y = topOffset + margin;

    UIView *descModule = [[UIView alloc] initWithFrame:CGRectMake(margin, y, moduleWidth, 50)];
    descModule.backgroundColor = [UIColor whiteColor];
    descModule.layer.cornerRadius = cornerRadius;
    descModule.layer.masksToBounds = YES;

    // UILabel *descriptionLabel = [[UILabel alloc] initWithFrame:CGRectMake(5, 5, moduleWidth - 20, 40)];
    // descriptionLabel.text = @"本工具将在京东APP启动时自动注入代付功能，无需手动操作。";
    // descriptionLabel.text = @"京东APP启动时会自动加载本工具，无需手动操作。";
    // descriptionLabel.text = @"开启功能之后要重启京东才会生效";
    // descriptionLabel.textAlignment = NSTextAlignmentCenter;
    // descriptionLabel.numberOfLines = 0;
    // descriptionLabel.font = [UIFont systemFontOfSize:13];
    // // descriptionLabel.textColor = [UIColor darkGrayColor];
    // descriptionLabel.textColor = [UIColor grayColor];
    // [descModule addSubview:descriptionLabel];

    // [self.view addSubview:descModule];

    // ======= 功能开关 =======
    // y += descModule.frame.size.height + margin;
    y += margin;

    UIView *switchModule = [[UIView alloc] initWithFrame:CGRectMake(margin, y, moduleWidth, 60)];
    switchModule.backgroundColor = [UIColor whiteColor];
    switchModule.layer.cornerRadius = cornerRadius;
    switchModule.layer.masksToBounds = YES;
    
    // icon
    UIImageView *mainSwitchIcon = [[UIImageView alloc] initWithFrame:CGRectMake(18, 13, 30, 30)];
    mainSwitchIcon.image = [UIImage imageNamed:@"main"];
    [switchModule addSubview:mainSwitchIcon];
    
    // 调整label位置
    self.switchLabel = [[UILabel alloc] initWithFrame:CGRectMake(62, 0, 100, 60)];
    self.switchLabel.text = @"总开关";
    self.switchLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    [switchModule addSubview:self.switchLabel];
    
    // 保持switch位置与下方一致
    self.controlSwitch = [[UISwitch alloc] initWithFrame:CGRectMake(moduleWidth - 70, 15, 51, 31)];
    [self.controlSwitch addTarget:self action:@selector(toggleSwitch:) forControlEvents:UIControlEventValueChanged];
    // self.controlSwitch.enabled = NO; // 初始禁用
    [switchModule addSubview:self.controlSwitch];
    
    [self.view addSubview:switchModule];

    // ======= 功能选择模块 =======
    y += switchModule.frame.size.height + margin;

    self.featureList = kFeatureList();
    self.featureSwitches = [NSMutableArray array];
    UIView *featureModule = [[UIView alloc] initWithFrame:CGRectMake(margin, y, moduleWidth, self.featureList.count * 50)];
    featureModule.backgroundColor = [UIColor whiteColor];
    featureModule.layer.cornerRadius = cornerRadius;
    featureModule.layer.masksToBounds = YES;

    // ======= 功能模块 =======

    for (NSInteger i = 0; i < self.featureList.count; i++) {
        NSDictionary *item = self.featureList[i];
        CGFloat rowHeight = 50; // 修改为50
        UIView *row = [[UIView alloc] initWithFrame:CGRectMake(0, i * rowHeight, moduleWidth, rowHeight)];
        row.backgroundColor = [UIColor clearColor];
    
        // icon
        UIImageView *iconView = [[UIImageView alloc] initWithFrame:CGRectMake(18, 10, 30, 30)];
        iconView.image = [UIImage imageNamed:item[@"icon"]];
        [row addSubview:iconView];
    
        // title
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(62, 0, 150, 22)];
        label.text = item[@"title"];
        label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        label.textColor = [UIColor blackColor];
        [row addSubview:label];
    
        // 描述
        UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectMake(62, 22, 180, 15)];
        descLabel.text = item[@"desc"];
        descLabel.font = [UIFont systemFontOfSize:11];
        descLabel.textColor = [UIColor grayColor];
        descLabel.numberOfLines = 1;
        [row addSubview:descLabel];
    
        // switch
        UISwitch *featureSwitch = [[UISwitch alloc] initWithFrame:CGRectMake(moduleWidth - 70, (rowHeight-31)/2, 51, 31)];
        featureSwitch.tag = 3000 + i;
        [featureSwitch addTarget:self action:@selector(featureSwitchChanged:) forControlEvents:UIControlEventValueChanged];
        featureSwitch.enabled = NO; // 初始禁用
        [self.featureSwitches addObject:featureSwitch];
        [row addSubview:featureSwitch];
    
        [featureModule addSubview:row];
    
        // 分割线
        if (i < self.featureList.count - 1) {
            UIView *line = [[UIView alloc] initWithFrame:CGRectMake(16, rowHeight-1, moduleWidth-32, 1)];
            line.backgroundColor = [UIColor colorWithWhite:0.93 alpha:1.0];
            [row addSubview:line];
        }
    }
    [self.view addSubview:featureModule];
    y += featureModule.frame.size.height + margin;

    // ======= 其他功能复选框模块 =======
    UIView *checkboxModule = [[UIView alloc] initWithFrame:CGRectMake(margin, y, moduleWidth, 80)];
    checkboxModule.backgroundColor = [UIColor whiteColor];
    checkboxModule.layer.cornerRadius = cornerRadius;
    checkboxModule.layer.masksToBounds = YES;
    
    // // 标题
    // UILabel *checkboxTitle = [[UILabel alloc] initWithFrame:CGRectMake(18, 10, moduleWidth - 36, 20)];
    // checkboxTitle.text = @"其他功能设置";
    // checkboxTitle.font = [UIFont systemFontOfSize:10 weight:UIFontWeightMedium];
    // checkboxTitle.textColor = [UIColor grayColor];
    // [checkboxModule addSubview:checkboxTitle];
    
    // // 计算每个复选框的宽度（每行3个）
    // CGFloat checkboxWidth = (moduleWidth - 36) / 3;
    // CGFloat checkboxY = 40;
    
    // // 保存订单号复选框
    // UIButton *saveOrderCheckbox = [UIButton buttonWithType:UIButtonTypeCustom];
    // saveOrderCheckbox.frame = CGRectMake(18, checkboxY, 20, 20);
    // saveOrderCheckbox.layer.borderWidth = 1.0;
    // saveOrderCheckbox.layer.borderColor = [UIColor lightGrayColor].CGColor;
    // saveOrderCheckbox.layer.cornerRadius = 3.0;
    // saveOrderCheckbox.tag = 4001;
    // [saveOrderCheckbox addTarget:self action:@selector(checkboxTapped:) forControlEvents:UIControlEventTouchUpInside];
    // [checkboxModule addSubview:saveOrderCheckbox];
    
    // UILabel *saveOrderLabel = [[UILabel alloc] initWithFrame:CGRectMake(18 + 25, checkboxY, checkboxWidth - 30, 20)];
    // saveOrderLabel.text = @"保存订单号";
    // saveOrderLabel.font = [UIFont systemFontOfSize:13];
    // [checkboxModule addSubview:saveOrderLabel];
    

    // // 保存订单金额复选框
    // UIButton *saveAmountCheckbox = [UIButton buttonWithType:UIButtonTypeCustom];
    // // saveAmountCheckbox.frame = CGRectMake(18 + checkboxWidth * 2, checkboxY, 20, 20);
    // saveAmountCheckbox.frame = CGRectMake(18 + checkboxWidth, checkboxY, 20, 20);
    // saveAmountCheckbox.layer.borderWidth = 1.0;
    // saveAmountCheckbox.layer.borderColor = [UIColor lightGrayColor].CGColor;
    // saveAmountCheckbox.layer.cornerRadius = 3.0;
    // saveAmountCheckbox.tag = 4003;
    // [saveAmountCheckbox addTarget:self action:@selector(checkboxTapped:) forControlEvents:UIControlEventTouchUpInside];
    // [checkboxModule addSubview:saveAmountCheckbox];
    // // UILabel *saveAmountLabel = [[UILabel alloc] initWithFrame:CGRectMake(18 + checkboxWidth * 2 + 25, checkboxY, checkboxWidth - 30, 20)];
    // UILabel *saveAmountLabel = [[UILabel alloc] initWithFrame:CGRectMake(18 + checkboxWidth + 25, checkboxY, checkboxWidth, 20)];
    // saveAmountLabel.text = @"保存订单金额";
    // saveAmountLabel.font = [UIFont systemFontOfSize:13];
    // [checkboxModule addSubview:saveAmountLabel];

    // // 保存CK复选框
    // UIButton *copyCKCheckbox = [UIButton buttonWithType:UIButtonTypeCustom];
    // copyCKCheckbox.frame = CGRectMake(18 + checkboxWidth * 2 + 15, checkboxY, 20, 20);
    // copyCKCheckbox.layer.borderWidth = 1.0;
    // copyCKCheckbox.layer.borderColor = [UIColor lightGrayColor].CGColor;
    // copyCKCheckbox.layer.cornerRadius = 3.0;
    // copyCKCheckbox.tag = 4002;
    // [copyCKCheckbox addTarget:self action:@selector(checkboxTapped:) forControlEvents:UIControlEventTouchUpInside];
    // [checkboxModule addSubview:copyCKCheckbox];
    // // UILabel *copyCKLabel = [[UILabel alloc] initWithFrame:CGRectMake(18 + checkboxWidth * 2 + 25, checkboxY, checkboxWidth - 30, 20)];
    // UILabel *copyCKLabel = [[UILabel alloc] initWithFrame:CGRectMake(18 + checkboxWidth * 2 + 40, checkboxY, checkboxWidth - 30, 20)]; // 调整标签位置

    // copyCKLabel.text = @"保存CK";
    // copyCKLabel.font = [UIFont systemFontOfSize:13];
    // [checkboxModule addSubview:copyCKLabel];
    
    
    // [self.view addSubview:checkboxModule];
    // y += checkboxModule.frame.size.height + margin;

    // // 添加保存路径描述模块
    
    // UIView *pathModule = [[UIView alloc] initWithFrame:CGRectMake(margin, y, moduleWidth, 50)];
    // pathModule.backgroundColor = [UIColor whiteColor];
    // pathModule.layer.cornerRadius = cornerRadius;
    // pathModule.layer.masksToBounds = YES;
    
    // UILabel *pathLabel = [[UILabel alloc] initWithFrame:CGRectMake(5, 5, moduleWidth - 20, 40)];
    // pathLabel.text = @"数据保存路径：/var/tmp/订单记录.txt";
    // // pathLabel.text = @"文件系统(越狱)：/var/tmp/订单记录.txt";
    // pathLabel.textAlignment = NSTextAlignmentCenter;
    // pathLabel.numberOfLines = 0;
    // pathLabel.font = [UIFont systemFontOfSize:13];
    // pathLabel.textColor = [UIColor grayColor];
    // [pathModule addSubview:pathLabel];
    
    // [self.view addSubview:pathModule];
    
    // y += pathModule.frame.size.height + margin;

    // ======= 到期时间显示 =======
    UIView *expiryContainer = [[UIView alloc] initWithFrame:CGRectMake(margin, y, moduleWidth, 20)];
    [self.view addSubview:expiryContainer];
    
    // 创建到期时间标签
    UILabel *expiryLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, moduleWidth, 20)];
    expiryLabel.text = @"到期时间: 1970-01-01 00:00:00";
    expiryLabel.textAlignment = NSTextAlignmentCenter;
    expiryLabel.font = [UIFont systemFontOfSize:12];
    expiryLabel.textColor = [UIColor redColor];
    expiryLabel.tag = 1000;
    [expiryContainer addSubview:expiryLabel];
    
    // 新增刷新按钮
    UIButton *refreshButton = [UIButton buttonWithType:UIButtonTypeCustom];
    // 计算按钮位置：标签宽度的一半 + 文本宽度的一半 + 一点间距
    CGSize textSize = [expiryLabel.text sizeWithAttributes:@{NSFontAttributeName: expiryLabel.font}];
    CGFloat buttonX = (moduleWidth / 2) + (textSize.width / 2) + 5;
    refreshButton.frame = CGRectMake(buttonX, 0, 20, 20); // 修改为16x16的正方形，并调整垂直位置
    refreshButton.tintColor = [UIColor systemBlueColor];
    [refreshButton addTarget:self action:@selector(refreshAuthStatus:) forControlEvents:UIControlEventTouchUpInside];
    
    // 替换 systemImageNamed 方法，使用自定义图标或条件判断
    if (@available(iOS 13.0, *)) {
        // iOS 13 及以上使用系统图标
        [refreshButton setImage:[UIImage systemImageNamed:@"arrow.clockwise"] forState:UIControlStateNormal];
    } else {
        // iOS 12 及以下使用自定义图标或文本
        [refreshButton setTitle:@"↻" forState:UIControlStateNormal]; // 使用Unicode刷新符号
        refreshButton.titleLabel.font = [UIFont systemFontOfSize:16];
    }
    
    [expiryContainer addSubview:refreshButton];
    
    y += expiryContainer.frame.size.height + margin;
    
    
    // ======= 联系作者 =======
    CGFloat bottomY;
    // 获取主窗口 - iOS 13+ 专用
    UIWindow *window = nil;
    // iOS 13+ 使用 windowScene
    for (UIWindowScene *windowScene in UIApplication.sharedApplication.connectedScenes) {
        if (windowScene.activationState == UISceneActivationStateForegroundActive) {
            for (UIWindow *sceneWindow in windowScene.windows) {
                if (sceneWindow.isKeyWindow) {
                    window = sceneWindow;
                    break;
                }
            }
            if (window) break;
        }
    }
    // 如果没找到，使用第一个可用窗口
    if (!window) {
        UIWindowScene *windowScene = (UIWindowScene *)UIApplication.sharedApplication.connectedScenes.anyObject;
        if (windowScene && windowScene.windows.count > 0) {
            window = windowScene.windows.firstObject;
        }
    }

    // 检查是否有底部安全区域（刘海屏/全面屏设备）
    if (window && window.safeAreaInsets.bottom > 0) {
        bottomY = 40; // iPhone X 及以上机型
    } else {
        bottomY = 25; // 较低型号设备
    }
    CGFloat bottomInfoY = self.view.bounds.size.height - bottomY;
    CGFloat labelWidth = self.view.bounds.size.width;
    CGFloat labelHeight = 20;

    UILabel *versionLabelBottom = [[UILabel alloc] initWithFrame:CGRectMake(0, bottomInfoY, labelWidth, labelHeight)];
    versionLabelBottom.text = @"联系作者";
    versionLabelBottom.textAlignment = NSTextAlignmentCenter;
    versionLabelBottom.font = [UIFont systemFontOfSize:12];
    versionLabelBottom.textColor = [UIColor systemBlueColor]; // 设置为系统蓝色
    versionLabelBottom.userInteractionEnabled = YES; // 开启用户交互
    [self.view addSubview:versionLabelBottom];
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(contactButtonTapped)];
    [versionLabelBottom addGestureRecognizer:tapGesture];

    // 加载历史配置文件
    [self loadFeatureSwitchStates];

    // 延迟执行权限认证
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self performAuthCheck];
    });
}

// 执行权限认证
- (void)performAuthCheck {
    char* response = NULL;
    const char* headers = "Content-Type: application/json";
    int statusCode = 0;
    bool success = socket_post_request_sync("http://127.0.0.1:33672/service", "{}", headers, &response, &statusCode);
    if (!success || statusCode != 200) {
        hasAuthorized = false;
        return;
    }
    LOG("HTTP状态码: %d", statusCode);
    LOG("响应内容: %s", response ? response : "空响应");


    hasAuthorized = true;
    // @try {
    //     // 查询授权
    //     // MRYIPCCenter* center = [MRYIPCCenter centerNamed:@"com.g4.core.services.springboard"];
    //     static const uint8_t springboardKeyName[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0xDF,0x86,0xDA,0xEA,0xC0,0x35,0x0C,0xF0,0xDF,0x66,0x51,0xE7,0x13,0x3B,0xD7,0x85,0xB7,0x72,0xD9,0xFF,0x5C,0xDB,0x28,0x52,0x96,0x34,0x43,0x15,0x75,0xA9,0x26,0x07,0xED,0xC4,0xD8,0xBC};
    //     const char *serviceName = DECRYPTED_STRING_AUTO(springboardKeyName);
    //     MRYIPCCenter *center = [MRYIPCCenter centerNamed:[NSString stringWithUTF8String:serviceName]];

    //     NSDictionary* response = [center callExternalMethod:@selector(handleQueryStatus:) withArguments:@{@"appId": appId, @"version": appVersion}];
    //     if (!response) {
    //         hasAuthorized = NO;
    //         [self showExitDialog:@"查询授权状态异常"];
    //         return;
    //     }

    //     // 判断查询结果是否正常
    //     if (![response[@"status"] boolValue]) {
    //         hasAuthorized = NO;
    //         NSString *error = response[@"error"];
    //         if (error) {
    //             [self showExitDialog:[NSString stringWithFormat:@"%@", error]];
    //         } else {
    //             [self showExitDialog:@"查询授权状态异常"];
    //         }
    //         return;
    //     }
    //     // 正常情况的数据是加密的。
    //     NSString *encryptedData = response[@"data"];
    //     if (!encryptedData) {
    //         hasAuthorized = NO;
    //         [self showExitDialog:@"查询授权状态异常"];
    //         return;
    //     }

    //     QueryResult result = parse_query_result([encryptedData UTF8String]);
    //     if (!result.success) {
    //         hasAuthorized = NO;
    //         [self showExitDialog:[NSString stringWithFormat:@"%s", result.error ? result.error : "null"]];
    //         free_parse_query_result(result);
    //         return;
    //     }
    //     // 修改授权状态
    //     hasAuthorized = result.status;
         
    //     [self printAllSwitchStates];

    //     NSString *expiryText = @"到期时间";
    //     NSTimeInterval timeInterval = result.time ? atof(result.time) : 0;
    //     NSDate *expiryDate = [NSDate dateWithTimeIntervalSince1970:timeInterval];

    //     NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    //     [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    //     NSString *formattedDate = [dateFormatter stringFromDate:expiryDate];

    //     NSString *expiryDateText = [NSString stringWithFormat:@"%@:%@", expiryText, formattedDate];


    //     // 在所有使用完result后释放资源
    //     free_parse_query_result(result);

    //     // 添加到期时间
    //     UILabel *expiryLabel = [self.view viewWithTag:1000];
    //     if (expiryLabel) {
    //         expiryLabel.text = expiryDateText;
    //     }
    //     expiryLabel.textColor = hasAuthorized ? [UIColor grayColor] : [UIColor redColor];

    //     if (hasAuthorized) {
    //         // ✅ 授权通过后，主开关可以操作
    //         // self.controlSwitch.on = YES;
    //         // self.controlSwitch.enabled = YES;
    //         // ✅ 根据主开关是否开启，来决定子开关是否可以点
    //         BOOL mainSwitchIsOn = self.controlSwitch.isOn;
    //         for (UISwitch *sw in self.featureSwitches) {
    //             sw.enabled = mainSwitchIsOn;
    //         }
    //     } else {
    //         // 如果当前控制按钮是开启状态，那么需要关闭
    //         if (self.controlSwitch.isOn) {
    //             self.controlSwitch.on = NO;
    //         }
    //         // 修改功能按钮状态
    //         for (UISwitch *sw in self.featureSwitches) {
    //             sw.enabled = NO;
    //         }
    //     }
    //     return;
    // } @catch (NSException *exception) {
    //     [self showExitDialog:@"查询设备授权状态时发生异常"];
    // }
}

// 主开关切换
- (void)toggleSwitch:(UISwitch *)sender {
    if (!hasAuthorized) {
        sender.on = !sender.on; // 恢复开关状态
        // 如果未激活直接出现激活弹窗
        [self activateButtonTapped:nil];
        return;
    }
    // 开启功能提示
    if (sender.isOn) {
        // [Toast show:@"开启之后需要重启京东才生效" duration:2.0];
    }
    
    // 动态控制下方功能按钮可用性
    for (UISwitch *sw in self.featureSwitches) {
        sw.enabled = sender.isOn;
    }

    // 保存功能开关状态
    [self saveFeatureSwitchStates];

    // 打印所有按钮的状态
    [self printAllSwitchStates];
    
}

// 功能开关切换
- (void)featureSwitchChanged:(UISwitch *)sender {
    if (!hasAuthorized) {
        sender.on = !sender.on; // 恢复开关状态
        return;
    }
    
    [self saveFeatureSwitchStates]; // ✅ 每次变化后立即保存

    // 打印所有按钮的状态
    [self printAllSwitchStates];
}

// 退出按钮事件
- (void)exitButtonTapped:(id)sender {
    exit(0);
}

// 复选框点击事件
- (void)checkboxTapped:(UIButton *)sender {
    // 切换选中状态
    sender.selected = !sender.selected;
    
    // 更新复选框外观
    [self updateCheckboxAppearance:sender];

    // 根据标签处理不同的复选框
    switch (sender.tag) {
        case 4001: // 保存订单号
            self.saveOrderCheckboxState = sender.selected; // 同步状态到属性
            break;
        case 4002: // 保存ck
            self.saveCKCheckboxState = sender.selected; // 同步状态到属性
            break;
        case 4003: // 保存金额
            self.saveAmountCheckboxState = sender.selected; // 同步状态到属性
            break;
        
        default:
            break;
    }
    // 保存复选框状态
    [self saveCheckboxStates];

    // 打印所有按钮的状态
    [self printAllSwitchStates];
}

- (void)saveFeatureSwitchStates {
    NSMutableDictionary *states = [NSMutableDictionary dictionary];
    for (UISwitch *sw in self.featureSwitches) {
        NSString *key = [NSString stringWithFormat:@"%ld", (long)sw.tag];
        [states setObject:@(sw.isOn) forKey:key];
    }
    [[NSUserDefaults standardUserDefaults] setObject:states forKey:kFeatureSwitchStatesKey];

    // 保存主开关状态
    [[NSUserDefaults standardUserDefaults] setBool:self.controlSwitch.isOn forKey:kMainSwitchStateKey];

    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)loadFeatureSwitchStates {
    NSDictionary *savedStates = [[NSUserDefaults standardUserDefaults] objectForKey:kFeatureSwitchStatesKey];
    if ([savedStates isKindOfClass:[NSDictionary class]]) {
        for (UISwitch *sw in self.featureSwitches) {
            NSString *key = [NSString stringWithFormat:@"%ld", (long)sw.tag];
            NSNumber *state = savedStates[key];
            if ([state isKindOfClass:[NSNumber class]]) {
                sw.on = [state boolValue];
            }
        }
    }
    
    // 加载主开关状态
    BOOL mainSwitchState = [[NSUserDefaults standardUserDefaults] boolForKey:kMainSwitchStateKey];
    self.controlSwitch.on = mainSwitchState;

    // 加载复选框状态
    self.saveOrderCheckboxState = [[NSUserDefaults standardUserDefaults] boolForKey:kSaveOrderCheckboxStateKey];
    self.saveCKCheckboxState = [[NSUserDefaults standardUserDefaults] boolForKey:kSaveCKCheckboxStateKey];
    self.saveAmountCheckboxState = [[NSUserDefaults standardUserDefaults] boolForKey:kSaveAmountCheckboxStateKey];
    // self.saveAmountCheckboxState = [[NSUserDefaults standardUserDefaults] boolForKey:kSaveAmountCheckboxStateKey];

    // 更新复选框UI
    UIButton *saveOrderCheckbox = [self.view viewWithTag:4001];
    UIButton *copyCKCheckbox = [self.view viewWithTag:4002];
    UIButton *saveAmountCheckbox = [self.view viewWithTag:4003];
    if (saveOrderCheckbox) {
        saveOrderCheckbox.selected = self.saveOrderCheckboxState;
        [self updateCheckboxAppearance:saveOrderCheckbox];
    }
    if (copyCKCheckbox) {
        copyCKCheckbox.selected = self.saveCKCheckboxState;
        [self updateCheckboxAppearance:copyCKCheckbox];
    }
    if (saveAmountCheckbox) {
        saveAmountCheckbox.selected = self.saveAmountCheckboxState;
        [self updateCheckboxAppearance:saveAmountCheckbox];
    }

    // 打印所有按钮的状态
    [self printAllSwitchStates];
}

// 保存复选框状态的方法
- (void)saveCheckboxStates {
    [[NSUserDefaults standardUserDefaults] setBool:self.saveOrderCheckboxState forKey:kSaveOrderCheckboxStateKey];
    [[NSUserDefaults standardUserDefaults] setBool:self.saveCKCheckboxState forKey:kSaveCKCheckboxStateKey];
    [[NSUserDefaults standardUserDefaults] setBool:self.saveAmountCheckboxState forKey:kSaveAmountCheckboxStateKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

// 新增一个辅助方法，用于根据复选框的选中状态更新其外观
- (void)updateCheckboxAppearance:(UIButton *)checkbox {
    if (checkbox.selected) {
        checkbox.backgroundColor = [UIColor systemBlueColor];
        checkbox.tintColor = [UIColor whiteColor];
        if (@available(iOS 13.0, *)) {
            [checkbox setImage:[UIImage systemImageNamed:@"checkmark"] forState:UIControlStateNormal];
        } else {
            [checkbox setTitle:@"✓" forState:UIControlStateNormal];
            checkbox.titleLabel.font = [UIFont systemFontOfSize:14];
        }
    } else {
        checkbox.backgroundColor = [UIColor whiteColor];
        [checkbox setImage:nil forState:UIControlStateNormal];
        [checkbox setTitle:@"" forState:UIControlStateNormal];
    }
}

- (void)printAllSwitchStates {
    
    // // 创建一个数组来存储功能开关的状态
    // NSMutableArray *featureStates = [NSMutableArray array];
    
    // // 遍历功能开关并添加状态到数组
    // for (UISwitch *sw in self.featureSwitches) {
    //     NSInteger index = sw.tag - 3000;
    //     NSDictionary *feature = self.featureList[index];
    //     NSString *featureId = feature[@"id"];
    //     BOOL featureState = sw.isOn;
    //     [featureStates addObject:@{@"id": featureId, @"state": @(featureState)}];
    // }
    
    // // 创建 settings 字典并添加功能开关状态数组
    // NSMutableDictionary *settings = [@{
    //     @"hasAuthorized": @(hasAuthorized),
    //     @"control": @(self.controlSwitch.isOn),
    //     @"saveOrderId": @(self.saveOrderCheckboxState),
    //     @"saveCookie": @(self.saveCKCheckboxState),
    //     @"saveAmount": @(self.saveAmountCheckboxState),
    //     @"features": featureStates
    // } mutableCopy];

    // // MRYIPCCenter* center = [MRYIPCCenter centerNamed:@"com.g4.rocket.paytool.springboard"];
    // static const uint8_t serviceNameKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0xDF,0x86,0xDA,0xEA,0xC0,0x35,0x0C,0x68,0xD6,0xEE,0x69,0x2A,0x67,0x96,0xED,0x2E,0x3D,0xC2,0x82,0xDA,0x6C,0x72,0x4A,0xA4,0xD1,0x08,0x88,0x01,0x9C,0x77,0x83,0x7D,0x60,0x8B,0xCA,0xB0,0x40};
    // const char *serviceName = DECRYPTED_STRING_AUTO(serviceNameKey);
    // MRYIPCCenter *center = [MRYIPCCenter centerNamed:[NSString stringWithUTF8String:serviceName]];
    // // NSLog(@"同步配置未启用%@, %@", center, settings);
    // NSDictionary* response = [center callExternalMethod:@selector(createFLoatingWindow:) withArguments:settings];

    // if (!response) {
    //     [Dialog show:@"配置同步失败，请重试！" duration:2.0];
    //     return;
    // }
}

// ======= 联系方式 =======

- (void)contactButtonTapped {
    // 自定义内容视图
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 240, 280)];
    // contentView.backgroundColor = [UIColor whiteColor];
    contentView.layer.cornerRadius = 16;
    contentView.layer.masksToBounds = YES;
    contentView.layer.shadowColor = [UIColor blackColor].CGColor;
    contentView.layer.shadowOpacity = 0.08;
    contentView.layer.shadowOffset = CGSizeMake(0, 4);
    contentView.layer.shadowRadius = 12;

    // 生成二维码图片
    NSString *qrString = @"https://qm.qq.com/q/RABFJMAOaa";
    NSData *stringData = [qrString dataUsingEncoding:NSUTF8StringEncoding];
    CIFilter *filter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    [filter setValue:stringData forKey:@"inputMessage"];
    [filter setValue:@"M" forKey:@"inputCorrectionLevel"];
    CIImage *ciImage = filter.outputImage;

    // 放大二维码
    CGFloat qrSize = 160; // 缩小二维码尺寸
    CGRect extent = CGRectIntegral(ciImage.extent);
    CGFloat scale = MIN(qrSize/CGRectGetWidth(extent), qrSize/CGRectGetHeight(extent));
    size_t width = CGRectGetWidth(extent) * scale;
    size_t height = CGRectGetHeight(extent) * scale;
    CGColorSpaceRef cs = CGColorSpaceCreateDeviceGray();
    CGContextRef bitmapRef = CGBitmapContextCreate(nil, width, height, 8, 0, cs, (CGBitmapInfo)kCGImageAlphaNone);
    CIContext *context = [CIContext contextWithOptions:nil];
    CGImageRef bitmapImage = [context createCGImage:ciImage fromRect:extent];
    CGContextSetInterpolationQuality(bitmapRef, kCGInterpolationNone);
    CGContextScaleCTM(bitmapRef, scale, scale);
    CGContextDrawImage(bitmapRef, extent, bitmapImage);
    CGImageRef scaledImage = CGBitmapContextCreateImage(bitmapRef);

    UIImage *qrImage = [UIImage imageWithCGImage:scaledImage];

    CGColorSpaceRelease(cs);
    CGContextRelease(bitmapRef);
    CGImageRelease(bitmapImage);
    CGImageRelease(scaledImage);

    // 二维码视图
    UIImageView *qrView = [[UIImageView alloc] initWithFrame:CGRectMake((240-qrSize)/2, 36, qrSize, qrSize)];
    qrView.image = qrImage;
    qrView.contentMode = UIViewContentModeScaleAspectFit;
    qrView.layer.cornerRadius = 8;
    qrView.layer.masksToBounds = YES;
    qrView.backgroundColor = [UIColor whiteColor];
    [contentView addSubview:qrView];

    // 说明文字
    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 36+qrSize+10, 240, 24)];
    tipLabel.text = @"QQ扫一扫联系我";
    tipLabel.textAlignment = NSTextAlignmentCenter;
    tipLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    tipLabel.textColor = [UIColor colorWithWhite:0.3 alpha:1];
    [contentView addSubview:tipLabel];

    // 用 UIViewController 包裹内容视图
    UIViewController *vc = [[UIViewController alloc] init];
    vc.preferredContentSize = CGSizeMake(240, 280); // 调整为新的尺寸
    vc.view.backgroundColor = [UIColor clearColor];
    [vc.view addSubview:contentView];

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"联系方式" message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alert setValue:vc forKey:@"contentViewController"];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"关闭" style:UIAlertActionStyleCancel handler:nil];
    [alert addAction:okAction];
    [self presentViewController:alert animated:YES completion:nil];
}

// 激活按钮事件
- (void)activateButtonTapped:(id)sender {
    // 创建 UIAlertController
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"输入激活码" message:@"" preferredStyle:UIAlertControllerStyleAlert];

    // 添加输入框
    [alert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        textField.placeholder = @"请输入激活码";
    }];

    // 确认按钮
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:@"确认" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        // 获取输入的激活码
        UITextField *textField = alert.textFields.firstObject;
        if (textField.text.length > 0) {
            // 这里可以添加验证激活码的逻辑
            // 验证成功后的处理逻辑
            [self handleActivationCode:textField.text];
        } else {
            // 提示用户输入激活码
            [Dialog show:@"请输入激活码！" duration:2.0];
        }
    }];

    // 购买激活码按钮
    UIAlertAction *buyAction = [UIAlertAction actionWithTitle:@"购买激活码" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        // 打开浏览器加载指定的 URL 地址
        NSURL *url = [NSURL URLWithString:@"http://47.108.169.125:5888/"];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
    }];

    // 取消按钮
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];

    // 添加按钮到弹窗
    [alert addAction:confirmAction];
    [alert addAction:buyAction];
    [alert addAction:cancelAction];

    // 显示弹窗
    [self presentViewController:alert animated:YES completion:nil];
}

// 处理激活码的验证逻辑
- (void)handleActivationCode:(NSString *)activationCode {
    // // 这里可以添加验证激活码的逻辑
    // // 例如，发送激活码到服务器进行验证
    // // 判断activationCode长度是否为13位
    // if (activationCode.length != 13) {
    //     dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    //         [Dialog show:@"激活码格式错误" duration:5.0];
    //     });
    //     return;
    // }
  
    // // MRYIPCCenter* center = [MRYIPCCenter centerNamed:g4SpringBoardService];
    // // MRYIPCCenter* center = [MRYIPCCenter centerNamed:@"com.g4.core.services.springboard"];
    // static const uint8_t springboardKeyName[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0xDF,0x86,0xDA,0xEA,0xC0,0x35,0x0C,0xF0,0xDF,0x66,0x51,0xE7,0x13,0x3B,0xD7,0x85,0xB7,0x72,0xD9,0xFF,0x5C,0xDB,0x28,0x52,0x96,0x34,0x43,0x15,0x75,0xA9,0x26,0x07,0xED,0xC4,0xD8,0xBC};
    // const char *serviceName = DECRYPTED_STRING_AUTO(springboardKeyName);
    // MRYIPCCenter *center = [MRYIPCCenter centerNamed:[NSString stringWithUTF8String:serviceName]];
    // NSDictionary* response = [center callExternalMethod:@selector(handleAuthDevice:) withArguments:@{@"code" : activationCode, @"appId": appId, @"version": appVersion}];

    // if (!response) {
    //     hasAuthorized = NO;
    //     dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    //         [Dialog show:@"激活程序异常" duration:5.0];
    //     });
    //     return;
    // }
    // // 判断查询结果是否正常
    // if (![response[@"status"] boolValue]) {
    //     hasAuthorized = NO;
    //     NSString *error = response[@"error"];
    //     NSString *errorString = error ? [NSString stringWithFormat:@"%@", error] : @"null";
    //     dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    //         [Dialog show:errorString duration:5.0];
    //     });
    //     return;
    // }
    // NSString *encryptedData = response[@"data"];
    // if (!encryptedData) {
    //     dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    //         [Dialog show:@"激活程序异常,请重试" duration:5.0];
    //     });
    //     return;
    // }
    // QueryResult result = parse_query_result([encryptedData UTF8String]);
    // if (!result.success) {
    //     hasAuthorized = NO;
    //     dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    //         [Dialog show:[NSString stringWithFormat:@"%s", result.error ? result.error : "null"] duration:2.0];
    //     });
    //     free_parse_query_result(result);
    //     return;
    // }
    // [self showExitDialog:@"激活成功,请重启后使用"];
}

// 刷新认证状态
- (void)refreshAuthStatus:(id)sender {
    UIButton *refreshButton = (UIButton *)sender;
    
    // 获取当前时间
    NSDate *currentTime = [NSDate date];

    // 检查上次点击的时间
    if (self.lastRefreshTime) {
        NSTimeInterval timeSinceLastRefresh = [currentTime timeIntervalSinceDate:self.lastRefreshTime];
        if (timeSinceLastRefresh < 5.0) {
            // 如果距离上次点击的时间小于5秒，则忽略点击
            // [Toast show:@"请勿频繁刷新，5秒后再试" duration:1.0];
            return;
        }
    }
    
    // 更新上次点击的时间
    self.lastRefreshTime = currentTime;

    // 禁用按钮并显示旋转动画
    refreshButton.enabled = NO;
    
    // 创建旋转动画
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.toValue = @(M_PI * 1.5);
    rotationAnimation.duration = 0.3;
    rotationAnimation.cumulative = YES;
    rotationAnimation.repeatCount = HUGE_VALF;
    
    [refreshButton.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
    
    // 延迟执行权限认证
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self performAuthCheck];
        
        // 停止动画并重新启用按钮
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [refreshButton.layer removeAnimationForKey:@"rotationAnimation"];
            refreshButton.enabled = YES;
            // [Toast show:@"刷新成功" duration:1.0];
        });
    });
}

@end