/**
 * @file productInfo.h
 * @brief 产品信息加密数据定义
 *
 * 包含版本号和产品ID的加密字节数组定义
 * 这些数据会在运行时解密并用于网络请求
 */

#ifndef PRODUCT_INFO_H
#define PRODUCT_INFO_H

#include <stdint.h>
#include <stddef.h>

/**
 * @brief 版本号加密数据
 *
 * 使用 StringCryptor_v2 加密的版本号信息
 * 运行时会自动解密为版本字符串
 */
static const uint8_t version_data[] = {
    0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
    0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A,
    0xA9, 0x7B, 0x63, 0xE1, 0x8A, 0x33, 0x8E, 0x45,
    0xBA
};

/**
 * @brief 版本号加密数据长度
 */
static const size_t version_data_length = sizeof(version_data);

/**
 * @brief 产品ID加密数据
 *
 * 使用 StringCryptor_v2 加密的产品ID信息
 * 运行时会自动解密为产品ID字符串
 */
static const uint8_t product_id_data[] = {
    0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
    0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A,
    0xDF, 0x86, 0xDA, 0xEA, 0xC0, 0x35, 0x0C, 0x98,
    0x09, 0xD3, 0x22, 0xFE, 0x32, 0x81, 0xC0, 0xA2,
    0x9A, 0x76
};

/**
 * @brief 产品ID加密数据长度
 */
static const size_t product_id_data_length = sizeof(product_id_data);

#endif // PRODUCT_INFO_H