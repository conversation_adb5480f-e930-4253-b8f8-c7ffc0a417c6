#include <stdio.h>
#include <stdlib.h>     // 🔧 添加getprogname支持
#include <string.h>
#include <unistd.h>
#include <time.h>       // 🔧 添加time函数支持
#include "Log.h"
#include <ctype.h>
#include <dispatch/dispatch.h>
#include <objc/NSObjCRuntime.h>
#include <Foundation/Foundation.h>
#include <UIKit/UIKit.h>
#include "JSONUtils.h"
#include "HttpService.h"
#include "DataCrypto.h"
#include "SocketHTTP.h"
#include "CryptoRequest.h"
#include "StringCryptor_v2.h"
#include <objc/runtime.h>
#include <objc/message.h>
#include "DataRecordManager.h"
#include "GlobalNetworkData.h"
#include "productInfo.h"
#include "Toast.h"
#include "Controller.h"

// #include "JDTools.h"
#include <stdbool.h>

// 记录数据 - 全局变量定义（对应头文件中的extern声明）
DataRecordManager *s_networkMonitorInstance = nil;

// 记录服务状态
static bool service_status = false;

void invokeCashierPayMethod(const char *functionIdStr) {
    // 1. 获取 topVC
    id topVC = getTopViewController();
    if (!topVC) {
        [Toast show:@"获取页面失败" duration:1.0];
        LOG("获取页面失败");
        return;
    }
    const char *className = getClassName(topVC);
    // 安全地判断类名
    if (strcmp(className, "JDCashierPayViewController") != 0) {
        [Toast show:@"当前页面不是收银台" duration:2.0];
        LOG("当前页面不是收银台");
        return;
    }
    // 3. 构造 selector
    SEL sel = sel_registerName("_payWithParamModel:channelModel:functionId:");

    // 4. 创建 NSString *，使用调用者传入的 const char * 参数
    Class NSStringCls = objc_getClass("NSString");
    SEL allocSel = sel_registerName("alloc");
    SEL initUTF8Sel = sel_registerName("initWithUTF8String:");
    id str = ((id (*)(id, SEL, const char *))objc_msgSend)(
        ((id (*)(Class, SEL))objc_msgSend)(NSStringCls, allocSel),
        initUTF8Sel,
        functionIdStr
    );

    // 5. 执行反射调用，前两个参数传 nil，第三个传字符串对象
    ((void (*)(id, SEL, id, id, id))objc_msgSend)(topVC, sel, 0, 0, str);

}

static int request_handler(const char* method, const char* path, const char* headers, const char* body, int client_socket) {

    // 🔧 签名验证通过，处理不同的路径
    if (strcmp(path, "/command") == 0) {
        // 🔧 控制命令接口 - 解析JSON数据
        cJSON* json = JSONUtils_ParseString(body);
        if (json == NULL) {
            return HTTP_STATUS_BAD_REQUEST;
        }
        LOG("Received command: %s", body);

        const char* data = JSONUtils_GetStringValue(json, "data");
        const char* key = JSONUtils_GetStringValue(json, "key");
        const char* sign = JSONUtils_GetStringValue(json, "sign");
        if (data == NULL || key == NULL || sign == NULL) {
            cJSON_Delete(json);
            return HTTP_STATUS_BAD_REQUEST;
        }
        DecryptResult decryptResult = decrypt_request_data(data, key, sign);
        if (!decryptResult.status) {
            cJSON_Delete(json);
            return HTTP_STATUS_BAD_REQUEST;
        }
        cJSON* requestData = decryptResult.data;
        const char* temp_windowId = JSONUtils_GetStringValue(requestData, "windowId");
        const char* temp_buttonId = JSONUtils_GetStringValue(requestData, "buttonId");
        const char* temp_serialNumber = JSONUtils_GetStringValue(requestData, "serialNumber");
        if (temp_windowId == NULL || temp_buttonId == NULL || temp_serialNumber == NULL) {
            free_decrypt_result(&decryptResult);
            cJSON_Delete(json);
            return HTTP_STATUS_BAD_REQUEST;
        }
        // 创建字符串副本以确保异步执行时内存安全
        char* windowId = strdup(temp_windowId);
        char* buttonId = strdup(temp_buttonId);
        char* serialNumber = strdup(temp_serialNumber);

        // 检查内存分配是否成功
        if (!windowId || !buttonId || !serialNumber) {
            LOG("内存分配失败");
            if (windowId) free(windowId);
            if (buttonId) free(buttonId);
            if (serialNumber) free(serialNumber);
            free_decrypt_result(&decryptResult);
            cJSON_Delete(json);
            return HTTP_STATUS_INTERNAL_ERROR;
        }

       

        // 在主线程执行
        dispatch_async(dispatch_get_main_queue(), ^{

            LOG("💚💚💚💚💚💚💚💚💚💚按钮信息: %s, %s, %s", windowId, buttonId, serialNumber);
            if (strcmp(windowId, "paytool") == 0) {
                if (s_networkMonitorInstance) {
                    [s_networkMonitorInstance updateStatus:YES];
                    invokeCashierPayMethod(buttonId);
                } else {
                    [Toast show:@"代付功能服务异常" duration:2.0];
                }
            }
            // 释放异步block中使用的字符串副本
            free(windowId);
            free(buttonId);
            free(serialNumber);

        });

        free_decrypt_result(&decryptResult);
        cJSON_Delete(json);

        http_service_send_json(client_socket, HTTP_STATUS_OK, "{\"success\": true}");
        return 1; // 已处理
    }

    // 🔧 返回0表示路径未匹配，HttpService会直接关闭连接
    return 0;
}

// 向JDToolsService获取设备信息，然后向服务器查询设备状态
bool checkDeviceStatus() {
    // 临时变量初始化
    char* response = NULL;
    cJSON* json = NULL;
    char *query = NULL;
    DecryptResult decryptDeviceInfoResult = {0};

    // 设置请求头
    const char* headers = "Content-Type: application/json";
    // 发送请求
    int statusCode = 0;
    // http://127.0.0.1:33672/device
    const uint8_t dvUrlKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x67,0x25,0x68,0xDF,0x79,0x06,0x07,0x6E,0x88,0x49,0x19,0xB3,0x3C,0xA3,0x3C,0xA5,0x23,0xF9,0x63,0x9A,0x82,0xDC,0x62,0xC6,0x7F,0x7A,0xB7,0x62,0x82,0xCC,0x38,0x0C,0x08};
    char *durl = decrypt_string_v2(dvUrlKey, sizeof(dvUrlKey));
    if (!durl) {
        LOG("解密本地请求设备信息url解密失败");
        return false;
    }
    bool success = socket_post_request_sync(durl, "{}", headers, &response, &statusCode);
    free(durl);
    if (!success) {
        LOG("HTTP请求失败: %s", response ? response : "未知错误");
        if (response) free(response);
        return false;
    }

    LOG("HTTP状态码: %d", statusCode);
    LOG("响应内容: %s", response ? response : "空响应");

    if (statusCode != 200) {
        LOG("HTTP状态码错误: %d", statusCode);
        if (response) free(response);
        return false;
    }

    json = JSONUtils_ParseString(response);
    if (json == NULL) {
        if (response) free(response);
        return false;
    }

    const char* data = JSONUtils_GetStringValue(json, "data");
    const char* key = JSONUtils_GetStringValue(json, "key");
    const char* sign = JSONUtils_GetStringValue(json, "sign");
    if (data == NULL || key == NULL || sign == NULL) {
        cJSON_Delete(json);
        if (response) free(response);
        return false;
    }
    // 解密设备信息
    decryptDeviceInfoResult = decrypt_request_data(data, key, sign);
    if (!decryptDeviceInfoResult.status) {
        cJSON_Delete(json);
        if (response) free(response);
        return false;
    }
    cJSON* requestData = decryptDeviceInfoResult.data;
    if (!requestData) {
        LOG("设备信息解析失败");
        cJSON_Delete(json);
        if (response) free(response);
        return false;
    }
    // 从requestData中取出序列号
    const char* serial_number = JSONUtils_GetStringValue(requestData, "serialNumber");
    if (!serial_number) {
        LOG("设备信息解析失败");
        cJSON_Delete(json);
        if (response) free(response);
        return false;
    }
    LOG("成功获取序列号: %s", serial_number);

    // query
    const uint8_t qrkey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xAF, 0x31, 0x11, 0xC8, 0x72, 0x6F, 0x32, 0xA5, 0x9A };
    query = decrypt_string_v2(qrkey, sizeof(qrkey));
    if (!query) {
        LOG("check 解密失败");
        goto cleanup;
    }
    CryptoRequestConfig config = {
        .path = "/paytools/v1",
        .action = query,
        .serial_number = serial_number,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

    // 先释放临时变量
    if (query) free(query);
    cJSON_Delete(json);
    free_decrypt_result(&decryptDeviceInfoResult);
    if (response) free(response);

    if (!result.success) {
        crypto_request_free_result(&result);
        return false;
    }
    if (!result.response_data) {
        crypto_request_free_result(&result);
        return false;
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        crypto_request_free_result(&result);
        return false;
    }
    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        if (message) {
            const char* error = message ? message : "未知错误";
            LOG("设备状态校验失败: %s", error);
            [Toast show:[NSString stringWithUTF8String:error] duration:2.0];
        } else {
            LOG("设备状态校验失败: 未知错误 %d", code);
        }
        
        crypto_request_free_result(&result);
        return false;
    }
    cJSON* response_data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!response_data) {
        crypto_request_free_result(&result);
        LOG("设备状态校验失败: 无法获取 data");
        // showToastMessage("设备状态校验失败: 获取 data 失败", 2.0);
        [Toast show:@"设备状态校验失败: 获取 data 失败" duration:2.0];
        return false;
    }
    bool status = JSONUtils_GetBoolValue(response_data, "status", false);

    // 释放资源
    crypto_request_free_result(&result);

    LOG("授权状态: %s", status ? "已授权" : "未授权");
    if (!status) {
        // showToastMessage("设备已到期或未授权", 2.0);
        [Toast show:@"设备已到期或未授权" duration:2.0];
    }
    return status;

cleanup:
    // 统一清理临时变量
    if (query) free(query);
    if (json) cJSON_Delete(json);
    if (decryptDeviceInfoResult.status) {
        free_decrypt_result(&decryptDeviceInfoResult);
    }
    if (response) free(response);
    return false;
}

__attribute__((constructor))
static void start_jdpaytool_service(void) {
    
    // 只在特定进程中运行示例
    if (strcmp(getprogname(), "JD4iPhone") == 0) {

        if (!checkDeviceStatus()) {
            LOG("设备未授权或未绑定");
            return;
        }

        // 启动HTTP服务
        // start_simple_http_service();
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 127.0.0.1
            const uint8_t addKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA9, 0x5B, 0x81, 0xEF, 0x8A, 0x01, 0x5F, 0x13, 0x87, 0x38, 0x5B, 0x9B, 0x9C };
            char *address = decrypt_string_v2(addKey, sizeof(addKey));
            if (!address) {
                return;
            }
            // 创建自定义配置
            http_service_config_t config = {
                .port = 51862,
                .bind_address = address,  // 本地绑定
                .request_handler = request_handler,
                .service_name = "JDTools"
            };

            // 启动服务
            if (http_service_start(&config) == 0) {
                service_status = true;
                enable_no_sleep("KeepAlive");
            }
            free(address);

        });

        // 启动数据收集
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 初始化全局网络数据
            initializeGlobalNetworkData();
            // 创建 AFURLSessionManager 实例，传入全局变量
            s_networkMonitorInstance = [[DataRecordManager alloc] initWithDataStore:g_collectedNetworkData];
            // 启动网络监控
            [s_networkMonitorInstance startMonitoring];
        });
     
    }
}

// 析构函数
__attribute__((destructor))
static void cleanup_jdpaytool_service(void) {
    if (strcmp(getprogname(), "JD4iPhone") == 0) {
        if (service_status) {
            // 确保服务已停止
            http_service_stop();

            disable_no_sleep();
        }

        // // 清理全局变量
        if (s_networkMonitorInstance) {
            // [s_networkMonitorInstance stopMonitoring];
            s_networkMonitorInstance = nil;
        }
    }
}
