#import "G4AppDelegate.h"
#import "G4RootViewController.h"

@implementation G4AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    _window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    
    // 直接使用 G4RootViewController 而不是包装在 UINavigationController 中
    _rootViewController = [[UINavigationController alloc] initWithRootViewController:[[G4RootViewController alloc] init]];
    _window.rootViewController = _rootViewController;
    [_window makeKeyAndVisible];
    return YES;
}

@end