# JDPayment Plugin Configuration
# This file contains all configuration for the JDPayment plugin

# DEBUG 控制变量，默认为 0（不输出日志） 1 输出日志
# DEBUG = 0

JDPayment_FILES = \
$(CRYPTOR_FILES) \
$(CJSON_FILES) \
$(REQUESTMANAGER_FILES) \
$(HTTPSERVICE_FILES) \
../../Core/Device/DeviceInfo.c \
../../Core/Window/Dialog.m \
../../Core/Window/Toast.m \
../../Core/Window/QRCode.m \
../../Tweaks/JDTools/Controller/Hook/DataRecordManager.m \
../../Tweaks/JDTools/Controller/Hook/GlobalNetworkData.m \
../../Tweaks/JDTools/Controller/Hook/QR.m \
../../Tweaks/JDTools/Controller/Controller.c \
JDPayment.m

# Compiler flags for JDPayment
JDPayment_CFLAGS = -fobjc-arc \
$(CRYPTOR_CFLAGS) \
$(CJSON_CFLAGS) \
$(REQUESTMANAGER_CFLAGS) \
$(HTTPSERVICE_CFLAGS) \
-I./Controller \
-I./Controller/Hook \
-I../../Core/Window \
-I../../Core/Device \
-I../../Core/Log/ \
-I../../Tweaks/JDTools/Controller \
-I../../Tweaks/JDTools/Controller/Hook \
-DDEBUG=$(DEBUG) \


JDPayment_FRAMEWORKS = IOKit CoreFoundation
JDPayment_LDFLAGS += -framework Foundation -framework UIKit -lobjc




RANDOM_SEED := $(shell echo $$RANDOM)
JDPayment_CFLAGS += -fobjc-arc \
                    -O3 \
                    -flto \
                    -fvisibility=hidden \
                    -funroll-loops \
                    -fdata-sections \
                    -ffunction-sections \
                    -fomit-frame-pointer \
                    -finline-functions \
                    -fno-stack-protector \
                    -fno-common \
                    -fno-asynchronous-unwind-tables \
                    -fno-exceptions \
                    -ObjC \
                    -DANTI_PTRACE \
                    -DANTI_HOOK \
                    -DANTI_DUMP \
                    -DANTI_DEBUG \
                    -DENABLE_ENCRYPTION \
                    -DRANDOM_SEED=$(RANDOM_SEED)

JDPayment_LDFLAGS = \
-Wl,-S \
-Wl,-x \
-Wl,-dead_strip \
-F$(THEOS)/sdks/iPhoneOS14.5.sdk/System/Library/PrivateFrameworks \


JDPayment_FRAMEWORKS = UIKit Foundation SystemConfiguration Network CFNetwork CoreFoundation IOKit MobileCoreServices AppSupport
# 在链接时使用 strip 删除符号表和调试信息
JDPayment_POST_LINK = \
    strip -S --strip-all --remove-section=.note.gnu.build-id --remove-section=.comment --remove-section=__LLVM,__bitcode $@ && \
    \
    codesign --remove-signature $@ && \
    \
    codesign --force --sign - $@