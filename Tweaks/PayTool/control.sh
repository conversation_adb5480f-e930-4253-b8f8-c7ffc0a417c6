#!/bin/bash

# 脚本用法检查
if [ $# -ne 1 ]; then
  echo "Usage: $0 rootful|rootless"
  exit 1
fi

MODE=$1

if [ "$MODE" = "rootful" ]; then
  SRC_FILE="control-rootful"
elif [ "$MODE" = "rootless" ]; then
  SRC_FILE="control-rootless"
else
  echo "Invalid argument. Use 'rootful' or 'rootless'"
  exit 1
fi

if [ ! -f "$SRC_FILE" ]; then
  echo "Source file '$SRC_FILE' does not exist."
  exit 1
fi

# 复制文件内容到 control
cp "$SRC_FILE" control

echo "完成复制 $SRC_FILE 到 control"