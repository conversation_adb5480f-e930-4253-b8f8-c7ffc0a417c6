# 桌面应用部分
APPLICATION_NAME = PayTool
PayTool_RESOURCE_DIRS = Resources
PayTool_PRIVATE_FRAMEWORKS = AppSupport
PayTool_LDFLAGS = \
-Wl,-S \
-Wl,-x \
-Wl,-dead_strip \
-F$(THEOS)/sdks/iPhoneOS12.4.sdk/System/Library/PrivateFrameworks \
-weak_framework UIKit \
-framework UIKit

# 框架链接 - 确保包含所有必要的框架
PayTool_FRAMEWORKS = UIKit Foundation SystemConfiguration Network CFNetwork CoreFoundation IOKit MobileCoreServices AppSupport

# 确保 iOS 13+ 符号可用
PayTool_CFLAGS += -mios-version-min=13.0
PayTool_LDFLAGS += -mios-version-min=13.0
# 在链接时使用 strip 删除符号表和调试信息
PayTool_POST_LINK = \
	strip -S --strip-all --remove-section=.note.gnu.build-id --remove-section=.comment --remove-section=__LLVM,__bitcode $@ && \
	\
	codesign --remove-signature $@ && \
	\
	codesign --force --sign - $@


# PayTool_LIBRARIES = substrate mryipc
PayTool_FILES = \
$(CRYPTOR_FILES) \
$(CJSON_FILES) \
./main.m \
./G4AppDelegate.m \
./G4RootViewController.m \
../../Core/Window/Dialog.m \
../../Core/RequestManager/SocketHTTP.c \
../../Core/RequestManager/CryptoRequest.c \


# ../../Modules/Window/DialogManager.m \
# ../../Modules/Window/ToastManager.m \
# ../../Modules/Crypto/MSGEncryption.c \
# ../../Modules/Crypto/DecryptString.c \
# ../../Modules/Crypto/Base64.c \
# ../../Modules/CJSON/cJSON.c \
# ../../Modules/CJSON/JSONUtils.c \
# ../../Modules/Network/query.c \


PayTool_CFLAGS += \
$(CRYPTOR_CFLAGS) \
$(CJSON_CFLAGS) \
-I../../Core/Window \
-I../../Core/RequestManager \
-I../../Core/Log/ \
-DDEBUG=$(DEBUG) \

