#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import "Log.h"
#import "FloatingView.h"
#import "FloatingWindow.h"
#import "ButtonClickHandler.h"
#import "DeviceInfo.h"
#import "HttpService.h"
#import "DataCrypto.h"
#import "StringCryptor_v2.h"
#import "JSONUtils.h"
#import "CryptoRequest.h"
#import "productInfo.h"
#import "Toast.h"
#include <time.h>

// 记录服务启动状态
bool service_status = false;
// 记录设备到期时间
int expireTime = 0;
// 定义全局常量指针
const char *serialNumber = NULL;
// 定义版本号
const char *version = "1.0.0";

static NSMutableArray<FloatingView *> *floatingViews = nil;
static NSMutableArray<FloatingWindow *> *floatingWindows = nil;

static int request_handler(const char* method, const char* path, const char* headers, const char* body, int client_socket) {

    // 🔧 签名验证通过，处理不同的路径
    if (strcmp(path, "/device") == 0) {

        char serialBuffer[32];
        getSerialNumber(serialBuffer, sizeof(serialBuffer));

        // 构建简化的设备信息：只包含序列号
        char* simple_device_info = NULL;
        size_t json_size = strlen(serialBuffer) + 32; // 预留足够空间
        simple_device_info = malloc(json_size);
        if (!simple_device_info) {
            LOG("分配设备信息内存失败");
            return 0;
        }

        snprintf(simple_device_info, json_size, "{\"serialNumber\":\"%s\"}", serialBuffer);
        LOG("构建简化设备信息: %s", simple_device_info);

        EncryptRequestData encrypted_response = encrypt_request_data(simple_device_info);
        if (!encrypted_response.status) {
            free(simple_device_info);
            LOG("设备信息加密失败");
            return 0;
        }
        free(simple_device_info);

        // 安全地计算所需大小，防止整数溢出
        size_t data_len = strlen(encrypted_response.data);
        size_t key_len = strlen(encrypted_response.key);
        size_t sign_len = strlen(encrypted_response.sign);

        // 检查单个字段长度
        if (data_len > 16384 || key_len > 1024 || sign_len > 1024) {
            LOG("加密响应数据字段过长");
            free_encrypt_request_data(&encrypted_response);
            return 0;
        }

        // 安全地计算总大小
        size_t encrypted_response_size = data_len + key_len + sign_len + 100;

        // 检查是否溢出
        if (encrypted_response_size < data_len || encrypted_response_size < key_len || encrypted_response_size < sign_len) {
            LOG("加密响应数据大小计算溢出");
            free_encrypt_request_data(&encrypted_response);
            return 0;
        }

        char* encrypted_response_json = (char*)malloc(encrypted_response_size);
        if (!encrypted_response_json) {
            free_encrypt_request_data(&encrypted_response);
            LOG("分配内存失败");
            return 0;
        }
        int written = snprintf(encrypted_response_json, encrypted_response_size, "{\"data\":\"%s\",\"key\":\"%s\",\"sign\":\"%s\"}", encrypted_response.data, encrypted_response.key, encrypted_response.sign);
        if (written < 0 || (size_t)written >= encrypted_response_size) {
            LOG("加密响应数据JSON格式化失败");
            free(encrypted_response_json);
            free_encrypt_request_data(&encrypted_response);
            return 0;
        }

        LOG("响应加密设备数据: %s", encrypted_response_json);
        free_encrypt_request_data(&encrypted_response);
        

        http_service_send_json(client_socket, HTTP_STATUS_OK, encrypted_response_json);
        free(encrypted_response_json);
        LOG("设备信息响应成功");
        return 1; // 已处理
    } else if (strcmp(path, "/service") == 0) {
        // 1. 获取设备信息
        // 2. 查询当前设备的授权信息
        // 3. 如果授权了就启动服务显示窗口，返回结果
    }
    // 🔧 返回0表示路径未匹配，HttpService会直接关闭连接
    return 0;
}


// 获取本地时间、设备信息进行服务器校验状态，判断时间，绑定设备
bool checkDeviceStatus(void) {
    // char* deviceInfo = deviceInfoInJson();
    // if (!deviceInfo) {
    //     return false;
    // }
    char serialBuffer[32];
    getSerialNumber(serialBuffer, sizeof(serialBuffer));

    // 临时变量声明（用于统一清理）
    char *query = NULL;
    char *request_data = NULL;

    // query
    const uint8_t qrkey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xAF, 0x31, 0x11, 0xC8, 0x72, 0x6F, 0x32, 0xA5, 0x9A };
    query = decrypt_string_v2(qrkey, sizeof(qrkey));
    if (!query) {
        LOG("check 解密失败");
        goto cleanup;
    }

    // 使用asprintf构建请求数据，检查返回值
    // int asprintf_result = asprintf(&request_data, "{\"%s\":\"%s\", \"serialNumber\": \"%s\"}", ac, ck, serialBuffer);
    // if (asprintf_result == -1 || !request_data) {
    //     LOG("构建请求数据失败");
    //     goto cleanup;
    // }
    
    CryptoRequestConfig config = {
        .path = "/paytools/v1",
        .action = query,
        .serial_number = serialBuffer,
        .version = version_data,
        .version_length = version_data_length,
        .product_id = product_id_data,
        .product_id_length = product_id_data_length
    };

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

    // 先释放临时变量
    if (request_data) free(request_data);
    // if (deviceInfo) free(deviceInfo);
    if (query) free(query);

    if (!result.success) {
        crypto_request_free_result(&result);
        return false;
    }
    if (!result.response_data) {
        crypto_request_free_result(&result);
        return false;
    }
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 0) {
        crypto_request_free_result(&result);
        LOG("设备状态校验失败: 响应数据为空");
        return false;
    }
    if (code != 200) {
        const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
        if (message) {
            const char* error = message ? message : "未知错误";
            (void)error;
            LOG("设备状态校验失败: %s", error);
        } else {
            LOG("设备状态校验失败: 未知错误 %d", code);
        }
        
        crypto_request_free_result(&result);
        return false;
    }
    cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
    if (!data) {
        crypto_request_free_result(&result);
        LOG("设备状态校验失败: 响应数据缺少data字段");
        return false;
    }

    int time = JSONUtils_GetIntValue(data, "time", 0);
    if (!time || time < 0) {
        LOG("未返回设备到期时间");
        crypto_request_free_result(&result);
        return false;
    }

    LOG("设备到期时间: %d", time);

    expireTime = time;

    bool status = JSONUtils_GetBoolValue(data, "status", false);
    // 释放资源
    crypto_request_free_result(&result);

    LOG("[JDPayTweak] ---------->授权状态: %s", status ? "已授权" : "未授权");

    return status;

cleanup:
    // 统一清理临时变量
    if (request_data) free(request_data);
    return false;
}

__attribute__((constructor))
static void start_jdtools_service(void) {

    LOG("[JDPayTweak]: Starting JDPayTweak");

    const uint8_t prognameKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x5C,0x46,0x7E,0x96,0x9F,0x4A,0xE6,0x1E,0x01,0x6B,0xA9,0x03,0xD6,0x2D,0xD2};

    char *progname = decrypt_string_v2(prognameKey, sizeof(prognameKey));
    if (!progname) {
        return;
    }
    LOG("[JDPayTweak]: current process name : %s", progname);
    if (strcmp(getprogname(), progname) != 0) {
        // 释放内存
        free(progname);
        LOG("[JDPayTweak]: Not the target process");
        return;
    }
    // 释放内存
    free(progname);

    if (!checkDeviceStatus()) {
        return;
    }

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 初始化数组
        floatingViews = [NSMutableArray array];
        floatingWindows = [NSMutableArray array];

        CGFloat startY = 150.00;
        UIColor *uiColor = [UIColor colorWithRed:0x02/255.0 green:0x56/255.0 blue:0xff/255.0 alpha:1.0];

        NSMutableArray *windowConfigs = [NSMutableArray arrayWithArray:@[
            @{
                @"buttonText": @"付",
                @"windowId": @"paytool",
                @"color": uiColor,
                @"position": @"right",
                @"startY": @(startY),
                @"buttonConfigs": @[
                    // @{kButtonTitleKey: @"测试", kButtonIdKey: @"test", kButtonAutoCollapseKey: @(YES)},
                    @{kButtonTitleKey: @"云闪付代付", kButtonIdKey: @"platUnionPay", kButtonAutoCollapseKey: @YES},
                    @{kButtonTitleKey: @"QQ代付", kButtonIdKey: @"platQQWalletPay", kButtonAutoCollapseKey: @YES},
                    @{kButtonTitleKey: @"微信代付", kButtonIdKey: @"platDFPay", kButtonAutoCollapseKey: @YES},
                    @{kButtonTitleKey: @"微信支付", kButtonIdKey: @"platWXPay", kButtonAutoCollapseKey: @YES},
                    @{kButtonTitleKey: @"增加一个", kButtonIdKey: @"add", kButtonAutoCollapseKey: @(YES)},
                ],
                @"actionBlock": ^(NSString *buttonId) {
                    NSLOG("[JDPayTweak] 触发按钮 %@", buttonId);
                   ButtonClickParams params = {0};
                    params.buttonId = [buttonId UTF8String];
                    params.windowId = "paytool";
                    params.serialNumber = serialNumber;
                    bool status = handleButtonClick(&params);
                    if (!status) {
                        [Toast show:@"请检查京东是否已启动" duration:2.0];
                    }
                    return status;

                    if ([buttonId isEqualToString:@"test"]) {
                    } else if ([buttonId isEqualToString:@"add"]) {
                        // 找到对应的 FloatingView 并添加新按钮
                        // 由于我们只有一个窗口，直接使用第一个
                        if (floatingViews.count > 0) {
                            FloatingView *floatingView = floatingViews[0];

                            // 创建新按钮配置
                            // 创建一个安全的测试按钮，避免删除操作
                            NSDictionary *newButtonConfig = @{kButtonTitleKey: @"删除",kButtonIdKey: @"remove",kButtonAutoCollapseKey: @(YES)};

                            // 调用 FloatingView 的 addButton 方法
                            [floatingView addButton:newButtonConfig];

                            NSLOG("[JDPayTweak] 成功添加新按钮: %@", newButtonConfig[kButtonTitleKey]);
                        } else {
                            NSLOG("[JDPayTweak] 没有找到可用的悬浮窗");
                        }

                    } else if ([buttonId isEqualToString:@"remove"]) {
                        // 安全的测试按钮，不执行危险的删除操作
                        NSLOG("[JDPayTweak] 测试按钮被点击---------》");

                        if (floatingViews.count > 0) {
                            FloatingView *floatingView = floatingViews[0];
                            [floatingView removeButtonWithId:@"remove"];
                        }

                    } else {
                        NSLOG("[JDPayTweak] 未知按钮: %@", buttonId);
                    }
                }
            }
        ]];

        // 创建默认悬浮窗
        for (NSInteger i = 0; i < windowConfigs.count; i++) {
            NSMutableDictionary *config = [windowConfigs[i] mutableCopy];

            // 动态计算startY：第一个使用原始startY，后续每个递增50
            CGFloat dynamicStartY = startY + (i * 50);
            config[@"startY"] = @(dynamicStartY);

            // 创建超级悬浮窗口
            FloatingWindow *window = [[FloatingWindow alloc] init];

            // 创建悬浮视图
            FloatingView *view = [[FloatingView alloc] initWithConfig:config];

            // 使用优化的添加子视图方法
            [window addFloatingSubview:view];

            NSString *windowId = config[@"windowId"];
            (void)windowId;
            LOG("[JDPayTweak] 创建默认悬浮窗 %ld，配置: %s", (long)i, windowId ? windowId.UTF8String : "未知");

            // 显示动画 - 错开时间
            view.alpha = 0;
            view.transform = CGAffineTransformMakeScale(0.5, 0.5);

            // 强制显示并保持在最顶层
            window.hidden = NO;
            [window makeKeyAndVisible];

            // 强制刷新显示（解决某些系统不显示的问题）
            [window forceRefreshDisplay];

            // ✅ 添加到数组中进行管理
            [floatingWindows addObject:window];
            [floatingViews addObject:view];

            // 错开显示动画时间
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * i * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [UIView animateWithDuration:0.5
                                        delay:0
                        usingSpringWithDamping:0.7
                        initialSpringVelocity:0.5
                                    options:UIViewAnimationOptionCurveEaseOut
                                    animations:^{
                                        view.alpha = 1.0;
                                        view.transform = CGAffineTransformIdentity;
                                    }
                                    completion:^(BOOL finished) {
                                        LOG("[JDPayTweak] 默认悬浮窗 %ld 显示动画完成", (long)i);
                                    }];
            });
        }
    });

    // 记录当前设备序列号
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        char serialBuffer[32];
        getSerialNumber(serialBuffer, sizeof(serialBuffer));

        // 复制到常量内存，检查分配是否成功
        serialNumber = strdup(serialBuffer);
        if (!serialNumber) {
            LOG("[JDPayTweak]: 序列号内存分配失败");
            return;
        }

        LOG("[JDPayTweak]: Service started with serial number: %s", serialNumber);

    });

    // 启动http服务
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 127.0.0.1
        const uint8_t addKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA9, 0x5B, 0x81, 0xEF, 0x8A, 0x01, 0x5F, 0x13, 0x87, 0x38, 0x5B, 0x9B, 0x9C };
        char *address = decrypt_string_v2(addKey, sizeof(addKey));
        if (!address) {
            return;
        }
        // LOG("解密得到地址: %s", address);
        const char *serviceName = "JDPayTweak";
        LOG("[JDPayTweak]: Starting HTTP service %s", serviceName);
        // 创建自定义配置
        http_service_config_t config = {
            .port = 33672,
            .bind_address = address,  // 绑定到所有接口
            .request_handler = request_handler,
            .service_name = serviceName
        };

        // 启动服务
        if (http_service_start(&config) == 0) {
            service_status = true;
            LOG("[JDPayTweak] HTTP service started");
            // 启动服务成功就需要开启防止休眠
            LOG("[JDPayTweak] Enabling no sleep");
            enable_no_sleep("KeepAlive");
        } else {
            LOG("[JDPayTweak] HTTP service start failed");
        }
        free(address);
    });

}