TARGET := iphone:clang:latest:13.0
INSTALL_TARGET_PROCESSES = SpringBoard
DEBUG = 0

include $(THEOS)/makefiles/common.mk

include ../../Core/Cryptor/Cryptor.mk
include ../../Core/Window/Window.mk

TWEAK_NAME = TestCryptor

TestCryptor_FILES = \
$(CRYPTOR_FILES) \
$(WINDOW_FILES) \
../../Core/Device/DeviceInfo.c \
../../Core/CJSON/cJSON.c \
../../Core/CJSON/JSONUtils.c \
../../Core/Services/DataCrypto/DataCrypto.c \
../../Core/Network/CryptoRequest/CryptoRequest.c \
../../Core/Network/SocketHTTP.c \
Tweak.c

TestCryptor_CFLAGS = -fobjc-arc
TestCryptor_CFLAGS += $(CRYPTOR_CFLAGS) \
$(WINDOW_CFLAGS) \
-I../../Core/Device/ \
-I../../Core/CJSON/ \
-I../../Core/Services/DataCrypto/ \
-I../../Core/Network/ \
-I../../Core/Network/CryptoRequest/ \
-I../../Core/Log/ \
-DDEBUG=$(DEBUG) \


include $(THEOS_MAKE_PATH)/tweak.mk
