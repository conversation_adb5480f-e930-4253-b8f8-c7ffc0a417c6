#include "Log.h"
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdio.h>
#include "CryptoRequest.h"
#include "JSONUtils.h"
#include "DeviceInfo.h"

/**
 * @brief 测试加密请求服务
 *
 * 使用新的CryptoRequestService模块发送加密请求并处理响应。
 * 展示了如何使用简化的API替代复杂的手动加密流程。
 */
__attribute__((constructor)) static void start_jdtools_service(void) {
    LOG("=== 开始测试加密请求服务 ===");

    // char* deviceInfo = deviceInfoInJson();
    // if (!deviceInfo) {
    //     LOG("Device info: %s", deviceInfo);
    //     free(deviceInfo);  // 直接释放，无需强制转换
    // }
    // LOG("设备信息: %s", deviceInfo);
    

    // 配置请求参数 
    // static const char* serverUrl = "http://**************:9000";
    CryptoRequestConfig config = {
        .request_data = "{\"action\":\"address\", \"serialNumber\": \"C39VHU30JCLF\"}"
    };

    LOG("发送加密请求到: %s", config.server_url);
    LOG("请求数据: %s", config.request_data);

    // 发送加密请求并获取解密后的响应
    CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

    // free(deviceInfo);

    // 处理结果
    if (result.success) {
        LOG("✅ 请求成功！");
        LOG("HTTP状态码: %d", result.http_status_code);
        // 处理解密后的JSON数据
        if (result.response_data) {
   
            // 发送加密请求并获取解密后的响应
            CryptoRequestResult result = crypto_request_send_and_decrypt(&config);
            if (!result.success) {
                crypto_request_free_result(&result);
                return;
            }
            if (!result.response_data) {
                crypto_request_free_result(&result);
                return;
            }

            // 检查业务状态码
            int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
            if (code == 0) {
                LOG("服务器返回状态码为0");
                crypto_request_free_result(&result);
                return;
            }
            if (code != 200) {
                const char* message = JSONUtils_GetStringValue(result.response_data, "msg");
                const char* error = message ? message : "未知错误";
                LOG("服务器响应错误 (code: %d): %s", code, error);
                crypto_request_free_result(&result);
                return;
            }

            // 获取data子对象
            cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
            if (!data) {
                LOG("响应中缺少data对象");
                crypto_request_free_result(&result);
                return;
            }

            // 从data对象中提取具体的业务数据
            bool status = JSONUtils_GetBoolValue(data, "status", false);
            if (!status) {
                LOG("业务处理失败，status为false");
                crypto_request_free_result(&result);
                return;
            }

            const char* addressText = JSONUtils_GetStringValue(data, "address");
            if (!addressText) {
                LOG("响应中缺少address字段");
                crypto_request_free_result(&result);
                return;
            }

            LOG("✅ 业务处理成功！");
            LOG("地址信息: %s", addressText);

            // 示例：如果data中还有其他字段，也可以提取
            const char* extra_info = JSONUtils_GetStringValue(data, "extra");
            if (extra_info) {
                LOG("额外信息: %s", extra_info);
            }

            // 示例：如果有数组数据
            cJSON* items = JSONUtils_GetArrayValue(data, "items");
            if (items) {
                int array_size = JSONUtils_GetArraySize(items);
                LOG("数组项目数量: %d", array_size);

                for (int i = 0; i < array_size; i++) {
                    cJSON* item = JSONUtils_GetArrayItem(items, i);
                    if (item) {
                        char* item_str = cJSON_Print(item);
                        if (item_str) {
                            LOG("项目[%d]: %s", i, item_str);
                            free(item_str);
                        }
                    }
                }
            }

           
        }

    } else {
        // + (void)show:(NSString *)message duration:(NSTimeInterval)duration;
        return;
        // LOG("❌ 请求失败！");
        // LOG("错误信息: %s",
        //        result.error_message ? result.error_message : "未知错误");

        // // 根据HTTP状态码进行不同的处理
        // switch (result.http_status_code) {
        //     case 200:
        //         LOG("HTTP状态正常，但加密/解密过程失败");
        //         break;
        //     case 400:
        //         LOG("请求参数错误 (HTTP 400)");
        //         break;
        //     case 401:
        //         LOG("认证失败 (HTTP 401)");
        //         break;
        //     case 403:
        //         LOG("权限不足 (HTTP 403)");
        //         break;
        //     case 404:
        //         LOG("接口不存在 (HTTP 404)");
        //         break;
        //     case 500:
        //         LOG("服务器内部错误 (HTTP 500)");
        //         break;
        //     case 0:
        //         LOG("网络连接失败");
        //         break;
        //     default:
        //         LOG("HTTP状态码: %d", result.http_status_code);
        //         break;
        // }

        // if (result.raw_response) {
        //     LOG("原始响应: %s", result.raw_response);
        // }
    }

    // 释放资源
    crypto_request_free_result(&result);

    LOG("=== 加密请求服务测试完成 ===");
}