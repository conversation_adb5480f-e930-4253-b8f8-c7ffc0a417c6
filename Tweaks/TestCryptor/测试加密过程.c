#include <syslog.h>
#include "Base64.h"
#include "Sha256.h"
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdio.h>

#include "StringCryptor.h"
#include "Signature.h"
#include "CryptoKey.h"
#include "ParamCrypto.h"
#include "DeviceInfo.h"

__attribute__((constructor)) static void start_jdtools_service(void) {
    // 读取设备信息
    char *deviceInfo = deviceInfoInJson();
    if (deviceInfo) {
        syslog(LOG_NOTICE, "[JDToolsService]: Device info: %s", deviceInfo);
        free(deviceInfo);  // 直接释放，无需强制转换
    }

    syslog(LOG_NOTICE, "[JDToolsService]: Starting JDToolsService");
    const uint8_t prognameKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x9F, 0xD2, 0xE7, 0xA7, 0x34, 0x48, 0xC5, 0xC4, 0x6C, 0xB5, 0x71, 0x53, 0xDA, 0xCD };
    char *progname = decrypt_string_auto(prognameKey, sizeof(prognameKey));
    if (!progname) {
        return;
    }
    syslog(LOG_NOTICE, "[JDToolsService]: current process name : %s", progname);
    if (strcmp(getprogname(), progname) != 0) {
        // 释放内存
        free(progname);
        syslog(LOG_NOTICE, "[JDToolsService]: Not the target process");
        return;
    }
    // 释放内存
    free(progname);

    // 进程正确，开始加载服务
    syslog(LOG_NOTICE, "[JDToolsService]: Starting JDToolsService");


    // ================ 测试签名 开始 ================
    // 固定的测试数据
    const char* data = "kdkfjalsdjflaksjdflajlfk";
    const char* key = "MyKey123";
    char* signature = get_secure_signature(data, key);
    if (signature) {
        syslog(LOG_NOTICE, "[JDToolsService]: Signature: %s", signature);
        free(signature);
    } else {
        syslog(LOG_NOTICE, "[JDToolsService]: Signature failed");
    }

    // 验证python签名
    // char python_sig[200];
    const char* p_data = "123123zxvasdfaf";
    const char* p_key = "adfzxcvasdfa#%";
    const char* p_sign = "tkmhEKQico7lBv9Nj3cRnUA+iWUhjJyOvv2OJqiB8jT0w04kCXDrL/tizLavMDm9VlPlUOwmSfpBzqxRHmjpMU5yhvJO9ezZ0zrGfUg5S0A=";

    bool py_valid = verify_secure_signature(p_data, p_key, p_sign);
    syslog(LOG_NOTICE, "[JDToolsService]: Verify Python Signature: %s", py_valid ? "✅ 验证成功" : "❌ 验证失败");

    // ================ 测试签名 结束 ================


    // ================ 本地加密 开始 ================
    const char* test_data2 = "Hello, World6a7465a67465w4664646123123%%#$!";
    const uint8_t test_keys[] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00};

    char* encrypted = msg_encryption_with_key((const uint8_t*)test_data2, strlen(test_data2), test_keys, sizeof(test_keys));
    if (encrypted) {
        syslog(LOG_NOTICE, "[JDToolsService]: Encrypted: %s", encrypted);
        free(encrypted);
    } else {
        syslog(LOG_NOTICE, "[JDToolsService]: Encryption failed");
    }

    const char* encrypted_str = "n/zJiYRCL9WvZ5M58xuYsQ==";

    size_t decrypted_len = 0;
    char* decrypted = msg_decryption_with_key(
        encrypted_str,
        &decrypted_len,
        test_keys,
        sizeof(test_keys)
    );

    if (decrypted) {
        syslog(LOG_NOTICE, "✅解密结果 %s", decrypted);
        free(decrypted);
    } else {
        syslog(LOG_NOTICE, "❌解密失败");
    }
    
    // ================ 本地msg加密 结束 ================

    // // ================ body加密 开始 ================

    // 测试数据
    const char* test_data = "Hello, World! This is a test message for encryption.";
    const char* hex_key = "8c42fe594933f0a1d820c455f70cd4dc74bfa7d87af98d0dfcbc05d6e7bc882b";
    
    // C版本加密
    char* encrypted_c = encrypt_data_base64_hex_key(
        (const uint8_t*)test_data, 
        strlen(test_data), 
        hex_key
    );
    
    if (!encrypted_c) {
        syslog(LOG_NOTICE, "❌ C版本加密失败");
        return;
    }
    syslog(LOG_NOTICE, "C版本加密结果: %s", encrypted_c);
    free(encrypted_c);

    
    // // C版本解密
    size_t decrypted_lens = 0;
    uint8_t* decrypted_c = decrypt_data_base64_hex_key(
        "LhbOs3SrStcuZVIUxE7tXg==", 
        &decrypted_lens, 
        hex_key
    );
    
    if (!decrypted_c) {
        syslog(LOG_NOTICE, "❌ C版本解密失败");
        return;
    }
    syslog(LOG_NOTICE, "✅解密结果 %s", decrypted_c);
    free(decrypted_c);

    
    // ================ body加密 结束 ================
}
