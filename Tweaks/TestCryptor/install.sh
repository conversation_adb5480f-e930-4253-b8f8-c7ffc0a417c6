#!/bin/bash

# 编译打包
build_package() {
    echo "清理本地packages目录..."
    rm -f ./packages/*.deb

    echo "开始编译打包..."
    if ! (make clean && make package); then
        echo "❌ 编译打包失败，终止执行！"
        exit 1
    fi

    package_path=$(ls -t ./packages/*.deb | head -1)
    if [ -z "$package_path" ]; then
        echo "❌ 没有找到打包后的deb文件，终止执行！"
        exit 1
    fi

    echo "✅ 打包完成: $package_path"
}

# 安装到设备
install_to_device() {
    local device_ip="$1"
    local kill_process="$2"
    local open_url="$3"
    local password="alpine"

    local package_name
    package_name=$(basename "$package_path")

    echo "📦 上传安装包到设备 $device_ip ..."
    sshpass -p "$password" scp "$package_path" root@"$device_ip":/tmp/ || {
        echo "❌ 上传失败"
        exit 1
    }

    echo "📦 开始远程安装..."
    sshpass -p "$password" ssh root@"$device_ip" "dpkg -i /tmp/$package_name" || {
        echo "❌ 安装失败"
        exit 1
    }

    echo "🧹 删除远程安装包..."
    sshpass -p "$password" ssh root@"$device_ip" "rm /tmp/$package_name"

    if [ -n "$kill_process" ]; then
        echo "🛑 杀死进程: $kill_process"
        sshpass -p "$password" ssh root@"$device_ip" "killall -9 $kill_process"
    fi

    echo "⏳ 等待0.2秒..."
    sshpass -p "$password" ssh root@"$device_ip" "sleep 0.2"

    if [ -n "$open_url" ]; then
        echo "🌐 打开URL Scheme: $open_url"
        sshpass -p "$password" ssh root@"$device_ip" "uiopen $open_url"
    fi

    echo "✅ 设备 $device_ip 安装完成！"
}

# ✨ 主逻辑
build_package

# 安装到多个设备
install_to_device "**************" "Passbook" "shoebox://"
# install_to_device "**************" "SpringBoard" 
# install_to_device "**************" "SpringBoard"
# install_to_device "***************" "SpringBoard"
# openapp.jdmobile://
#  "openapp.jdmobile://"