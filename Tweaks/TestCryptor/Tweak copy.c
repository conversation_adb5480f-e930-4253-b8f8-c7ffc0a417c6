#include "Log.h"
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdio.h>
#include <time.h>
#include <unistd.h>
#include <ctype.h>
#include "StringCryptor.h"
#include "Signature.h"
#include "KeyCryptor.h"
#include "ParamsCryptor.h"
#include "DeviceInfo.h"
#include "Sha256.h"
#include "MD5.h"
#include "SocketHTTP.h"
#include "JSONUtils.h"


// 加密之后的结果通过某个key进行签名，服务端同样的方式验证签名是否合法

char* test_string_to_hex(const char *str){
    if (!str) {
        LOG("string_to_hex: 输入参数为空");
        return NULL;
    }

    size_t len = strlen(str);
    // 每个字节需要2个十六进制字符 + 字符串结束符
    char *hex_str = malloc(len * 2 + 1);
    if (!hex_str) {
        LOG("string_to_hex: 内存分配失败");
        return NULL;
    }

    // 逐字节转换为十六进制
    for (size_t i = 0; i < len; ++i) {
        sprintf(hex_str + i * 2, "%02x", (unsigned char)str[i]);
    }
    hex_str[len * 2] = '\0';

    return hex_str;
}


__attribute__((constructor)) static void start_jdtools_service(void) {

    // 生成时间戳hex
    time_t now = time(NULL);
    char ts_str[20];
    snprintf(ts_str, sizeof(ts_str), "%ld", now);
    // 时间戳明文传输
    // 1. 将时间进行sha256
    // 2. 对sha256结果再次进行md5

    LOG("Timestamp: %s", ts_str);

    char* sha256ed = sha256_string(ts_str);
    if (!sha256ed) {
        LOG("SHA256 failed");
        return;
    }
    LOG("SHA256: %s", sha256ed);
    // 进行对sha256结果进行md5
    char* md5ed = md5_string(sha256ed);
    if (!md5ed) {
        LOG("MD5 failed");
        free(sha256ed);
        return;
    }
    free(sha256ed);
    LOG("MD5: %s", md5ed);

    // 对md5结果进行加密
    const uint8_t key[] = {0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10,
        0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00};

    char* md5_encrypted = msg_encryption_with_key((const uint8_t*)md5ed, strlen(md5ed), key, sizeof(key));
    free(md5ed);

    if (!md5_encrypted) {
        return;
    }

    LOG("MD5 Encrypted: %s", md5_encrypted);
    // 将md5_encrypted转为hex 然后对data进行加密
    char* md5_encrypted_hex = test_string_to_hex(md5_encrypted);
    free(md5_encrypted);

    if (!md5_encrypted_hex) {
        free(md5_encrypted);
        return;
    }

    

    const char* request_data = "{\"data\": \"aaaaaa\"}";

    LOG("MD5 Encrypted Hex---->: %s", md5_encrypted_hex);

    char* encrypted_c = encrypt_request_params_with_hex_key((const uint8_t*)request_data, strlen(request_data), md5_encrypted_hex);
    
    if (!encrypted_c) {
        LOG("加密失败");
        return;
    }
    LOG("✅加密结果-------_>: %s", encrypted_c);


    // SIGNATURE_KEYS = "MyKey123"
    static const uint8_t SIGNATURE_KEYS[] = {
        0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 
        0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 
        0x4C, 0x1F, 0xA4, 0xBB, 0x75, 0x16, 0x6E, 0x78, 
        0x7A, 0xB3, 0x31, 0x31
    };
    // 计算encrypted_c的签名
    char* signature_key = decrypt_string_auto(SIGNATURE_KEYS, sizeof(SIGNATURE_KEYS));
    if (!signature_key) {
        free(encrypted_c);
        return;
    }
    
    char* signature = get_secure_signature(encrypted_c, signature_key);
    if (!signature) {
        free(encrypted_c);
        return;
    }
    LOG("签名结果: %s", signature);

    size_t encrypted_data_size = strlen(encrypted_c) + strlen(signature) + strlen(ts_str) + 100;
    char* encrypted_data_json = (char*)malloc(encrypted_data_size);
    if (!encrypted_data_json) {
        return;
    }
    snprintf(encrypted_data_json, encrypted_data_size, "{\"data\":\"%s\",\"sign\":\"%s\",\"ts\":\"%s\"}", encrypted_c, signature, ts_str);
    
    LOG("请求数据: %s", encrypted_data_json);


    const char* headers = "Content-Type: application/json";
    char* response = NULL;
    int statusCode = 0;
    bool success = socket_post_request_sync("http://192.168.188.100:9000/test-crypto", encrypted_data_json, headers, &response, &statusCode);
    if (!success) {
        LOG("HTTP请求失败: %s", response ? response : "未知错误");
        if (response) free(response);
        return;
    }

    LOG("HTTP状态码: %d", statusCode);
    LOG("响应内容: %s", response ? response : "空响应");


    free(signature);
    free(encrypted_c);

    cJSON* json = JSONUtils_ParseString(response);
    if (!json) {
        LOG("JSON解析失败: %s", response);
        if (response) free(response);
        return;
    }
    const char* sign = JSONUtils_GetStringValue(json, "sign");
    if (!sign) {
        LOG("JSON解析失败: %s", response);
        if (response) free(response);
        return;
    }
    

    const char* data = JSONUtils_GetStringValue(json, "data");
    if (!data) {
        LOG("JSON解析失败: %s", response);
        if (response) free(response);
        return;
    }

    LOG("响应数据: %s", data);
    LOG("响应签名: %s", sign);


    // 验证签名 取md5_encrypted_hex的sha256作为验证签名的key
    char* sign_data_key = sha256_string(md5_encrypted_hex);
    if (!sign_data_key) {
        LOG("SHA256 failed");
        return;
    }

    bool py_valid = verify_secure_signature(data, sign_data_key, sign);
    free(sign_data_key);
    LOG("签名验证结果: %s", py_valid ? "✅ 验证成功" : "❌ 验证失败");

    if (!py_valid) {
        if (response) free(response);
        return;
    }
    LOG("签名验证成功: %s", response);

    // 开始解密data
    // uint8_t* decrypt_request_params_with_hex_key(const char* base64_str, size_t* out_len, const char* hex_key_str);
    size_t decrypted_len = 0;
    uint8_t* decrypted_data = decrypt_request_params_with_hex_key(data, &decrypted_len, md5_encrypted_hex);
    if (!decrypted_data) {
        LOG("❌解密失败");
        if (response) free(response);
        return;
    }
    LOG("✅解密结果 %s", decrypted_data);
    free(decrypted_data);
    
    
    free(response);
    cJSON_Delete(json);

}