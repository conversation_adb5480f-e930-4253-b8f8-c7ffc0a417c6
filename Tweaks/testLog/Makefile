TARGET := iphone:clang:latest:13.0
INSTALL_TARGET_PROCESSES = SpringBoard

DEBUG = 0

ifeq ($(DEBUG),1)
    testLog_CFLAGS += -DDEBUG=1
else
    testLog_CFLAGS += -DDEBUG=0
endif


include $(THEOS)/makefiles/common.mk

TWEAK_NAME = testLog

testLog_FILES = Tweak.xm
testLog_CFLAGS += -fobjc-arc

# testLog_CFLAGS += -include ./Prefix.pch
testLog_CFLAGS += -I../../Core/Log/


include $(THEOS_MAKE_PATH)/tweak.mk
