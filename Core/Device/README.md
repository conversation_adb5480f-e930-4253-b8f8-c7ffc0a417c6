# DeviceInfo Library

## 📱 iOS设备信息获取库

这是一个安全、高效的iOS设备信息获取库，支持获取序列号、UDID、IMEI、MAC地址等各种设备信息。

## ✨ 特性

- 🔐 **安全加密**: 所有敏感字符串都经过加密存储
- 🚀 **动态加载**: 运行时动态加载MobileGestalt库
- 📱 **全面信息**: 支持获取序列号、UDID、IMEI、MAC地址等
- 🛡️ **内存安全**: 自动内存管理，防止内存泄漏
- 📄 **JSON输出**: 支持将所有设备信息导出为JSON格式
- 🔧 **线程安全**: 所有函数都是线程安全的

## 🚀 快速开始

### 基本使用

```c
#include "DeviceInfo.h"
#include <stdio.h>
#include <stdlib.h>

int main() {
    // 获取单个设备信息
    char serialNumber[64];
    getSerialNumber(serialNumber, sizeof(serialNumber));
    printf("序列号: %s\n", serialNumber);
    
    // 获取完整JSON（推荐）
    char *deviceInfo = deviceInfoInJson();
    if (deviceInfo) {
        printf("设备信息: %s\n", deviceInfo);
        free(deviceInfo);  // 重要：必须释放内存
    }
    
    return 0;
}
```

### 编译

```bash
gcc -o test main.c DeviceInfo.c StringCryptor.c -framework CoreFoundation
```

## 📋 API参考

### 核心函数

| 函数 | 描述 | 缓冲区大小建议 |
|------|------|----------------|
| `getSerialNumber()` | 获取设备序列号 | 64字节 |
| `getUniqueDeviceID()` | 获取UDID | 64字节 |
| `getMLBSerialNumber()` | 获取MLB序列号 | 32字节 |
| `getIMEI()` | 获取IMEI号 | 32字节 |
| `getWifiAddress()` | 获取WiFi MAC地址 | 32字节 |
| `getBluetoothAddress()` | 获取蓝牙MAC地址 | 32字节 |
| `getProductType()` | 获取产品类型 | 32字节 |
| `getProductVersion()` | 获取系统版本 | 32字节 |
| `deviceInfoInJson()` | 获取完整JSON | 返回动态分配的字符串 |

### JSON输出格式

```json
{
  "SerialNumber": "XXXXXXXXXXXX",
  "MLBSerialNumber": "XXXXXXXXXXXX", 
  "UniqueDeviceID": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
  "InternationalMobileEquipmentIdentity": "XXXXXXXXXXXXXXX",
  "WiFiAddress": "XX:XX:XX:XX:XX:XX",
  "BluetoothAddress": "XX:XX:XX:XX:XX:XX",
  "ProductType": "iPhone14,2",
  "ProductVersion": "16.1.2"
}
```

## ⚠️ 重要注意事项

### 内存管理
- `deviceInfoInJson()` 返回的指针**必须**用 `free()` 释放
- 其他函数使用调用者提供的缓冲区，无需释放

### 错误处理
- 如果获取失败，缓冲区将被设置为 `"unknown"`
- `deviceInfoInJson()` 失败时返回 `NULL`

### 性能考虑
- 首次调用会动态加载库，可能稍慢
- 后续调用使用缓存，性能较好
- 建议在需要时才调用，避免不必要的开销

## 🔧 依赖关系

- **StringCryptor**: 用于字符串加密解密
- **CoreFoundation**: iOS系统框架
- **MobileGestalt**: iOS设备信息库

## 📱 兼容性

- **iOS版本**: 8.0+
- **设备类型**: 支持所有iOS设备
- **环境**: 兼容越狱和非越狱环境

## 🛡️ 安全特性

- 敏感字符串加密存储，防止静态分析
- 运行时动态解密，提高安全性
- 自动清理敏感数据，防止内存泄漏

## 📄 许可证

此库仅供学习和研究使用。
