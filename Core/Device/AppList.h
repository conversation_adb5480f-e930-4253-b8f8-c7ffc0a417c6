#import <Foundation/Foundation.h>

@interface AppList : NSObject

// 获取所有已安装的应用
+ (NSArray *)getAllInstalledApps;

@end

// NSArray *installedApps = [AppList getAllInstalledApps];
// // 京东应用的标识符（假设为 "com.jingdong.app.mall"）
// NSString *jdAppIdentifier = @"com.360buy.jdmobile";
// // 检查京东应用是否已安装
// BOOL isJDAppInstalled = [installedApps containsObject:jdAppIdentifier];
// if (!isJDAppInstalled) {
//     [Utils showToastWithMessage:@"未检测京东运行状态" displayDuration:2.0];
//     return;
// }