/**
 * @file DeviceInfo.c
 * @brief iOS设备信息获取库实现
 * @version 1.0
 * @date 2024
 *
 * @description
 * 这个文件实现了安全获取iOS设备各种硬件信息的功能。
 * 使用动态库加载和加密字符串技术，提高安全性和反静态分析能力。
 *
 * @implementation_details
 * - 使用 constructor/destructor 属性自动管理库的加载和卸载
 * - 所有敏感字符串（库路径、函数名、查询键）都经过加密存储
 * - 运行时动态解密，避免静态分析
 * - 使用 MobileGestalt 框架获取设备信息
 * - 自动内存管理，防止内存泄漏
 *
 * @security_features
 * - 加密存储敏感字符串
 * - 动态库加载，避免静态链接
 * - 运行时解密，防止静态分析
 * - 自动清理敏感数据
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dlfcn.h>
#include <CoreFoundation/CoreFoundation.h>
#include "StringCryptor_v2.h"

// ================================
// 类型定义和全局变量
// ================================

/**
 * @brief MobileGestalt库中MGCopyAnswer函数的类型定义
 *
 * 这个函数用于查询iOS设备的各种信息，是获取设备信息的核心函数。
 */
typedef CFPropertyListRef (*MGCopyAnswerFuncType)(CFStringRef);

/**
 * @brief MobileGestalt动态库句柄
 *
 * 存储动态加载的MobileGestalt库的句柄，用于后续的函数调用。
 * 在程序启动时自动加载，程序结束时自动卸载。
 */
static void *mgLib = NULL;

/**
 * @brief MGCopyAnswer函数指针
 *
 * 指向MobileGestalt库中的MGCopyAnswer函数，用于查询设备信息。
 * 在库加载成功后通过dlsym获取。
 */
static MGCopyAnswerFuncType MGCopyAnswerFunc = NULL;

// ================================
// 库初始化和清理函数
// ================================

/**
 * @brief 自动初始化MobileGestalt库
 *
 * 使用constructor属性，在程序启动时自动调用。
 * 动态加载MobileGestalt库并获取MGCopyAnswer函数指针。
 *
 * @note 所有敏感字符串都经过加密存储，运行时解密
 * @note 如果加载失败，后续的设备信息查询将返回空值
 *
 * @security
 * - 库路径加密存储: /usr/lib/libMobileGestalt.dylib
 * - 函数名加密存储: MGCopyAnswer
 */
__attribute__((constructor)) static void initializeMG() {
    if (!mgLib) {
        // /usr/lib/libMobileGestalt.dylib
        static const uint8_t pathKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x79,0x3C,0x61,0xCF,0x80,0x73,0x2E,0xEA,0xD8,0x36,0x74,0x43,0x2A,0x88,0x5C,0x65,0x91,0x40,0xCD,0x6E,0xEF,0xA8,0xD7,0x0D,0xEB,0x31,0x5B,0x5C,0x49,0x6A,0xAA,0x5A,0x07,0xA5,0x5A};
        char *libPath = decrypt_string_v2(pathKey, sizeof(pathKey));
        if (!libPath) {
            free(libPath);
            return;
        }
        
        mgLib = dlopen(libPath, RTLD_LAZY);
        if (mgLib) {
            // MGCopyAnswer
            static const uint8_t nameKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x4C,0xCE,0xE9,0xEF,0x88,0xDB,0xE7,0x06,0xF0,0x4C,0x53,0xC1,0xEF,0xDB,0xCC,0xCE};
            char *key = decrypt_string_v2(nameKey, sizeof(nameKey));
            if (!key) {
                free(key);
                return;
            }
            MGCopyAnswerFunc = (MGCopyAnswerFuncType)dlsym(mgLib, key);
            free(key);
        }
        free(libPath);
    }
}

/**
 * @brief 自动清理MobileGestalt库
 *
 * 使用destructor属性，在程序结束时自动调用。
 * 卸载动态加载的库并清理相关资源。
 *
 * @note 确保程序结束时正确释放资源，防止内存泄漏
 */
__attribute__((destructor)) static void cleanupMG() {
    if (mgLib) {
        dlclose(mgLib);
        mgLib = NULL;
        MGCopyAnswerFunc = NULL;
    }
}

// ================================
// 核心查询函数
// ================================

/**
 * @brief 通用的MobileGestalt查询函数
 *
 * 这是所有设备信息查询的核心函数，通过MGCopyAnswer查询指定的设备信息。
 *
 * @param key 查询键，用于指定要获取的设备信息类型
 * @param outBuf 输出缓冲区，用于存储查询结果
 * @param bufSize 输出缓冲区大小
 *
 * @note 如果查询失败，输出缓冲区将被设置为"unknown"
 * @note 自动处理CoreFoundation对象的内存管理
 * @note 线程安全，可并发调用
 *
 * @implementation
 * 1. 参数验证
 * 2. 检查MGCopyAnswer函数是否可用
 * 3. 创建CFString查询键
 * 4. 调用MGCopyAnswer查询
 * 5. 转换结果为C字符串
 * 6. 自动释放CoreFoundation对象
 */
static void getMGCopyAnswer(const char *key, char *outBuf, size_t bufSize) {
    if (!outBuf || bufSize == 0) return;
    memset(outBuf, 0, bufSize);

    if (!MGCopyAnswerFunc) {
        strncpy(outBuf, "unknown", bufSize - 1);
        return;
    }
    
    CFStringRef keyRef = CFStringCreateWithCString(NULL, key, kCFStringEncodingUTF8);
    if (!keyRef) {
        strncpy(outBuf, "unknown", bufSize - 1);
        return;
    }
    
    CFPropertyListRef value = MGCopyAnswerFunc(keyRef);
    CFRelease(keyRef);

    if (!value) {
        strncpy(outBuf, "unknown", bufSize - 1);
        return;
    }
    
    if (CFGetTypeID(value) == CFStringGetTypeID()) {
        CFStringGetCString((CFStringRef)value, outBuf, bufSize, kCFStringEncodingUTF8);
        CFRelease(value);
    } else {
        CFRelease(value);
        strncpy(outBuf, "unknown", bufSize - 1);
    }
}

void getSerialNumber(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // SerialNumber
    static const uint8_t serialNumberKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x5C, 0xBE, 0x71, 0x96, 0x37, 0x78, 0x05, 0x40, 0xE4, 0xE5, 0x59, 0xC1, 0x4E, 0x17, 0xA1, 0x5E };
    char *key = decrypt_string_v2(serialNumberKey, sizeof(serialNumberKey));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
   
}

void getUniqueDeviceID(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // UniqueDeviceID
    static const uint8_t uniqueDeviceID[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x8C, 0x7B, 0xB5, 0xDA, 0x53, 0xB6, 0x39, 0xC3, 0xA4, 0x59, 0xA2, 0x26, 0xCC, 0x4F, 0xD6, 0xA0, 0x8E, 0x7A };
    char *key = decrypt_string_v2(uniqueDeviceID, sizeof(uniqueDeviceID));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}

void getMLBSerialNumber(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // MLBSerialNumber
    static const uint8_t MLBSerialNumber[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x4C, 0x66, 0xFB, 0xCF, 0xD2, 0x26, 0x2B, 0xE2, 0xF6, 0x05, 0xD7, 0xE1, 0x9B, 0x33, 0xD7, 0x3E, 0x41, 0x87, 0x78 };
    char *key = decrypt_string_v2(MLBSerialNumber, sizeof(MLBSerialNumber));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}

void getIMEI(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // InternationalMobileEquipmentIdentity
    static const uint8_t IMEI[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x6C, 0x75, 0x6D, 0xB7, 0xBD, 0x00, 0xE9, 0x76, 0xC7, 0x0F, 0x1F, 0x4D, 0x21, 0x79, 0x3B, 0x2B, 0xBD, 0x0A, 0xDE, 0x6E, 0xFF, 0xA1, 0x97, 0xA9, 0xB9, 0xEE, 0x86, 0xA9, 0x2D, 0x54, 0xA1, 0x5F, 0xE2, 0xB5, 0x7A, 0x0D, 0x5F, 0x18, 0xBF, 0x70 };
    char *key = decrypt_string_v2(IMEI, sizeof(IMEI));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}

void getWifiAddress(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // WifiAddress
    static const uint8_t WifiAddress[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0x90, 0x13, 0x90, 0x34, 0xB8, 0x3A, 0x6B, 0x26, 0x61, 0x21, 0x68, 0x3E, 0xAE, 0xAF };
    char *key = decrypt_string_v2(WifiAddress, sizeof(WifiAddress));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}

void getBluetoothAddress(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // BluetoothAddress
    static const uint8_t BluetoothAddress[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xD4, 0x6E, 0x94, 0xB8, 0xAD, 0x09, 0x19, 0x79, 0xDF, 0x1F, 0xAE, 0x1E, 0x14, 0x3B, 0xDF, 0xBD, 0xE6, 0x12, 0x0C, 0xF1 };
    char *key = decrypt_string_v2(BluetoothAddress, sizeof(BluetoothAddress));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}

void getProductType(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // ProductType
    static const uint8_t ProductType[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA4, 0x59, 0xC7, 0xB5, 0x55, 0xA6, 0xBB, 0x72, 0x47, 0x9F, 0x5E, 0x31, 0xE0, 0x29, 0x25 };
    char *key = decrypt_string_v2(ProductType, sizeof(ProductType));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}

void getProductVersion(char *buffer, size_t bufferSize) {
    if (!buffer || bufferSize == 0) return;
    // ProductVersion
    
    static const uint8_t ProductVersion[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA4, 0x59, 0xC7, 0xB5, 0x55, 0xA6, 0xBB, 0x42, 0x24, 0x69, 0x21, 0x0E, 0x3D, 0x91, 0x4B, 0x97, 0x24, 0xDB };
    char *key = decrypt_string_v2(ProductVersion, sizeof(ProductVersion));
    if (!key) {
        return;
    }
    getMGCopyAnswer(key, buffer, bufferSize);
    free(key);
}



char* deviceInfoInJson() {
    // 序列号
    char serialNumber[32];
    getSerialNumber(serialNumber, sizeof(serialNumber));

    // UDID
    char uniqueDeviceID[41];
    getUniqueDeviceID(uniqueDeviceID, sizeof(uniqueDeviceID));

    // 主板序列号
    char mlbSerialNumber[20];
    getMLBSerialNumber(mlbSerialNumber, sizeof(mlbSerialNumber));

    // IMEI
    char imei[20];
    getIMEI(imei, sizeof(imei));

    // WiFi MAC地址
    char wifiAddress[20];
    getWifiAddress(wifiAddress, sizeof(wifiAddress));

    // 蓝牙MAC地址
    char bluetoothAddress[20];
    getBluetoothAddress(bluetoothAddress, sizeof(bluetoothAddress));

    // 设备型号 iPhone10,3
    char productType[16];
    getProductType(productType, sizeof(productType));

    // 设备版本 14.8
    char productVersion[8];
    getProductVersion(productVersion, sizeof(productVersion));
    
    // 使用 snprintf 拼接设备信息 JSON 字符串
    char* deviceInfoJsonStr = (char*)malloc(512);
    if (deviceInfoJsonStr == NULL) {
        return NULL;
    }

    // serialNumber
    static const uint8_t serialNumberKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x5F, 0xBE, 0x71, 0x96, 0x37, 0x78, 0x05, 0x40, 0xE4, 0xE5, 0x59, 0xC1, 0x4B, 0x33, 0x80, 0x87 };
    char *serialNumberStr = decrypt_string_v2(serialNumberKey, sizeof(serialNumberKey));
    if (!serialNumberStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }


    static const uint8_t mlbSerialNumberKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x4F, 0x67, 0xFC, 0xCF, 0xD2, 0x26, 0x2B, 0xE2, 0xF6, 0x05, 0xD7, 0xE1, 0x9B, 0x33, 0xD7, 0x39, 0x89, 0x48, 0xCB };
    char *mlbSerialNumberStr = decrypt_string_v2(mlbSerialNumberKey, sizeof(mlbSerialNumberKey));
    if (!mlbSerialNumberStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }
    
    static const uint8_t imeiKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x6F, 0x7D, 0x15, 0x90, 0xCE, 0x7B, 0x4D, 0x4D };
    char *imeiStr = decrypt_string_v2(imeiKey, sizeof(imeiKey));
    if (!imeiStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }

    
    static const uint8_t wifiAddressKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBF, 0x90, 0x13, 0x90, 0x34, 0xB8, 0x3A, 0x6B, 0x26, 0x61, 0x21, 0x75, 0x5D, 0xD0, 0xCB };
    char *wifiAddressStr = decrypt_string_v2(wifiAddressKey, sizeof(wifiAddressKey));
    if (!wifiAddressStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }


    static const uint8_t bluetoothAddressKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xD7, 0x6E, 0x94, 0xB8, 0xAD, 0x09, 0x19, 0x79, 0xDF, 0x1F, 0xAE, 0x1E, 0x14, 0x3B, 0xDF, 0xBD, 0xE3, 0x42, 0x59, 0x29 };
    char *bluetoothAddressStr = decrypt_string_v2(bluetoothAddressKey, sizeof(bluetoothAddressKey));
    if (!bluetoothAddressStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }

    static const uint8_t productTypeKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA7, 0x59, 0xC7, 0xB5, 0x55, 0xA6, 0xBB, 0x72, 0x47, 0x9F, 0x5E, 0x3E, 0x0A, 0xCC, 0x41 };
    char *productTypeStr = decrypt_string_v2(productTypeKey, sizeof(productTypeKey));
    if (!productTypeStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }

    static const uint8_t productVersionKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xA7, 0x59, 0xC7, 0xB5, 0x55, 0xA6, 0xBB, 0x42, 0x24, 0x69, 0x21, 0x0E, 0x3D, 0x91, 0x48, 0xBD, 0x0D, 0x09 };
    char *productVersionStr = decrypt_string_v2(productVersionKey, sizeof(productVersionKey));
    if (!productVersionStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }

    static const uint8_t uniqueDeviceIDKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x8F, 0x7B, 0xB5, 0xDA, 0x53, 0xB6, 0x39, 0xC3, 0xA4, 0x59, 0xA2, 0x26, 0xCC, 0x4F, 0xD3, 0xD6, 0xFD, 0xAD };
    char *uniqueDeviceIDStr = decrypt_string_v2(uniqueDeviceIDKey, sizeof(uniqueDeviceIDKey));
    if (!uniqueDeviceIDStr) {
        free(deviceInfoJsonStr);
        return NULL;
    }

    snprintf(deviceInfoJsonStr, 512, 
        "{\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\","
        "\"%s\":\"%s\",\"%s\":\"%s\","
        "\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\"}",
        serialNumberStr, serialNumber, 
        mlbSerialNumberStr, mlbSerialNumber, 
        uniqueDeviceIDStr, uniqueDeviceID, 
        imeiStr, imei, 
        wifiAddressStr, wifiAddress, 
        bluetoothAddressStr, bluetoothAddress, 
        productTypeStr, productType, 
        productVersionStr, productVersion
    );
    free(serialNumberStr);
    free(mlbSerialNumberStr);
    free(uniqueDeviceIDStr);
    free(imeiStr);
    free(wifiAddressStr);
    free(bluetoothAddressStr);
    free(productTypeStr);
    free(productVersionStr);
    
    return deviceInfoJsonStr;
}


// ================================
// 使用示例和最佳实践
// ================================

/*
 * 📋 完整使用示例:
 *
 * #include "DeviceInfo.h"
 * #include <stdio.h>
 * #include <stdlib.h>
 *
 * int main() {
 *     // 方式1: 获取单个设备信息
 *     char serialNumber[64];
 *     getSerialNumber(serialNumber, sizeof(serialNumber));
 *     printf("序列号: %s\n", serialNumber);
 *
 *     char udid[64];
 *     getUniqueDeviceID(udid, sizeof(udid));
 *     printf("UDID: %s\n", udid);
 *
 *     // 方式2: 获取完整JSON（推荐）
 *     char *deviceInfo = deviceInfoInJson();
 *     if (deviceInfo) {
 *         printf("完整设备信息: %s\n", deviceInfo);
 *         free(deviceInfo);  // 重要：必须释放内存
 *     } else {
 *         printf("获取设备信息失败\n");
 *     }
 *
 *     return 0;
 * }
 *
 * 🔧 编译命令:
 * gcc -o test main.c DeviceInfo.c StringCryptor.c -framework CoreFoundation
 *
 * ⚠️  重要注意事项:
 *
 * 1. 内存管理:
 *    - deviceInfoInJson() 返回的指针必须用 free() 释放
 *    - 忘记释放会导致内存泄漏
 *    - 其他函数使用调用者缓冲区，无需释放
 *
 * 2. 错误处理:
 *    - 所有函数都有内置的错误处理
 *    - 失败时返回"unknown"或NULL
 *    - 建议检查返回值
 *
 * 3. 性能优化:
 *    - 库在首次使用时自动加载
 *    - 后续调用使用缓存，性能较好
 *    - 避免频繁调用，建议缓存结果
 *
 * 4. 线程安全:
 *    - 所有公共函数都是线程安全的
 *    - 可以在多线程环境中安全使用
 *
 * 5. 依赖关系:
 *    - 依赖 StringCryptor 库进行字符串解密
 *    - 需要 CoreFoundation 框架
 *    - 需要访问 MobileGestalt 库
 *
 * 6. 兼容性:
 *    - 支持 iOS 8.0+
 *    - 在越狱和非越狱环境中都可以工作
 *    - 某些信息在沙盒环境中可能受限
 */