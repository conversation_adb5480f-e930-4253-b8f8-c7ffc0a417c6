#import "DeviceControl.h"
#import <UIKit/UIKit.h>
#import <objc/message.h>
#import <SpringBoard/SpringBoard.h>
// #import <FrontBoardServices/FrontBoardServices.h>
#include <sys/sysctl.h>
#include <unistd.h>
#import "Log.h"
#import <CoreTelephony/CTTelephonyNetworkInfo.h>

// ============ 飞行模式 ============
@interface RadiosPreferences : NSObject
- (BOOL)airplaneMode;
- (void)setAirplaneMode:(BOOL)enabled;
@end
// ============ 飞行模式 ============

@interface FBSystemService : NSObject
+ (id)sharedInstance;
- (void)exitAndRelaunch:(BOOL)arg1;
- (void)shutdownAndReboot:(BOOL)arg1;
@end

@interface SBLockScreenManager : NSObject
+ (id)sharedInstance;
- (void)unlockUIFromSource:(int)source withOptions:(id)options;
- (BOOL)isUILocked;
@end

// @interface SpringBoard : UIApplication
// - (void)_simulateHomeButtonPress;
// @end

@interface SBBacklightController : NSObject
+ (instancetype)sharedInstance;
- (BOOL)screenIsOn;
@end

@interface SBLockStateAggregator : NSObject
+ (instancetype)sharedInstance;
- (BOOL)isPasscodeLocked;   // 是否上锁（需要密码）
- (BOOL)hasAnyLockState;    // 是否仍然处于锁屏状态
@end

// 判断某个应用是否在运行
@interface SBApplicationController : NSObject
+ (instancetype)sharedInstance;
- (id)applicationWithBundleIdentifier:(NSString *)bundleID;
@end

@interface SBApplication : NSObject
- (BOOL)isRunning;
- (BOOL)isForeground;
@end

@implementation DeviceControl

// ============ 飞行模式 ============
+ (void)toggleAirplaneMode:(BOOL)enable {
    RadiosPreferences *prefs = [[RadiosPreferences alloc] init];
    [prefs setAirplaneMode:enable];
}


+ (void)respring {
    @try {
        dispatch_async(dispatch_get_main_queue(), ^{
            // 使用 FBSystemService 重启 SpringBoard
            Class FBSystemServiceClass = NSClassFromString(@"FBSystemService");
            if (FBSystemServiceClass) {
                id service = [FBSystemServiceClass sharedInstance];
                if ([service respondsToSelector:@selector(exitAndRelaunch:)]) {
                    [service exitAndRelaunch:YES];
                }
            }
        });
    } @catch (NSException *exception) {
        NSLOG(@"注销设备失败: %@", exception);
    }
}

+ (void)reboot {
    @try {
        dispatch_async(dispatch_get_main_queue(), ^{
            Class FBSystemServiceClass = NSClassFromString(@"FBSystemService");
            if (FBSystemServiceClass) {
                id service = [FBSystemServiceClass sharedInstance];
                if ([service respondsToSelector:@selector(shutdownAndReboot:)]) {
                    [service shutdownAndReboot:YES];
                }
            }
        });
    } @catch (NSException *exception) {
        NSLOG(@"重启设备失败: %@", exception);
    }
}

+ (BOOL)isScreenOn {
    @try {
        // 获取 SBBacklightController 的单例对象
        Class SBBacklightControllerClass = NSClassFromString(@"SBBacklightController");
        if (SBBacklightControllerClass) {
            id backlightController = [SBBacklightControllerClass performSelector:@selector(sharedInstance)];
            if (backlightController && [backlightController respondsToSelector:@selector(screenIsOn)]) {
                BOOL isOn = ((BOOL (*)(id, SEL))objc_msgSend)(backlightController, @selector(screenIsOn));
                return isOn;
            }
        }
    } @catch (NSException *exception) {
        return NO;
    }

    return YES;
    
}

// 检测设备是否已解锁
+ (BOOL) isDeviceUnlocked {
    @try {
        SBLockStateAggregator *lockState = [NSClassFromString(@"SBLockStateAggregator") sharedInstance];
        if (!lockState) {
            return NO;
        }
        
        // 尝试使用 hasAnyLockState 方法替代 isPasscodeLocked
        if ([lockState respondsToSelector:@selector(hasAnyLockState)]) {
            BOOL locked = [lockState hasAnyLockState];
            return !locked;
        }
        
        // 如果 hasAnyLockState 也不存在，尝试使用 SBLockScreenManager 作为备选方案
        SBLockScreenManager *lockScreenManager = [NSClassFromString(@"SBLockScreenManager") sharedInstance];
        if (lockScreenManager && [lockScreenManager respondsToSelector:@selector(isUILocked)]) {
            BOOL locked = [lockScreenManager isUILocked];
            return !locked;
        }
        
        return NO;
    } @catch (NSException *exception) {
        return NO;
    }
}

// 修改 wakeUp 方法，添加完成回调
+ (void)wakeUpWithCompletion:(void(^)(void))completion {
    @try {
        dispatch_async(dispatch_get_main_queue(), ^{
            // 初始化 UIApplication 对象
            UIApplication *app = [UIApplication sharedApplication];

            // 检查屏幕状态和设备锁定状态
            BOOL isScreenOn = [self isScreenOn];
            BOOL isUnlocked = [self isDeviceUnlocked];
            
            // NSLOG(@"[WakeUpScreen] 当前状态: 屏幕%@, 设备%@", 
            //       isScreenOn ? @"已点亮" : @"已熄灭", 
            //       isUnlocked ? @"已解锁" : @"已锁定");
            
            // 如果设备已经点亮且已解锁，不需要执行任何操作
            if (isScreenOn && isUnlocked) {
                // NSLOG(@"[WakeUpScreen] 设备已点亮且已解锁，无需执行任何操作");
                // NSLOG(@"[WakeUpScreen] 唤醒解锁流程完成-----------------");
                if (completion) {
                    completion();
                }
                return;
            }
            
            // 根据不同状态执行不同操作
            if (!isScreenOn) {
                // 屏幕未点亮，先发送唤醒通知
                // NSLOG(@"[WakeUpScreen] 屏幕未点亮，尝试唤醒屏幕");
                [[NSNotificationCenter defaultCenter] postNotificationName:@"SBBacklightResetNotification" object:nil];
                
                // 给屏幕点亮一些时间
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    // 继续解锁流程，传递已知状态和完成回调
                    [self continueUnlockProcess:app isUnlocked:NO completion:completion];
                });
            } else if (!isUnlocked) {
                // 屏幕已点亮但设备锁定，直接执行解锁流程
                // NSLOG(@"[WakeUpScreen] 屏幕已点亮但设备锁定，执行解锁流程");
                [self continueUnlockProcess:app isUnlocked:NO completion:completion];
            } else {
                // NSLOG(@"[WakeUpScreen] 唤醒解锁流程完成-----------------");
                if (completion) {
                    completion();
                }
            }
        });
    } @catch (NSException *exception) {
        // NSLOG(@"[WakeUpScreen] 唤醒解锁失败: %@", exception);
        if (completion) {
            completion();
        }
    }
}

// 保留原来的 wakeUp 方法，但内部调用新方法
+ (void)wakeUp {
    [self wakeUpWithCompletion:nil];
}

// 修改 continueUnlockProcess 方法，添加完成回调
+ (void)continueUnlockProcess:(UIApplication *)app isUnlocked:(BOOL)isUnlocked completion:(void(^)(void))completion {
    // 如果设备已解锁，只需模拟一次Home键
    if (isUnlocked) {
        // NSLOG(@"[WakeUpScreen] 设备已解锁，只需模拟一次Home键");
        [self simulateHomeButtonPress];
        if (completion) {
            completion();
        }
        return;
    }
    
    // 设备未解锁，尝试解锁
    Class lockScreenClass = NSClassFromString(@"SBLockScreenManager");
    if (lockScreenClass) {
        id lockScreenManager = [lockScreenClass sharedInstance];
        if ([lockScreenManager respondsToSelector:@selector(isUILocked)] && 
            [lockScreenManager respondsToSelector:@selector(unlockUIFromSource:withOptions:)]) {
            
            // 检查是否锁屏
            BOOL isLocked = [lockScreenManager isUILocked];
            // NSLOG(@"[WakeUpScreen] 当前锁屏状态: %@", isLocked ? @"已锁定" : @"未锁定");
            
            if (isLocked) {
                // 尝试使用不同的解锁源
                // NSLOG(@"[WakeUpScreen] 尝试解锁设备");
                [lockScreenManager unlockUIFromSource:1 withOptions:nil];
                
                // 再次尝试解锁，使用不同的参数
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [lockScreenManager unlockUIFromSource:2 withOptions:nil];
                    
                    // 解锁后等待一段时间，然后模拟Home键
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [self simulateDoubleHomeButtonPress:app];
                        
                        // 等待操作完成后调用回调
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            // NSLOG(@"[WakeUpScreen] 唤醒解锁流程完成-----------------");
                            if (completion) {
                                completion();
                            }
                        });
                    });
                });
                return;
            }
        }
    }
    
    // 如果无法使用SBLockScreenManager或设备未锁定，直接模拟Home键
    [self simulateDoubleHomeButtonPress:app];
    
    // 等待操作完成后调用回调
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // NSLOG(@"[WakeUpScreen] 唤醒解锁流程完成-----------------");
        if (completion) {
            completion();
        }
    });
}

// 新增方法：模拟双击Home键
+ (void)simulateDoubleHomeButtonPress:(UIApplication *)app {
    SpringBoard *springBoard = (SpringBoard *)[UIApplication sharedApplication];
    if ([springBoard respondsToSelector:@selector(_simulateHomeButtonPress)]) {
        // NSLOG(@"[WakeUpScreen] 第一次模拟Home键");
        [springBoard _simulateHomeButtonPress];
        
        // 再次模拟Home键，确保进入桌面
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // NSLOG(@"[WakeUpScreen] 第二次模拟Home键");
            [springBoard _simulateHomeButtonPress];
            
            // 保持设备唤醒状态一段时间，然后再恢复自动锁屏
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [app setIdleTimerDisabled:NO];
                // NSLOG(@"[WakeUpScreen] 恢复自动锁屏设置");
            });
        });
    }
}

// 新增方法：只模拟Home键
+ (void)simulateHomeButtonPress {
    dispatch_async(dispatch_get_main_queue(), ^{
        SpringBoard *springBoard = (SpringBoard *)[UIApplication sharedApplication];
        if ([springBoard respondsToSelector:@selector(_simulateHomeButtonPress)]) {
            // NSLOG(@"[WakeUpScreen] 模拟Home键返回桌面");
            [springBoard _simulateHomeButtonPress];
        }
    });
}


@end