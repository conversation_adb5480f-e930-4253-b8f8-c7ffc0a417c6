#import "FindDeviceAudio.h"
#import <math.h>
#import <MediaPlayer/MediaPlayer.h>
#import "Log.h"

@interface FindDeviceAudio ()

@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic, assign) float originalVolume; // 添加属性保存原始音量

@end

@implementation FindDeviceAudio

+ (instancetype)sharedInstance {
    static FindDeviceAudio *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)createFindDeviceAudioFile {
    NSLOG(@"[FindDeviceAudio] 创建查找设备音频文件");
    
    // 创建临时文件路径
    NSString *tempDir = NSTemporaryDirectory();
    NSString *findDeviceAudioPath = [tempDir stringByAppendingPathComponent:@"findDevice.wav"];
    
    // 检查文件是否已存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:findDeviceAudioPath]) {
        NSLOG(@"[FindDeviceAudio] 查找设备音频文件已存在");
        return;
    }
    
    @try {
        // WAV文件参数
        int sampleRate = 44100;
        int numChannels = 1;
        int bitsPerSample = 16;
        int numSamples = sampleRate * 2; // 2秒音频
        
        // 创建WAV文件头
        char header[44];
        memset(header, 0, sizeof(header));
        
        // RIFF块
        memcpy(header, "RIFF", 4);
        *(uint32_t *)(header + 4) = 36 + numSamples * numChannels * bitsPerSample / 8;
        memcpy(header + 8, "WAVE", 4);
        
        // fmt子块
        memcpy(header + 12, "fmt ", 4);
        *(uint32_t *)(header + 16) = 16; // fmt块大小
        *(uint16_t *)(header + 20) = 1; // PCM格式
        *(uint16_t *)(header + 22) = numChannels;
        *(uint32_t *)(header + 24) = sampleRate;
        *(uint32_t *)(header + 28) = sampleRate * numChannels * bitsPerSample / 8; // 字节率
        *(uint16_t *)(header + 32) = numChannels * bitsPerSample / 8; // 块对齐
        *(uint16_t *)(header + 34) = bitsPerSample;
        
        // data子块
        memcpy(header + 36, "data", 4);
        *(uint32_t *)(header + 40) = numSamples * numChannels * bitsPerSample / 8;
        
        // 创建音频数据
        NSMutableData *audioData = [NSMutableData dataWithBytes:header length:44];
        
        // 创建一个2秒的音频数据（蜂鸣声）
        int16_t *samples = (int16_t *)malloc(numSamples * numChannels * sizeof(int16_t));
        if (samples) {
            // 生成蜂鸣声（交替高低音调）
            float frequency1 = 1000.0; // 1000Hz
            float frequency2 = 1500.0; // 1500Hz
            float amplitude = 16000.0; // 音量
            
            for (int i = 0; i < numSamples; i++) {
                float time = (float)i / sampleRate;
                float frequency = (i / (sampleRate / 4)) % 2 == 0 ? frequency1 : frequency2; // 每0.25秒切换频率
                samples[i] = (int16_t)(amplitude * sin(2.0 * M_PI * frequency * time));
            }
            
            [audioData appendBytes:samples length:numSamples * numChannels * sizeof(int16_t)];
            free(samples);
            
            // 写入文件
            BOOL success = [audioData writeToFile:findDeviceAudioPath atomically:YES];
            if (success) {
                NSLOG(@"[FindDeviceAudio] 查找设备音频文件创建成功: %@", findDeviceAudioPath);
            } else {
                NSLOG(@"[FindDeviceAudio] 查找设备音频文件创建失败");
            }
        } else {
            NSLOG(@"[FindDeviceAudio] 内存分配失败");
        }
    } @catch (NSException *exception) {
        NSLOG(@"[FindDeviceAudio] 创建音频文件时发生异常: %@", exception.reason);
    }
}

- (void)playFindDeviceSound {
    NSLOG(@"[FindDeviceAudio] 开始播放查找设备音频");
    
    @try {
        // 设置音频会话
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        NSError *sessionError = nil;
        
        // 设置音频会话类别
        if (![audioSession setCategory:AVAudioSessionCategoryPlayback
                           withOptions:AVAudioSessionCategoryOptionMixWithOthers
                                 error:&sessionError]) {
            NSLOG(@"[FindDeviceAudio] 设置音频会话类别失败: %@", sessionError.localizedDescription);
            // 继续执行，不要因为这个错误而中断
        }
        
        // 激活音频会话
        if (![audioSession setActive:YES error:&sessionError]) {
            NSLOG(@"[FindDeviceAudio] 激活音频会话失败: %@", sessionError.localizedDescription);
            // 继续执行，不要因为这个错误而中断
        }
        
        // 保存当前系统音量
        self.originalVolume = audioSession.outputVolume;
        NSLOG(@"[FindDeviceAudio] 保存原始音量: %.2f", self.originalVolume);
        
        NSString *tempDir = NSTemporaryDirectory();
        NSString *findDeviceAudioPath = [tempDir stringByAppendingPathComponent:@"findDevice.wav"];
        
        if (![[NSFileManager defaultManager] fileExistsAtPath:findDeviceAudioPath]) {
            NSLOG(@"[FindDeviceAudio] 查找设备音频文件不存在，创建中...");
            [self createFindDeviceAudioFile];
            
            // 再次检查文件是否创建成功
            if (![[NSFileManager defaultManager] fileExistsAtPath:findDeviceAudioPath]) {
                NSLOG(@"[FindDeviceAudio] 创建音频文件失败，无法播放");
                return;
            }
        }
        
        // 停止当前播放的音频
        [self stopFindDeviceSound];
        
        // 设置系统音量为50%
        [self setSystemVolume:0.5];
        
        NSError *error = nil;
        self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:[NSURL fileURLWithPath:findDeviceAudioPath] error:&error];
        
        if (error) {
            NSLOG(@"[FindDeviceAudio] 创建查找设备音频播放器失败: %@", error.localizedDescription);
            // 恢复原始音量
            [self setSystemVolume:self.originalVolume];
            return;
        }
        
        // 设置播放完成回调
        self.audioPlayer.delegate = self;
        
        self.audioPlayer.numberOfLoops = 0; // 只播放一次
        self.audioPlayer.volume = 1.0; // 最大音量
        
        if ([self.audioPlayer play]) {
            NSLOG(@"[FindDeviceAudio] 查找设备音频开始播放");
        } else {
            NSLOG(@"[FindDeviceAudio] 查找设备音频播放失败");
            // 恢复原始音量
            [self setSystemVolume:self.originalVolume];
        }
    } @catch (NSException *exception) {
        NSLOG(@"[FindDeviceAudio] 播放音频时发生异常: %@", exception.reason);
        // 尝试恢复音量
        @try {
            [self setSystemVolume:self.originalVolume];
        } @catch (NSException *e) {
            NSLOG(@"[FindDeviceAudio] 恢复音量时发生异常: %@", e.reason);
        }
    }
}

- (void)stopFindDeviceSound {
    @try {
        if (self.audioPlayer && self.audioPlayer.isPlaying) {
            [self.audioPlayer stop];
            // 恢复原始音量
            [self setSystemVolume:self.originalVolume];
            self.audioPlayer = nil;
            NSLOG(@"[FindDeviceAudio] 查找设备音频已停止");
        }
    } @catch (NSException *exception) {
        NSLOG(@"[FindDeviceAudio] 停止音频时发生异常: %@", exception.reason);
    }
}

// 添加AVAudioPlayerDelegate方法
- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    NSLOG(@"[FindDeviceAudio] 音频播放完成，恢复原始音量: %.2f", self.originalVolume);
    // 恢复原始音量
    @try {
        [self setSystemVolume:self.originalVolume];
        self.audioPlayer = nil;
    } @catch (NSException *exception) {
        NSLOG(@"[FindDeviceAudio] 播放完成后恢复音量时发生异常: %@", exception.reason);
    }
}

// 添加设置系统音量的方法
- (void)setSystemVolume:(float)volume {
    NSLOG(@"[FindDeviceAudio] 尝试设置系统音量为: %.2f", volume);
    
    @try {
        // 方法1: 使用AVAudioSession设置音量
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        NSError *error = nil;
        
        // 确保音频会话已激活
        if (![audioSession setActive:YES error:&error]) {
            NSLOG(@"[FindDeviceAudio] 激活音频会话失败: %@", error.localizedDescription);
        }
        
        // 方法2: 使用MPVolumeView (更可靠的方式)
        MPVolumeView *volumeView = [[MPVolumeView alloc] init];
        // 将volumeView添加到屏幕外的位置，但仍在视图层次结构中
        volumeView.frame = CGRectMake(-1000, -1000, 100, 100);
        
        // 获取当前应用的主窗口并添加volumeView
        UIWindow *window = [UIApplication sharedApplication].keyWindow;
        if (!window) {
            window = [[UIApplication sharedApplication].windows firstObject];
        }
        
        if (window) {
            [window addSubview:volumeView];
            
            // 查找并操作音量滑块
            UISlider *volumeSlider = nil;
            for (UIView *view in volumeView.subviews) {
                if ([view isKindOfClass:[UISlider class]]) {
                    volumeSlider = (UISlider *)view;
                    break;
                }
            }
            
            if (volumeSlider) {
                // 使用GCD延迟一小段时间再设置音量，确保UI已完全加载
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    @try {
                        [volumeSlider setValue:volume animated:NO];
                        [volumeSlider sendActionsForControlEvents:UIControlEventValueChanged];
                        NSLOG(@"[FindDeviceAudio] 系统音量已设置为: %.2f", volume);
                    } @catch (NSException *exception) {
                        NSLOG(@"[FindDeviceAudio] 设置音量滑块时发生异常: %@", exception.reason);
                    }
                    
                    // 设置完成后移除volumeView
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [volumeView removeFromSuperview];
                    });
                });
            } else {
                NSLOG(@"[FindDeviceAudio] 无法获取音量滑块控件");
                [volumeView removeFromSuperview];
            }
        } else {
            NSLOG(@"[FindDeviceAudio] 无法获取主窗口");
        }
        
        // 方法3: 直接使用音频播放器的音量属性
        // 这只会影响当前播放器的音量，不是系统音量
        if (self.audioPlayer) {
            self.audioPlayer.volume = 1.0; // 确保播放器音量最大
        }
    } @catch (NSException *exception) {
        NSLOG(@"[FindDeviceAudio] 设置系统音量时发生异常: %@", exception.reason);
    }
}

@end