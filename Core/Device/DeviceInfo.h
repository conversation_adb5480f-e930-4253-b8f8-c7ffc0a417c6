/**
 * @file DeviceInfo.h
 * @brief iOS设备信息获取库
 * @version 1.0
 * @date 2024
 *
 * @description
 * 这个库提供了安全获取iOS设备各种硬件信息的功能，包括序列号、UDID、IMEI等。
 * 所有敏感的字符串都经过加密处理，运行时动态解密，提高安全性。
 *
 * @features
 * - 🔐 加密字符串保护：所有敏感字符串都经过加密存储
 * - 🚀 动态库加载：运行时动态加载MobileGestalt库
 * - 📱 全面设备信息：支持获取序列号、UDID、IMEI、MAC地址等
 * - 🛡️ 内存安全：自动内存管理，防止内存泄漏
 * - 📄 JSON输出：支持将所有设备信息导出为JSON格式
 *
 * @security_notes
 * - 使用加密字符串存储敏感的库路径和函数名
 * - 运行时动态解密，避免静态分析
 * - 自动清理敏感数据，防止内存泄漏
 *
 * @compatibility
 * - iOS 8.0+
 * - 支持所有iOS设备类型
 * - 兼容越狱和非越狱环境
 */

#ifndef DEVICEINFO_H
#define DEVICEINFO_H

#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 核心设备信息获取函数
// ================================

/**
 * @brief 获取设备序列号
 *
 * @param buffer 输出缓冲区，用于存储序列号字符串
 * @param bufferSize 缓冲区大小，建议至少64字节
 *
 * @note 线程安全，可并发调用
 * @note 如果获取失败，buffer将被设置为空字符串
 *
 * @example
 * char serialNumber[64];
 * getSerialNumber(serialNumber, sizeof(serialNumber));
 * printf("序列号: %s\n", serialNumber);
 */
void getSerialNumber(char *buffer, size_t bufferSize);

/**
 * @brief 获取设备唯一标识符(UDID)
 *
 * @param buffer 输出缓冲区，用于存储UDID字符串
 * @param bufferSize 缓冲区大小，建议至少64字节
 *
 * @note UDID是设备的唯一标识符，40个字符的十六进制字符串
 * @note 线程安全，可并发调用
 *
 * @example
 * char udid[64];
 * getUniqueDeviceID(udid, sizeof(udid));
 * printf("UDID: %s\n", udid);
 */
void getUniqueDeviceID(char *buffer, size_t bufferSize);

/**
 * @brief 获取MLB序列号(主逻辑板序列号)
 *
 * @param buffer 输出缓冲区，用于存储MLB序列号
 * @param bufferSize 缓冲区大小，建议至少32字节
 *
 * @note MLB序列号用于标识设备的主逻辑板
 * @note 线程安全，可并发调用
 *
 * @example
 * char mlbSerial[32];
 * getMLBSerialNumber(mlbSerial, sizeof(mlbSerial));
 * printf("MLB序列号: %s\n", mlbSerial);
 */
void getMLBSerialNumber(char *buffer, size_t bufferSize);

/**
 * @brief 获取设备IMEI号
 *
 * @param buffer 输出缓冲区，用于存储IMEI号
 * @param bufferSize 缓冲区大小，建议至少32字节
 *
 * @note IMEI是移动设备的国际身份码，仅蜂窝设备有效
 * @note 对于WiFi-only设备，可能返回空字符串
 * @note 线程安全，可并发调用
 *
 * @example
 * char imei[32];
 * getIMEI(imei, sizeof(imei));
 * printf("IMEI: %s\n", imei);
 */
void getIMEI(char *buffer, size_t bufferSize);

/**
 * @brief 获取WiFi MAC地址
 *
 * @param buffer 输出缓冲区，用于存储WiFi MAC地址
 * @param bufferSize 缓冲区大小，建议至少32字节
 *
 * @note MAC地址格式为 XX:XX:XX:XX:XX:XX
 * @note 线程安全，可并发调用
 *
 * @example
 * char wifiMac[32];
 * getWifiAddress(wifiMac, sizeof(wifiMac));
 * printf("WiFi MAC: %s\n", wifiMac);
 */
void getWifiAddress(char *buffer, size_t bufferSize);

/**
 * @brief 获取蓝牙MAC地址
 *
 * @param buffer 输出缓冲区，用于存储蓝牙MAC地址
 * @param bufferSize 缓冲区大小，建议至少32字节
 *
 * @note MAC地址格式为 XX:XX:XX:XX:XX:XX
 * @note 线程安全，可并发调用
 *
 * @example
 * char bluetoothMac[32];
 * getBluetoothAddress(bluetoothMac, sizeof(bluetoothMac));
 * printf("蓝牙 MAC: %s\n", bluetoothMac);
 */
void getBluetoothAddress(char *buffer, size_t bufferSize);

/**
 * @brief 获取设备产品类型
 *
 * @param buffer 输出缓冲区，用于存储产品类型
 * @param bufferSize 缓冲区大小，建议至少32字节
 *
 * @note 产品类型如 iPhone14,2、iPad13,1 等
 * @note 线程安全，可并发调用
 *
 * @example
 * char productType[32];
 * getProductType(productType, sizeof(productType));
 * printf("产品类型: %s\n", productType);
 */
void getProductType(char *buffer, size_t bufferSize);

/**
 * @brief 获取系统版本号
 *
 * @param buffer 输出缓冲区，用于存储系统版本号
 * @param bufferSize 缓冲区大小，建议至少32字节
 *
 * @note 版本号格式如 15.0.1、16.1.2 等
 * @note 线程安全，可并发调用
 *
 * @example
 * char version[32];
 * getProductVersion(version, sizeof(version));
 * printf("系统版本: %s\n", version);
 */
void getProductVersion(char *buffer, size_t bufferSize);

// ================================
// 综合信息获取函数
// ================================

/**
 * @brief 获取所有设备信息的JSON字符串（推荐使用）
 *
 * @return char* 包含所有设备信息的JSON字符串，失败返回NULL
 *               调用者负责释放返回的内存
 *
 * @note 这是最方便的函数，一次性获取所有设备信息
 * @note 返回的JSON包含所有可用的设备信息字段
 * @note 线程安全，可并发调用
 * @note 必须调用 free() 释放返回的内存
 *
 * @warning 必须手动释放返回的内存！
 *
 * @example
 * char *deviceInfo = deviceInfoInJson();
 * if (deviceInfo) {
 *     printf("设备信息: %s\n", deviceInfo);
 *     free(deviceInfo);  // 必须释放内存
 * }
 *
 * @json_format
 * {
 *   "SerialNumber": "XXXXXXXXXXXX",
 *   "MLBSerialNumber": "XXXXXXXXXXXX",
 *   "UniqueDeviceID": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
 *   "InternationalMobileEquipmentIdentity": "XXXXXXXXXXXXXXX",
 *   "WiFiAddress": "XX:XX:XX:XX:XX:XX",
 *   "BluetoothAddress": "XX:XX:XX:XX:XX:XX",
 *   "ProductType": "iPhone14,2",
 *   "ProductVersion": "16.1.2"
 * }
 */
char* deviceInfoInJson();

// ================================
// 使用指南和注意事项
// ================================

/*
 * 📋 使用指南:
 *
 * 1. 基本使用 - 获取单个信息:
 *    char serialNumber[64];
 *    getSerialNumber(serialNumber, sizeof(serialNumber));
 *    printf("序列号: %s\n", serialNumber);
 *
 * 2. 推荐使用 - 获取完整JSON:
 *    char *deviceInfo = deviceInfoInJson();
 *    if (deviceInfo) {
 *        printf("设备信息: %s\n", deviceInfo);
 *        free(deviceInfo);  // 重要：必须释放内存
 *    }
 *
 * 3. 批量获取多个信息:
 *    char serial[64], udid[64], imei[32];
 *    getSerialNumber(serial, sizeof(serial));
 *    getUniqueDeviceID(udid, sizeof(udid));
 *    getIMEI(imei, sizeof(imei));
 *
 * ⚠️  重要注意事项:
 *
 * 1. 内存管理:
 *    - deviceInfoInJson() 返回的指针必须用 free() 释放
 *    - 其他函数使用调用者提供的缓冲区，无需释放
 *
 * 2. 缓冲区大小:
 *    - 序列号/UDID: 建议64字节
 *    - IMEI/MAC地址: 建议32字节
 *    - 产品类型/版本: 建议32字节
 *
 * 3. 错误处理:
 *    - 如果获取失败，缓冲区将被设置为空字符串
 *    - deviceInfoInJson() 失败时返回 NULL
 *
 * 4. 线程安全:
 *    - 所有函数都是线程安全的，可以并发调用
 *    - 内部使用了适当的同步机制
 *
 * 5. 权限要求:
 *    - 需要访问MobileGestalt框架
 *    - 在沙盒环境中可能受限
 *
 * 6. 性能考虑:
 *    - 首次调用会动态加载库，可能稍慢
 *    - 后续调用使用缓存，性能较好
 *    - 建议在需要时才调用，避免不必要的开销
 */

#ifdef __cplusplus
}
#endif

#endif // DEVICEINFO_H