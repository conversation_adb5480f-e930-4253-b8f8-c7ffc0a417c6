#import "AppList.h"

@interface LSApplicationWorkspace : NSObject
+ (id)defaultWorkspace;
- (NSArray *)allInstalledApplications;
@end

@interface LSApplicationProxy : NSObject
@property (nonatomic, readonly) NSString *applicationIdentifier;
@property (nonatomic, readonly) BOOL isSystemApplication;
@end

@implementation AppList

+ (NSArray *)getAllInstalledApps {
    @try {
        NSMutableArray *appIds = [NSMutableArray array];
        
        // 获取应用工作区类
        Class LSApplicationWorkspaceClass = NSClassFromString(@"LSApplicationWorkspace");
        if (!LSApplicationWorkspaceClass) {
            return @[];
        }
        
        // 获取工作区实例
        LSApplicationWorkspace *workspace = [LSApplicationWorkspaceClass defaultWorkspace];
        if (!workspace) {
            return @[];
        }
        
        // 获取所有已安装应用
        NSArray *apps = [workspace allInstalledApplications];
        if (!apps) {
            return @[];
        }
        
        // 遍历应用并处理
        for (LSApplicationProxy *app in apps) {
            @try {
                NSString *bundleId = app.applicationIdentifier;
                if (bundleId.length > 0) {
                    [appIds addObject:bundleId];
                }
            } @catch (NSException *appException) {
                continue;
            }
        }
        
        return [appIds copy];
    } @catch (NSException *exception) {
        return @[];
    }
}

@end