#import "DeviceBattery.h"
#import "Log.h"

@implementation DeviceBattery

+ (NSDictionary *)getBatteryInfo {
    @try {
        UIDevice *device = [UIDevice currentDevice];
        if (!device.isBatteryMonitoringEnabled) {
            device.batteryMonitoringEnabled = YES;
        }
        
        // 给系统一点时间更新电池状态
        [[NSRunLoop currentRunLoop] runUntilDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
        
        float batteryLevel = device.batteryLevel;
        UIDeviceBatteryState batteryState = device.batteryState;
        
        // 处理无效的电池电量值
        if (batteryLevel < 0) {
            batteryLevel = 0;
        }
        
        NSString *stateString;
        switch (batteryState) {
            case UIDeviceBatteryStateUnplugged:
                stateString = @"unplugged";
                break;
            case UIDeviceBatteryStateCharging:
                stateString = @"charging";
                break;
            case UIDeviceBatteryStateFull:
                stateString = @"full";
                break;
            case UIDeviceBatteryStateUnknown:
            default:
                stateString = @"unknown";
                break;
        }
        
        return @{
            @"level": @((int)(batteryLevel * 100)),
            @"state": stateString
        };
    } @catch (NSException *exception) {
        NSLOG(@"[BatteryInfo] : %@", exception);
        return @{
            @"level": @(0),
            @"state": @"error"
        };
    }
}

@end