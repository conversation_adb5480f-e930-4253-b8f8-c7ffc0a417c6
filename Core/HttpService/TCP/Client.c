#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <signal.h>
#include <time.h>
#include <stdarg.h>
#include "Log.h"


#define SERVER_HOST "127.0.0.1"
#define SERVER_PORT 25874
#define RECONNECT_INTERVAL 2  // 重连间隔
#define HEARTBEAT_INTERVAL 5  // 🔧 心跳间隔5秒
#define CONNECTION_TIMEOUT 10
#define BUFFER_SIZE 2048

// 🔧 极简客户端状态
static volatile int g_client_socket = -1;
static volatile int g_is_connected = 0;
static volatile int g_should_reconnect = 1;
static volatile int g_reconnect_thread_running = 0;  // 防止重连线程重复创建
static volatile int g_heartbeat_thread_running = 0;  // 🔧 心跳线程状态

// 🔧 极简函数声明
static int connect_to_server(void);
static void* simple_receive_thread(void* arg);
static int send_simple_message(const char* message);
static void simple_reconnect(void);
static void* background_reconnect_thread(void* arg);
static void* heartbeat_thread(void* arg);  // 🔧 心跳线程
static void start_heartbeat(void);         // 🔧 启动心跳
static void stop_heartbeat(void);          // 🔧 停止心跳


// 🔧 极简连接到服务器
static int connect_to_server(void) {
    if (g_is_connected) {
        LOG("[TCPClient] ⚠️ 已经连接到服务器");
        return 1;
    }

    // 创建socket
    int socket_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (socket_fd < 0) {
        LOG("[TCPClient] ❌ 创建socket失败: %s", strerror(errno));
        return 0;
    }

    // 设置基本socket选项
    int opt = 1;
    setsockopt(socket_fd, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt));
    setsockopt(socket_fd, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));

    // 连接服务器
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = inet_addr(SERVER_HOST);
    server_addr.sin_port = htons(SERVER_PORT);

    if (connect(socket_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        LOG("[TCPClient] ❌ 连接服务器失败: %s:%d, 错误: %s", SERVER_HOST, SERVER_PORT, strerror(errno));
        close(socket_fd);
        return 0;
    }

    // 连接成功
    g_client_socket = socket_fd;
    g_is_connected = 1;

    LOG("[TCPClient] ✅ 成功连接到服务器: %s:%d", SERVER_HOST, SERVER_PORT);

    // 启动简单的接收线程
    pthread_t receive_thread;
    if (pthread_create(&receive_thread, NULL, simple_receive_thread, NULL) == 0) {
        pthread_detach(receive_thread);
    }

    // 🔧 启动心跳线程
    start_heartbeat();

    return 1;
}

// 🔧 极简接收线程
static void* simple_receive_thread(void* arg) {
    char buffer[BUFFER_SIZE];

    LOG("[TCPClient] 🔄 接收线程启动");

    while (g_is_connected && g_client_socket >= 0) {
        ssize_t bytes_read = recv(g_client_socket, buffer, sizeof(buffer) - 1, 0);

        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';

            // 🔧 检查是否为心跳响应
            if (strstr(buffer, "pong") || strcmp(buffer, "pong\n") == 0) {
                LOG("[TCPClient] ❤️ 收到心跳响应");
            } else {
                LOG("[TCPClient] 📨 收到消息: %s", buffer);
            }
        }
        else if (bytes_read <= 0) {
            LOG("[TCPClient] 🔌 连接断开");

            // 🔧 停止心跳
            stop_heartbeat();

            g_is_connected = 0;
            close(g_client_socket);
            g_client_socket = -1;

            // 启动重连
            if (g_should_reconnect) {
                simple_reconnect();
            }
            break;
        }
    }

    LOG("[TCPClient] 🛑 接收线程结束");
    return NULL;
}

// 🔧 简单发送消息
static int send_simple_message(const char* message) {
    if (!g_is_connected || g_client_socket < 0) {
        LOG("[TCPClient] ⚠️ 未连接到服务器");
        return 0;
    }

    char full_message[BUFFER_SIZE];
    snprintf(full_message, sizeof(full_message), "%s\n", message ? message : "ping");

    ssize_t bytes_sent = send(g_client_socket, full_message, strlen(full_message), MSG_NOSIGNAL);
    if (bytes_sent > 0) {
        LOG("[TCPClient] 📤 发送消息成功");
        return 1;
    }

    LOG("[TCPClient] ❌ 发送消息失败: %s", strerror(errno));
    return 0;
}



// 🔧 极简重连函数 - 防止重复创建重连线程
static void simple_reconnect(void) {
    if (!g_should_reconnect) {
        return;
    }

    // 🔧 防止重连线程重复创建
    if (g_reconnect_thread_running) {
        LOG("[TCPClient] ⚠️ 重连线程已在运行");
        return;
    }

    LOG("[TCPClient] � 启动后台重连");
    g_reconnect_thread_running = 1;

    // 创建后台重连线程
    pthread_t reconnect_thread;
    if (pthread_create(&reconnect_thread, NULL, background_reconnect_thread, NULL) == 0) {
        pthread_detach(reconnect_thread);
    } else {
        g_reconnect_thread_running = 0;  // 创建失败，重置状态
        LOG("[TCPClient] ❌ 创建重连线程失败");
    }
}

// 后台重连线程
static void* background_reconnect_thread(void* arg) {
    while (g_should_reconnect && !g_is_connected) {
        sleep(RECONNECT_INTERVAL);

        if (g_should_reconnect && !g_is_connected) {
            LOG("[TCPClient] 🔄 尝试重连");
            connect_to_server();
        }
    }

    LOG("[TCPClient] 🛑 重连线程结束");
    return NULL;
}


// 构造函数 - 京东APP启动时自动执行
__attribute__((constructor))
static void init_client(void) {

    LOG("TCP服务构造函数执行 - 进程: %s", getprogname());
    // 判断getprogname()是否为JD4iPhone
    if (strcmp(getprogname(), "JD4iPhone") != 0) {
        return;
    }

    // 🔧 初始化syslog
    openlog("TCPClient", LOG_PID | LOG_CONS, LOG_USER);

    LOG("[TCPClient] 🚀 京东APP TCP客户端初始化");

    // 忽略SIGPIPE信号
    signal(SIGPIPE, SIG_IGN);

    // 初始化状态
    g_client_socket = -1;
    g_is_connected = 0;
    g_should_reconnect = 1;

    // Service肯定已经在运行，直接连接
    LOG("[TCPClient] 🌐 连接TCP服务器");
    if (connect_to_server()) {
        LOG("[TCPClient] ✅ 连接成功");
        // 发送一条信息
        send_simple_message("Hello, World!");
    } else {
        LOG("[TCPClient] ❌ 连接失败，将自动重连");
    }
}

// 🔧 极简析构函数
__attribute__((destructor))
static void cleanup_client(void) {
    LOG("[TCPClient] 🧹 京东APP TCP客户端清理");

    g_should_reconnect = 0;
    g_is_connected = 0;

    // 🔧 停止心跳
    stop_heartbeat();

    if (g_client_socket >= 0) {
        close(g_client_socket);
        g_client_socket = -1;
    }

    closelog();
}

// 🔧 心跳线程函数
static void* heartbeat_thread(void* arg) {
    LOG("[TCPClient] ❤️ 心跳线程启动");

    while (g_heartbeat_thread_running && g_is_connected) {
        sleep(HEARTBEAT_INTERVAL);

        if (!g_heartbeat_thread_running || !g_is_connected) {
            break;
        }

        // 发送心跳包
        if (!send_simple_message("ping")) {
            LOG("[TCPClient] 💔 心跳发送失败");
            break;
        }

        LOG("[TCPClient] ❤️ 心跳发送成功");
    }

    g_heartbeat_thread_running = 0;
    LOG("[TCPClient] 🛑 心跳线程结束");
    return NULL;
}

// 🔧 启动心跳
static void start_heartbeat(void) {
    if (g_heartbeat_thread_running) {
        LOG("[TCPClient] ⚠️ 心跳线程已在运行");
        return;
    }

    g_heartbeat_thread_running = 1;

    pthread_t heartbeat_tid;
    if (pthread_create(&heartbeat_tid, NULL, heartbeat_thread, NULL) == 0) {
        pthread_detach(heartbeat_tid);
        LOG("[TCPClient] ✅ 心跳线程启动成功");
    } else {
        g_heartbeat_thread_running = 0;
        LOG("[TCPClient] ❌ 心跳线程启动失败");
    }
}

// 🔧 停止心跳
static void stop_heartbeat(void) {
    if (g_heartbeat_thread_running) {
        LOG("[TCPClient] 🛑 停止心跳线程");
        g_heartbeat_thread_running = 0;
    }
}