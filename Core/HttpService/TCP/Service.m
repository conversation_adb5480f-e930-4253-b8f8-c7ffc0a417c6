#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <sys/socket.h>
#import <netinet/in.h>
#import <netinet/tcp.h>  // 🔧 添加TCP相关定义
#import <arpa/inet.h>
#import <unistd.h>
#import <fcntl.h>
#import <errno.h>
#import "Log.h"

#define SERVER_PORT 25874
#define MAX_CLIENTS 10
#define SOCKET_TIMEOUT 30  // 30秒socket超时
#define KEEPALIVE_INTERVAL 10  // 10秒keepalive间隔

@interface TCPService : NSObject
@property (nonatomic, assign) int serverSocket;
@property (nonatomic, strong) NSMutableArray *clientSockets;
@property (nonatomic, assign) BOOL isRunning;
@property (nonatomic, strong) dispatch_queue_t serverQueue;
@property (nonatomic, strong) dispatch_source_t serverSource;  // 🔧 添加GCD source管理
@property (nonatomic, strong) NSMutableDictionary *clientInfo;  // 🔧 客户端信息管理
@end

@implementation TCPService

- (instancetype)init {
    self = [super init];
    if (self) {
        _clientSockets = [NSMutableArray array];
        _clientInfo = [NSMutableDictionary dictionary];  // 🔧 初始化客户端信息字典
        _serverQueue = dispatch_queue_create("com.service.tcp.queue", DISPATCH_QUEUE_CONCURRENT);
        _isRunning = NO;
        _serverSocket = -1;  // 🔧 初始化为无效socket
    }
    return self;
}

- (BOOL)startServer {
    if (self.isRunning) {
        NSLOG(@"[TCPService] 服务已经在运行");
        return YES;
    }
    
    // 创建socket
    self.serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (self.serverSocket < 0) {
        NSLOG(@"[TCPService] 创建socket失败");
        return NO;
    }
    
    // 🔧 设置socket选项，提升稳定性
    int opt = 1;
    // 允许地址重用
    if (setsockopt(self.serverSocket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        NSLOG(@"[TCPService] 设置SO_REUSEADDR失败");
        close(self.serverSocket);
        return NO;
    }

    // 启用keepalive
    if (setsockopt(self.serverSocket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt)) < 0) {
        NSLOG(@"[TCPService] 设置SO_KEEPALIVE失败");
    }

    // 设置接收缓冲区大小
    int bufferSize = 65536;  // 64KB
    if (setsockopt(self.serverSocket, SOL_SOCKET, SO_RCVBUF, &bufferSize, sizeof(bufferSize)) < 0) {
        NSLOG(@"[TCPService] 设置接收缓冲区失败");
    }

    // 设置发送缓冲区大小
    if (setsockopt(self.serverSocket, SOL_SOCKET, SO_SNDBUF, &bufferSize, sizeof(bufferSize)) < 0) {
        NSLOG(@"[TCPService] 设置发送缓冲区失败");
    }
    
    // 绑定地址
    struct sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = inet_addr("127.0.0.1");
    serverAddr.sin_port = htons(SERVER_PORT);
    
    if (bind(self.serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        NSLOG(@"[TCPService] 绑定地址失败，端口: %d", SERVER_PORT);
        close(self.serverSocket);
        return NO;
    }
    
    // 开始监听
    if (listen(self.serverSocket, MAX_CLIENTS) < 0) {
        NSLOG(@"[TCPService] 监听失败");
        close(self.serverSocket);
        return NO;
    }
    
    self.isRunning = YES;
    NSLOG(@"[TCPService] TCP服务启动成功，监听端口: %d", SERVER_PORT);
    
    // 在后台线程处理连接
    dispatch_async(self.serverQueue, ^{
        [self acceptConnections];
    });
    
    return YES;
}

- (void)acceptConnections {
    while (self.isRunning) {
        struct sockaddr_in clientAddr;
        socklen_t clientLen = sizeof(clientAddr);
        
        int clientSocket = accept(self.serverSocket, (struct sockaddr*)&clientAddr, &clientLen);
        if (clientSocket < 0) {
            if (self.isRunning) {
                NSLOG(@"[TCPService] 接受连接失败");
            }
            continue;
        }
        
        NSString *clientIP = [NSString stringWithUTF8String:inet_ntoa(clientAddr.sin_addr)];
        NSLOG(@"[TCPService] 新客户端连接: %@:%d", clientIP, ntohs(clientAddr.sin_port));

        // 🔧 设置客户端socket选项
        int opt = 1;
        setsockopt(clientSocket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt));

        // 设置TCP_NODELAY，减少延迟
        setsockopt(clientSocket, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));

        // 添加到客户端列表和信息字典
        @synchronized(self.clientSockets) {
            [self.clientSockets addObject:@(clientSocket)];
            self.clientInfo[@(clientSocket)] = @{
                @"ip": clientIP,
                @"port": @(ntohs(clientAddr.sin_port)),
                @"connectTime": @([[NSDate date] timeIntervalSince1970]),
                @"lastActivity": @([[NSDate date] timeIntervalSince1970])
            };
        }
        
        // 为每个客户端创建处理线程
        dispatch_async(self.serverQueue, ^{
            [self handleClient:clientSocket];
        });
    }
}

- (void)handleClient:(int)clientSocket {
    // 🔧 设置socket为非阻塞模式，并添加超时控制
    int flags = fcntl(clientSocket, F_GETFL, 0);
    fcntl(clientSocket, F_SETFL, flags | O_NONBLOCK);

    // 设置接收和发送超时
    struct timeval timeout;
    timeout.tv_sec = SOCKET_TIMEOUT;
    timeout.tv_usec = 0;
    setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
    setsockopt(clientSocket, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));

    char buffer[2048];  // 🔧 增大缓冲区
    NSMutableString *messageBuffer = [NSMutableString string];
    NSTimeInterval lastActivityTime = [[NSDate date] timeIntervalSince1970];

    while (self.isRunning) {
        ssize_t bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);

        if (bytesRead > 0) {
            buffer[bytesRead] = '\0';
            [messageBuffer appendString:[NSString stringWithUTF8String:buffer]];

            // 🔧 更新客户端活动时间
            lastActivityTime = [[NSDate date] timeIntervalSince1970];
            @synchronized(self.clientSockets) {
                NSMutableDictionary *info = [self.clientInfo[@(clientSocket)] mutableCopy];
                if (info) {
                    info[@"lastActivity"] = @(lastActivityTime);
                    self.clientInfo[@(clientSocket)] = info;
                }
            }

            // 检查是否有完整的JSON消息（简单的换行符分隔）
            NSRange newlineRange = [messageBuffer rangeOfString:@"\n"];
            while (newlineRange.location != NSNotFound) {
                NSString *message = [messageBuffer substringToIndex:newlineRange.location];
                [messageBuffer deleteCharactersInRange:NSMakeRange(0, newlineRange.location + 1)];

                if (message.length > 0) {
                    NSLOG(@"[TCPService] 收到消息: %@", message);

                    // 处理消息
                    NSString *response = [self processMessage:message];

                    // 🔧 安全发送响应，添加错误处理
                    if (response) {
                        NSString *responseWithNewline = [response stringByAppendingString:@"\n"];
                        const char *responseData = [responseWithNewline UTF8String];
                        ssize_t bytesSent = send(clientSocket, responseData, strlen(responseData), MSG_NOSIGNAL);
                        if (bytesSent < 0) {
                            NSLOG(@"[TCPService] 发送响应失败: %s", strerror(errno));
                            goto cleanup;
                        }
                        NSLOG(@"[TCPService] 发送响应: %@", response);
                    }
                }

                newlineRange = [messageBuffer rangeOfString:@"\n"];
            }
        }
        else if (bytesRead == 0) {
            NSLOG(@"[TCPService] 客户端正常断开连接: socket=%d", clientSocket);
            break;
        }
        else {
            // bytesRead < 0
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 🔧 检查客户端超时
                NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
                if (currentTime - lastActivityTime > SOCKET_TIMEOUT) {
                    NSLOG(@"[TCPService] 客户端超时断开: socket=%d", clientSocket);
                    break;
                }
                // 非阻塞模式下没有数据可读，休眠一下避免CPU占用过高
                usleep(10000); // 10ms
                continue;
            } else {
                NSLOG(@"[TCPService] 客户端异常断开连接: socket=%d, error=%s", clientSocket, strerror(errno));
                break;
            }
        }
    }

cleanup:
    // 🔧 清理客户端连接和信息
    @synchronized(self.clientSockets) {
        [self.clientSockets removeObject:@(clientSocket)];
        [self.clientInfo removeObjectForKey:@(clientSocket)];
    }
    close(clientSocket);
    NSLOG(@"[TCPService] 客户端处理线程结束: socket=%d", clientSocket);
}

- (NSString *)processMessage:(NSString *)message {
    @try {
        // 尝试解析JSON消息
        NSData *data = [message dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *messageDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
        
        if (!messageDict) {
            return @"{\"status\":\"error\",\"message\":\"Invalid JSON format\"}";
        }
        
        NSString *action = messageDict[@"action"];
        NSString *messageId = messageDict[@"messageId"] ?: @"unknown";

        // 简化处理逻辑，主要处理心跳
        if ([action isEqualToString:@"ping"]) {
            NSLOG(@"[TCPService] ❤️ 收到心跳请求");
            return [NSString stringWithFormat:@"{\"status\":\"success\",\"action\":\"pong\",\"messageId\":\"%@\",\"timestamp\":%f}", messageId, [[NSDate date] timeIntervalSince1970]];
        }
        else {
            NSLOG(@"[TCPService] 📨 收到消息: %@ - %@", action, messageDict);
            return [NSString stringWithFormat:@"{\"status\":\"success\",\"action\":\"response\",\"messageId\":\"%@\",\"message\":\"消息已收到\"}", messageId];
        }
        
    } @catch (NSException *exception) {
        NSLOG(@"[TCPService] 处理消息异常: %@", exception.reason);
        return @"{\"status\":\"error\",\"message\":\"Message processing failed\"}";
    }
}

- (void)stopServer {
    if (!self.isRunning) {
        return;
    }
    
    self.isRunning = NO;
    
    // 关闭所有客户端连接
    @synchronized(self.clientSockets) {
        for (NSNumber *socketNum in self.clientSockets) {
            close([socketNum intValue]);
        }
        [self.clientSockets removeAllObjects];
    }
    
    // 关闭服务器socket
    if (self.serverSocket >= 0) {
        close(self.serverSocket);
        self.serverSocket = -1;
    }
    
    NSLOG(@"[TCPService] TCP服务已停止");
}

- (void)broadcastMessage:(NSString *)message {
    if (!message) return;
    
    const char *data = [message UTF8String];
    
    @synchronized(self.clientSockets) {
        NSMutableArray *disconnectedSockets = [NSMutableArray array];
        
        for (NSNumber *socketNum in self.clientSockets) {
            int clientSocket = [socketNum intValue];
            ssize_t bytesSent = send(clientSocket, data, strlen(data), 0);
            
            if (bytesSent < 0) {
                NSLOG(@"[TCPService] 发送广播消息失败，socket: %d", clientSocket);
                [disconnectedSockets addObject:socketNum];
            }
        }
        
        // 移除断开的连接
        for (NSNumber *socketNum in disconnectedSockets) {
            [self.clientSockets removeObject:socketNum];
            close([socketNum intValue]);
        }
    }
    
    NSLOG(@"[TCPService] 广播消息: %@", message);
}

@end

// 全局服务实例
static TCPService *globalTCPService = nil;

// 启动服务的函数
static void startTCPService() {
    if (!globalTCPService) {
        globalTCPService = [[TCPService alloc] init];
    }
    
    if (![globalTCPService startServer]) {
        NSLOG(@"[Service] TCP服务启动失败");
    }
}

// 停止服务的函数
static void stopTCPService() {
    if (globalTCPService) {
        [globalTCPService stopServer];
    }
}

// 广播消息的函数
// static void broadcastMessage(NSString *message) {
//     if (globalTCPService) {
//         [globalTCPService broadcastMessage:message];
//     }
// }

// 构造函数 - SpringBoard启动时自动执行
__attribute__((constructor)) static void initService() {
    NSLOG(@"[Service] 🚀 SpringBoard TCP服务初始化");

    // 延迟启动，确保SpringBoard完全加载
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSLOG(@"[Service] 🌐 启动TCP服务器");
        startTCPService();
    });
}

// 析构函数 - SpringBoard退出时执行
__attribute__((destructor))
static void cleanupService() {
    NSLOG(@"[Service] SpringBoard TCP服务清理");
    stopTCPService();
}
