#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <signal.h>
#include <sys/select.h>
#include <time.h>
#include <stdarg.h>
#include "Log.h"


#define SERVER_PORT 25874       // 服务器端口
#define MAX_CLIENTS 10          // 最大连接数
#define BUFFER_SIZE 2048        // 缓冲区大小
#define SOCKET_TIMEOUT 30       // 30秒socket超时
#define KEEPALIVE_INTERVAL 10   // 10秒keepalive间隔

// 🔧 极简的服务器状态
static volatile int g_server_socket = -1;
static volatile int g_is_running = 0;

// 函数声明
static int setup_server_socket(void);
static void* server_thread_func(void* arg);
static void handle_client(void* arg);
static void send_response(int client_socket, const char* message);

// 设置服务器socket
static int setup_server_socket(void) {
    int server_socket;
    struct sockaddr_in server_addr;
    int opt = 1;
    
    // 创建socket
    server_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (server_socket < 0) {
        LOG("[TCPService] ❌ 创建socket失败: %s", strerror(errno));
        return -1;
    }

    // 设置socket选项
    if (setsockopt(server_socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        LOG("[TCPService] ❌ 设置SO_REUSEADDR失败: %s", strerror(errno));
        close(server_socket);
        return -1;
    }

    // 启用keepalive
    if (setsockopt(server_socket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt)) < 0) {
        LOG("[TCPService] ⚠️ 设置SO_KEEPALIVE失败: %s", strerror(errno));
    }
    
    // 设置缓冲区大小
    int buffer_size = 65536;
    setsockopt(server_socket, SOL_SOCKET, SO_RCVBUF, &buffer_size, sizeof(buffer_size));
    setsockopt(server_socket, SOL_SOCKET, SO_SNDBUF, &buffer_size, sizeof(buffer_size));
    
    // 绑定地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
    server_addr.sin_port = htons(SERVER_PORT);
    
    if (bind(server_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        LOG("[TCPService] ❌ 绑定地址失败，端口: %d, 错误: %s", SERVER_PORT, strerror(errno));
        close(server_socket);
        return -1;
    }

    // 开始监听
    if (listen(server_socket, MAX_CLIENTS) < 0) {
        LOG("[TCPService] ❌ 监听失败: %s", strerror(errno));
        close(server_socket);
        return -1;
    }

    LOG("[TCPService] ✅ TCP服务器启动成功，监听端口: %d", SERVER_PORT);
    return server_socket;
}

// 🔧 极简启动TCP服务器
static int start_tcp_server(void) {
    if (g_is_running) {
        LOG("[TCPService] ⚠️ 服务已经在运行");
        return 1;
    }

    // 设置服务器socket
    g_server_socket = setup_server_socket();
    if (g_server_socket < 0) {
        return 0;
    }

    g_is_running = 1;

    // 创建服务器线程
    pthread_t server_thread;
    if (pthread_create(&server_thread, NULL, server_thread_func, NULL) != 0) {
        LOG("[TCPService] ❌ 创建服务器线程失败");
        close(g_server_socket);
        g_server_socket = -1;
        g_is_running = 0;
        return 0;
    }

    // 分离线程，让其自动清理
    pthread_detach(server_thread);

    return 1;
}

// 🔧 极简服务器主线程
static void* server_thread_func(void* arg) {
    struct sockaddr_in client_addr;
    socklen_t client_len = sizeof(client_addr);

    LOG("[TCPService] 🚀 服务器线程启动");

    while (g_is_running) {
        int client_socket = accept(g_server_socket, (struct sockaddr*)&client_addr, &client_len);

        if (client_socket < 0) {
            if (g_is_running && errno != EINTR) {
                LOG("[TCPService] ❌ 接受连接失败: %s", strerror(errno));
            }
            continue;
        }

        // 直接在新线程中处理客户端，不维护客户端列表
        pthread_t client_thread;
        int* client_socket_ptr = malloc(sizeof(int));
        if (client_socket_ptr) {
            *client_socket_ptr = client_socket;
            if (pthread_create(&client_thread, NULL, (void*(*)(void*))handle_client, client_socket_ptr) == 0) {
                pthread_detach(client_thread);
            } else {
                free(client_socket_ptr);
                close(client_socket);
            }
        } else {
            close(client_socket);
        }
    }

    LOG("[TCPService] 🛑 服务器线程结束");
    return NULL;
}

// 🔧 极简客户端处理函数
static void handle_client(void* arg) {
    int client_socket = *(int*)arg;
    free(arg);

    LOG("[TCPService] 🔗 新客户端连接: socket=%d", client_socket);

    // 设置socket选项
    int opt = 1;
    setsockopt(client_socket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt));
    setsockopt(client_socket, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));

    char buffer[BUFFER_SIZE];
    char message_buffer[BUFFER_SIZE * 2] = {0};

    while (g_is_running) {
        ssize_t bytes_read = recv(client_socket, buffer, sizeof(buffer) - 1, 0);

        if (bytes_read <= 0) {
            break;  // 客户端断开或错误
        }

        buffer[bytes_read] = '\0';
        strncat(message_buffer, buffer, sizeof(message_buffer) - strlen(message_buffer) - 1);

        // 处理完整消息
        char* line_start = message_buffer;
        char* newline_pos;

        while ((newline_pos = strchr(line_start, '\n')) != NULL) {
            *newline_pos = '\0';

            if (strlen(line_start) > 0) {
                send_response(client_socket, line_start);
            }

            line_start = newline_pos + 1;
        }

        // 保留未完成的消息
        if (line_start != message_buffer) {
            memmove(message_buffer, line_start, strlen(line_start) + 1);
        }
    }

    close(client_socket);
    LOG("[TCPService] 🔌 客户端断开: socket=%d", client_socket);
}

// 🔧 发送响应
static void send_response(int client_socket, const char* message) {
    if (!message || strlen(message) == 0) {
        return;
    }

    // 简单响应
    char response[512];
    time_t now = time(NULL);

    // 🔧 检查是否为心跳包
    if (strstr(message, "ping") || strcmp(message, "ping") == 0) {
        snprintf(response, sizeof(response), "pong\n");
        LOG("[TCPService] ❤️ 收到心跳包，回复pong");
    } else {
        snprintf(response, sizeof(response),
                "{\"status\":\"success\",\"action\":\"response\",\"message\":\"收到\",\"timestamp\":%ld}\n", now);
        LOG("[TCPService] 📨 收到消息: %s", message);
    }

    ssize_t bytes_sent = send(client_socket, response, strlen(response), MSG_NOSIGNAL);
    if (bytes_sent > 0) {
        if (strstr(message, "ping") || strcmp(message, "ping") == 0) {
            LOG("[TCPService] ❤️ 心跳响应发送成功");
        } else {
            LOG("[TCPService] 📤 响应发送成功");
        }
    } else {
        LOG("[TCPService] ❌ 响应发送失败: %s", strerror(errno));
    }
}



// 🔧 极简停止TCP服务器
static void stop_tcp_server(void) {
    if (!g_is_running) {
        return;
    }

    LOG("[TCPService] 🛑 正在停止TCP服务器...");
    g_is_running = 0;

    // 关闭服务器socket
    if (g_server_socket >= 0) {
        close(g_server_socket);
        g_server_socket = -1;
    }

    LOG("[TCPService] ✅ TCP服务器已停止");
}



// 构造函数 - SpringBoard启动时自动执行
__attribute__((constructor))
static void init_service(void) {
    // 🔧 多种方式确保日志输出

    // 2. 初始化syslog
    openlog("TCPService", LOG_PID | LOG_CONS | LOG_PERROR, LOG_USER);

    // 3. 使用不同的syslog级别
    LOG("TCP服务构造函数执行 - 进程: %s", getprogname());
    LOG("TCP服务初始化开始");

    // 4. 强制刷新syslog
    closelog();
    openlog("TCPService", LOG_PID | LOG_CONS | LOG_PERROR, LOG_USER);

    LOG("[TCPService] 🚀 SpringBoard TCP服务初始化66666");

    // 忽略SIGPIPE信号
    signal(SIGPIPE, SIG_IGN);

    // 🔧 立即启动TCP服务器
    LOG("[TCPService] 🌐 启动TCP服务器");
    if (!start_tcp_server()) {
        LOG("[TCPService] ❌ TCP服务启动失败");
    }
}

// 析构函数 - SpringBoard退出时执行
__attribute__((destructor))
static void cleanup_service(void) {
    LOG("[TCPService] 🧹 SpringBoard TCP服务清理");
    stop_tcp_server();

    // 🔧 关闭syslog
    closelog();
}
