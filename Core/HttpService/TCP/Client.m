#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <sys/socket.h>
#import <netinet/in.h>
#import <netinet/tcp.h>  // 🔧 添加TCP相关定义
#import <arpa/inet.h>
#import <unistd.h>
#import <fcntl.h>
#import <errno.h>
#import "Log.h"

#define SERVER_HOST "127.0.0.1"
#define SERVER_PORT 25874
#define RECONNECT_INTERVAL 3.0   // 🔧 增加重连间隔到3秒
#define HEARTBEAT_INTERVAL 8.0   // 🔧 增加心跳间隔到8秒
#define CONNECTION_TIMEOUT 10.0  // 🔧 连接超时10秒
#define MAX_RECONNECT_ATTEMPTS 5 // 🔧 最大重连次数

@interface TCPClient : NSObject
@property (nonatomic, assign) int clientSocket;
@property (nonatomic, assign) BOOL isConnected;
@property (nonatomic, assign) BOOL shouldReconnect;
@property (nonatomic, strong) dispatch_queue_t clientQueue;
@property (nonatomic, strong) NSTimer *heartbeatTimer;
@property (nonatomic, strong) NSTimer *reconnectTimer;
@property (nonatomic, assign) NSUInteger messageIdCounter;
@property (nonatomic, assign) NSUInteger reconnectAttempts;  // 🔧 重连计数器
@property (nonatomic, assign) NSTimeInterval lastHeartbeatTime;  // 🔧 最后心跳时间
@property (nonatomic, strong) NSMutableString *receiveBuffer;  // 🔧 接收缓冲区
@end

@implementation TCPClient

- (instancetype)init {
    self = [super init];
    if (self) {
        _clientSocket = -1;
        _isConnected = NO;
        _shouldReconnect = YES;
        _messageIdCounter = 0;
        _reconnectAttempts = 0;  // 🔧 初始化重连计数器
        _lastHeartbeatTime = 0;  // 🔧 初始化心跳时间
        _receiveBuffer = [NSMutableString string];  // 🔧 初始化接收缓冲区
        _clientQueue = dispatch_queue_create("com.client.tcp.queue", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

- (BOOL)connectToServer {
    if (self.isConnected) {
        NSLOG(@"[TCPClient] 已经连接到服务器");
        return YES;
    }
    
    // 创建socket
    self.clientSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (self.clientSocket < 0) {
        NSLOG(@"[TCPClient] 创建socket失败");
        return NO;
    }
    
    // 🔧 设置socket选项，提升稳定性
    int opt = 1;
    // 启用keepalive
    setsockopt(self.clientSocket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt));

    // 设置TCP_NODELAY，减少延迟
    setsockopt(self.clientSocket, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));

    // 设置发送超时
    struct timeval sendTimeout;
    sendTimeout.tv_sec = 10;  // 🔧 增加发送超时到10秒
    sendTimeout.tv_usec = 0;
    setsockopt(self.clientSocket, SOL_SOCKET, SO_SNDTIMEO, &sendTimeout, sizeof(sendTimeout));

    // 设置接收缓冲区大小
    int bufferSize = 32768;  // 32KB
    setsockopt(self.clientSocket, SOL_SOCKET, SO_RCVBUF, &bufferSize, sizeof(bufferSize));
    setsockopt(self.clientSocket, SOL_SOCKET, SO_SNDBUF, &bufferSize, sizeof(bufferSize));
    
    // 连接服务器
    struct sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = inet_addr(SERVER_HOST);
    serverAddr.sin_port = htons(SERVER_PORT);
    
    if (connect(self.clientSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        NSLOG(@"[TCPClient] 连接服务器失败: %s:%d, error: %s", SERVER_HOST, SERVER_PORT, strerror(errno));
        close(self.clientSocket);
        self.clientSocket = -1;
        self.reconnectAttempts++;  // 🔧 增加重连计数
        return NO;
    }

    self.isConnected = YES;
    self.reconnectAttempts = 0;  // 🔧 连接成功后重置重连计数
    self.lastHeartbeatTime = [[NSDate date] timeIntervalSince1970];  // 🔧 记录连接时间
    NSLOG(@"[TCPClient] ✅ 成功连接到服务器: %s:%d", SERVER_HOST, SERVER_PORT);
    
    // 启动接收消息的线程
    dispatch_async(self.clientQueue, ^{
        [self receiveMessages];
    });
    
    // 启动心跳
    [self startHeartbeat];
    
    // 发送连接确认消息
    [self sendMessage:@{
        @"action": @"connect",
        @"client": @"JDMobile",
        @"timestamp": @([[NSDate date] timeIntervalSince1970])
    }];
    
    return YES;
}

- (void)receiveMessages {
    // 🔧 设置socket为非阻塞模式
    int flags = fcntl(self.clientSocket, F_GETFL, 0);
    fcntl(self.clientSocket, F_SETFL, flags | O_NONBLOCK);

    char buffer[2048];  // 🔧 增大缓冲区
    // 🔧 使用实例变量作为消息缓冲区，避免重复创建
    [self.receiveBuffer setString:@""];

    while (self.isConnected && self.clientSocket >= 0) {
        ssize_t bytesRead = recv(self.clientSocket, buffer, sizeof(buffer) - 1, 0);

        if (bytesRead > 0) {
            buffer[bytesRead] = '\0';
            [self.receiveBuffer appendString:[NSString stringWithUTF8String:buffer]];

            // 🔧 更新最后活动时间
            self.lastHeartbeatTime = [[NSDate date] timeIntervalSince1970];

            // 检查是否有完整的消息（以换行符分隔）
            NSRange newlineRange = [self.receiveBuffer rangeOfString:@"\n"];
            while (newlineRange.location != NSNotFound) {
                NSString *message = [self.receiveBuffer substringToIndex:newlineRange.location];
                [self.receiveBuffer deleteCharactersInRange:NSMakeRange(0, newlineRange.location + 1)];

                if (message.length > 0) {
                    // 🔧 在客户端队列中处理消息，避免主线程阻塞
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self handleReceivedMessage:message];
                    });
                }

                newlineRange = [self.receiveBuffer rangeOfString:@"\n"];
            }
        }
        else if (bytesRead == 0) {
            NSLOG(@"[TCPClient] 服务器正常关闭连接");
            [self handleDisconnection];
            break;
        }
        else {
            // bytesRead < 0
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 🔧 检查心跳超时
                NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
                if (currentTime - self.lastHeartbeatTime > HEARTBEAT_INTERVAL * 3) {
                    NSLOG(@"[TCPClient] 心跳超时，断开连接");
                    [self handleDisconnection];
                    break;
                }
                // 非阻塞模式下没有数据可读，休眠一下避免CPU占用过高
                usleep(50000); // 50ms
                continue;
            } else {
                NSLOG(@"[TCPClient] 服务器异常断开连接: error=%s", strerror(errno));
                [self handleDisconnection];
                break;
            }
        }
    }

    NSLOG(@"[TCPClient] 接收消息线程结束");
}

- (void)handleReceivedMessage:(NSString *)message {
    NSLOG(@"[TCPClient] 收到消息: %@", message);
    
    @try {
        NSData *data = [message dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *messageDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
        
        if (!messageDict) {
            NSLOG(@"[TCPClient] 无效的JSON消息");
            return;
        }
        
        NSString *action = messageDict[@"action"];
        
        if ([action isEqualToString:@"pong"]) {
            NSLOG(@"[TCPClient] 收到心跳响应");
        }
        else if ([action isEqualToString:@"server_ready"]) {
            NSLOG(@"[TCPClient] 服务器就绪通知");
        }
        else if ([action isEqualToString:@"search_result"]) {
            NSString *keyword = messageDict[@"keyword"];
            NSString *result = messageDict[@"result"];
            NSLOG(@"[TCPClient] 搜索结果 - 关键词: %@, 结果: %@", keyword, result);
        }
        else if ([action isEqualToString:@"command_result"]) {
            NSString *command = messageDict[@"command"];
            NSString *result = messageDict[@"result"];
            NSLOG(@"[TCPClient] 命令结果 - 命令: %@, 结果: %@", command, result);
        }
        else {
            NSLOG(@"[TCPClient] 未知消息类型: %@", action);
        }
        
    } @catch (NSException *exception) {
        NSLOG(@"[TCPClient] 处理消息异常: %@", exception.reason);
    }
}

- (void)handleDisconnection {
    self.isConnected = NO;
    
    if (self.clientSocket >= 0) {
        close(self.clientSocket);
        self.clientSocket = -1;
    }
    
    [self stopHeartbeat];
    
    // 如果需要重连，启动重连定时器
    if (self.shouldReconnect) {
        [self startReconnectTimer];
    }
}

- (BOOL)sendMessage:(NSDictionary *)messageDict {
    if (!self.isConnected || self.clientSocket < 0) {
        NSLOG(@"[TCPClient] 未连接到服务器，无法发送消息");
        return NO;
    }
    
    @try {
        // 添加消息ID
        NSMutableDictionary *mutableDict = [messageDict mutableCopy];
        if (!mutableDict[@"messageId"]) {
            mutableDict[@"messageId"] = [NSString stringWithFormat:@"msg_%lu", (unsigned long)++self.messageIdCounter];
        }
        
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:mutableDict options:0 error:nil];
        if (!jsonData) {
            NSLOG(@"[TCPClient] JSON序列化失败");
            return NO;
        }
        
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        // 添加换行符作为消息分隔符
        NSString *messageWithNewline = [jsonString stringByAppendingString:@"\n"];
        const char *data = [messageWithNewline UTF8String];

        ssize_t bytesSent = send(self.clientSocket, data, strlen(data), 0);
        if (bytesSent < 0) {
            NSLOG(@"[TCPClient] 发送消息失败");
            [self handleDisconnection];
            return NO;
        }
        
        NSLOG(@"[TCPClient] 发送消息: %@", jsonString);
        return YES;
        
    } @catch (NSException *exception) {
        NSLOG(@"[TCPClient] 发送消息异常: %@", exception.reason);
        return NO;
    }
}

- (void)startHeartbeat {
    [self stopHeartbeat];
    
    self.heartbeatTimer = [NSTimer scheduledTimerWithTimeInterval:HEARTBEAT_INTERVAL
                                                           target:self
                                                         selector:@selector(sendHeartbeat)
                                                         userInfo:nil
                                                          repeats:YES];
}

- (void)stopHeartbeat {
    if (self.heartbeatTimer) {
        [self.heartbeatTimer invalidate];
        self.heartbeatTimer = nil;
    }
}

- (void)sendHeartbeat {
    // 🔧 发送心跳前检查连接状态
    if (!self.isConnected || self.clientSocket < 0) {
        NSLOG(@"[TCPClient] 连接已断开，停止心跳");
        [self stopHeartbeat];
        return;
    }

    BOOL success = [self sendMessage:@{
        @"action": @"ping",
        @"timestamp": @([[NSDate date] timeIntervalSince1970])
    }];

    if (!success) {
        NSLOG(@"[TCPClient] 心跳发送失败，触发重连");
        [self handleDisconnection];
    }
}

- (void)startReconnectTimer {
    [self stopReconnectTimer];

    // 🔧 检查重连次数限制
    if (self.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        NSLOG(@"[TCPClient] 达到最大重连次数 (%d)，停止重连", MAX_RECONNECT_ATTEMPTS);
        self.shouldReconnect = NO;
        return;
    }

    // 🔧 指数退避重连间隔
    NSTimeInterval interval = RECONNECT_INTERVAL * (1 << MIN(self.reconnectAttempts, 4));
    NSLOG(@"[TCPClient] %.1f秒后尝试第%lu次重连", interval, (unsigned long)self.reconnectAttempts + 1);

    self.reconnectTimer = [NSTimer scheduledTimerWithTimeInterval:interval
                                                           target:self
                                                         selector:@selector(attemptReconnect)
                                                         userInfo:nil
                                                          repeats:NO];
}

- (void)stopReconnectTimer {
    if (self.reconnectTimer) {
        [self.reconnectTimer invalidate];
        self.reconnectTimer = nil;
    }
}

- (void)attemptReconnect {
    NSLOG(@"[TCPClient] 尝试重连服务器");
    [self connectToServer];
}

- (void)disconnect {
    self.shouldReconnect = NO;
    [self stopHeartbeat];
    [self stopReconnectTimer];
    
    if (self.isConnected) {
        [self sendMessage:@{@"action": @"disconnect"}];
    }
    
    [self handleDisconnection];
    NSLOG(@"[TCPClient] 已断开连接");
}

// 公共接口方法
- (void)searchKeyword:(NSString *)keyword {
    [self sendMessage:@{
        @"action": @"search",
        @"keyword": keyword ?: @""
    }];
}

- (void)executeCommand:(NSString *)command {
    [self sendMessage:@{
        @"action": @"command",
        @"command": command ?: @""
    }];
}

@end

// 全局客户端实例
static TCPClient *globalTCPClient = nil;

// 获取客户端实例
static TCPClient *getTCPClient() {
    if (!globalTCPClient) {
        globalTCPClient = [[TCPClient alloc] init];
    }
    return globalTCPClient;
}

// 公共接口函数
static void connectToTCPServer() {
    TCPClient *client = getTCPClient();
    [client connectToServer];
}

static void disconnectFromTCPServer() {
    if (globalTCPClient) {
        [globalTCPClient disconnect];
    }
}

static void sendSearchRequest(NSString *keyword) {
    TCPClient *client = getTCPClient();
    [client searchKeyword:keyword];
}

static void sendCommandRequest(NSString *command) {
    TCPClient *client = getTCPClient();
    [client executeCommand:command];
}

// 构造函数 - 京东APP启动时自动执行
__attribute__((constructor))
static void initClient() {
    NSLOG(@"----------------------");
    NSLOG(@"[Client] 京东APP TCP客户端初始化");
    
    // 延迟连接，确保服务器已启动
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        connectToTCPServer();

        // 减少测试消息发送，避免频繁通信导致发热
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSLOG(@"[Client] 发送测试消息");
            sendSearchRequest(@"iPhone手机");

            // 间隔发送第二条消息
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                sendCommandRequest(@"test_command");
            });
        });
    });
}

// 析构函数 - 京东APP退出时执行
__attribute__((destructor))
static void cleanupClient() {
    NSLOG(@"[Client] 京东APP TCP客户端清理");
    disconnectFromTCPServer();
}
