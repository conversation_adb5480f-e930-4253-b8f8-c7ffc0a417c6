/**
 * @file DataCrypto.c
 * @brief 数据加密解密核心实现
 *
 * 提供高安全性的数据加密和解密功能，包含多层安全措施和完整的内存管理。
 * 支持时间戳生成、密钥强化、数据加密、数字签名等完整的安全流程。
 *
 * <AUTHOR> Team
 * @version 1.0
 * @date 2024
 */

#include <unistd.h>
#include <ctype.h>
#include <stdlib.h>      // malloc, free
#include <string.h>      // strlen, memset
#include <stdbool.h>     // bool类型
#include <stdint.h>      // uint8_t类型
#include <stdio.h>       // sprintf
#include <time.h>        // time()
#include "cJSON.h"       // JSON处理
#include "DataCrypto.h"  // 自身头文件
#include "StringCryptor_v2.h"   // 字符串加密
#include "Signature.h"       // 签名功能
#include "KeyCryptor.h"      // 密钥加密
#include "ParamsCryptor.h"   // 参数加密
#include "Log.h"

static const uint8_t SIGNATURE_KEYS[] = {
    0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 
    0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 
    0x4C, 0x1F, 0xA4, 0xBB, 0x75, 0x16, 0x6E, 0x78, 
    0x7A, 0xB3, 0x31, 0x31
};

/**
 * @brief 保护密钥常量
 *
 * 用于时间戳密钥的二次保护加密的固定密钥。
 * 在加密和解密过程中都会使用相同的密钥来确保一致性。
 * 原始字符串: 67Ft2<%v5 >0k1WCN8z l{#3[x2+XMZn
 *
 * @note 这是一个52字节的固定密钥数组
 * @note 密钥内容经过精心设计，具有良好的随机性和安全性
 * @warning 修改此密钥会导致之前加密的数据无法解密
 */
static const uint8_t PROTECTION_KEYS[] = {
    0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
    0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A,
    0xB1, 0x42, 0x19, 0x38, 0xB7, 0xF2, 0x38, 0x4B,
    0xA2, 0x13, 0x9C, 0xBB, 0xD6, 0x95, 0xF6, 0x3E,
    0x8B, 0xAB, 0x3C, 0x4F, 0x65, 0x18, 0xCE, 0x5A,
    0x25, 0x8F, 0xE6, 0x29, 0xAD, 0x05, 0xE3, 0x5B,
    0xB5, 0x1F, 0x52, 0xCD
};

/**
 * @brief 字符串到十六进制转换实现
 *
 * 将输入字符串的每个字节转换为对应的十六进制表示。
 * 使用标准的sprintf函数确保转换的准确性和一致性。
 *
 * @param str 输入字符串指针
 * @return 转换后的十六进制字符串，失败时返回NULL
 *
 * @implementation
 * - 首先验证输入参数的有效性
 * - 计算所需的内存大小（原长度*2+1）
 * - 逐字节进行十六进制转换
 * - 使用sprintf确保格式正确性
 */
char* string_to_hex(const char *str){
    if (!str) {
        LOG("string_to_hex: 输入参数为空");
        return NULL;
    }

    size_t len = strlen(str);

    // 防止整数溢出：检查输入长度
    if (len > 5120) {  // 限制最大输入长度为5KB
        LOG("string_to_hex: 输入字符串过长");
        return NULL;
    }

    // 检查乘法是否会溢出
    if (len > SIZE_MAX / 2 - 1) {
        LOG("string_to_hex: 长度计算溢出");
        return NULL;
    }

    // 每个字节需要2个十六进制字符 + 字符串结束符
    char *hex_str = malloc(len * 2 + 1);
    if (!hex_str) {
        LOG("string_to_hex: 内存分配失败");
        return NULL;
    }

    // 逐字节转换为十六进制
    for (size_t i = 0; i < len; ++i) {
        sprintf(hex_str + i * 2, "%02x", (unsigned char)str[i]);
    }
    hex_str[len * 2] = '\0';

    return hex_str;
}

/**
 * @brief 十六进制到字符串转换实现
 *
 * 将十六进制字符串转换回原始字节序列。
 * 包含完整的输入验证和错误处理机制。
 *
 * @param hex 十六进制字符串指针
 * @return 转换后的原始字符串，失败时返回NULL
 *
 * @implementation
 * - 验证输入参数不为NULL
 * - 检查字符串长度必须为偶数
 * - 验证每个字符都是有效的十六进制字符
 * - 使用strtol进行安全的字符转换
 * - 提供详细的错误日志记录
 */
char* hex_to_string(const char *hex) {
    if (!hex) {
        LOG("hex_to_string: 输入参数为空");
        return NULL;
    }

    size_t len = strlen(hex);

    // 防止过长的输入
    if (len > 10240) {  // 限制最大十六进制字符串长度为10KB
        LOG("hex_to_string: 十六进制字符串过长");
        return NULL;
    }

    if (len % 2 != 0) {
        LOG("hex_to_string: 十六进制字符串长度必须为偶数");
        return NULL;
    }

    size_t str_len = len / 2;
    char *str = malloc(str_len + 1);
    if (!str) {
        LOG("hex_to_string: 内存分配失败");
        return NULL;
    }

    // 逐对字符转换为字节
    for (size_t i = 0; i < str_len; ++i) {
        char byte_str[3] = { hex[i * 2], hex[i * 2 + 1], '\0' };

        // 验证是否为有效的十六进制字符
        if (!isxdigit(byte_str[0]) || !isxdigit(byte_str[1])) {
            LOG("hex_to_string: 包含无效的十六进制字符");
            free(str);
            return NULL;
        }

        // 转换为字节值
        str[i] = (char) strtol(byte_str, NULL, 16);
    }
    str[str_len] = '\0';

    return str;
}

/**
 * @brief 安全释放加密请求数据结构体内存实现
 *
 * 安全地释放EncryptRequestData结构体中所有动态分配的内存。
 * 采用防御性编程策略，确保内存释放的安全性和完整性。
 *
 * @param result 要释放的结构体指针
 *
 * @implementation
 * - 首先检查结构体指针是否为NULL（防御性编程）
 * - 逐个检查并释放每个字符串字段
 * - 释放后立即将指针设置为NULL（防止悬空指针）
 * - 最后重置状态字段为false
 * - 不释放结构体本身的内存（由调用者管理）
 *
 * @safety
 * - NULL指针安全：对NULL指针调用不会产生任何副作用
 * - 重复调用安全：多次调用同一个结构体不会产生问题
 * - 悬空指针防护：释放后立即置NULL防止误用
 */
void free_encrypt_request_data(EncryptRequestData *result) {
    if (!result) {
        return; // NULL指针安全处理
    }

    // 安全释放所有动态分配的内存
    if (result->key) {
        free(result->key);
        result->key = NULL;
    }

    if (result->sign) {
        free(result->sign);
        result->sign = NULL;
    }

    if (result->data) {
        free(result->data);
        result->data = NULL;
    }

    // 重置状态
    result->status = false;
}

/**
 * @brief 高安全性加密请求数据函数实现
 *
 * 执行完整的多层加密流程，包含时间戳生成、密钥强化、数据加密和数字签名。
 * 采用多重安全措施和严格的内存管理，确保加密过程的安全性和可靠性。
 *
 * @param request_data 原始请求数据（JSON格式字符串）
 * @return EncryptRequestData 加密结果结构体
 *
 * @implementation
 * 加密流程包含以下步骤：
 * 1. 参数验证和初始化
 * 2. 生成当前时间戳并转换为十六进制
 * 3. 对时间戳进行字符串加密
 * 4. 执行MSG加密增强安全性
 * 5. 使用保护密钥进行二次加密
 * 6. 转换为十六进制格式便于传输
 * 7. 使用最终密钥加密原始数据
 * 8. 生成数字签名确保完整性
 * 9. 统一的错误处理和内存清理
 *
 * @memory_management
 * - 使用goto cleanup模式确保所有路径都能正确清理内存
 * - 成功时将内存所有权转移给返回结构体
 * - 失败时在cleanup中安全释放所有临时内存
 * - 对敏感数据进行安全清零处理
 *
 * @security_features
 * - 时间戳基础密钥生成
 * - 多层加密保护（字符串加密+MSG加密+保护密钥加密）
 * - 十六进制编码增强传输安全性
 * - 数字签名确保数据完整性
 * - 内存安全清零防止敏感数据泄露
 */
EncryptRequestData encrypt_request_data(const char *request_data) {
    // 初始化结果结构体
    EncryptRequestData result = { false, NULL, NULL, NULL };

    // 临时变量声明（用于统一清理）
    char* hex_key = NULL;
    char* protected_hex_key = NULL;
    uint8_t* key_bytes = NULL;
    char* encrypt_data_key = NULL;
    char* data_encrypt_key_hex = NULL;
    char* encrypted_c = NULL;
    char* signature = NULL;
    char* temp_protected_key = NULL;
    char* signature_key = NULL;
    size_t hex_key_len = 0;  // 保存hex_key长度

    // 参数验证
    if (!request_data) {
        goto cleanup;
    }

    // 检查请求数据长度，防止过大的输入
    size_t request_data_len = strlen(request_data);
    if (request_data_len > 8192) {  // 限制最大请求数据为8KB
        LOG("encrypt_request_data: 请求数据过大");
        goto cleanup;
    }

    // 生成时间戳hex
    time_t now = time(NULL);
    char ts_str[20];
    snprintf(ts_str, sizeof(ts_str), "%ld", now);

    hex_key = string_to_hex(ts_str);
    if (!hex_key) goto cleanup;
    hex_key_len = strlen(hex_key);  // 保存长度

    // 对时间戳hex进行字符串加密
    key_bytes = encrypt_string_v2(hex_key);
    if (!key_bytes) goto cleanup;

    // MSG加密
    size_t key_bytes_len = calculate_encrypted_size_v2(hex_key);
    encrypt_data_key = msg_encryption_with_key((const uint8_t*)hex_key, strlen(hex_key), key_bytes, key_bytes_len);
    if (!encrypt_data_key) goto cleanup;

    // 对时间戳hex进行保护加密
    temp_protected_key = msg_encryption_with_key((const uint8_t*)hex_key, strlen(hex_key), PROTECTION_KEYS, sizeof(PROTECTION_KEYS));
    if (!temp_protected_key) goto cleanup;

    // 将保护密钥转换为hex字符串
    protected_hex_key = string_to_hex(temp_protected_key);
    if (!protected_hex_key) goto cleanup;

    // 转换为hex
    data_encrypt_key_hex = string_to_hex(encrypt_data_key);
    if (!data_encrypt_key_hex) goto cleanup;

    // 加密请求数据
    encrypted_c = encrypt_request_params_with_hex_key((const uint8_t*)request_data, strlen(request_data), data_encrypt_key_hex);
    if (!encrypted_c) goto cleanup;

    // 生成签名
    signature_key = decrypt_string_v2(SIGNATURE_KEYS, sizeof(SIGNATURE_KEYS));
    if (!signature_key) goto cleanup;
    signature = get_secure_signature(encrypted_c, signature_key);
    if (!signature) goto cleanup;

    // 设置返回结果
    result.status = true;
    result.key = protected_hex_key;
    result.data = encrypted_c;
    result.sign = signature;

    // 防止cleanup时释放已转移的内存
    protected_hex_key = NULL;
    encrypted_c = NULL;
    signature = NULL;

cleanup:
    // 统一清理临时变量
    if (hex_key) {
        memset(hex_key, 0, hex_key_len);
        free(hex_key);
    }
    if (key_bytes) {
        // 使用保存的长度来计算加密大小
        size_t key_len = hex_key_len > 0 ? (16 + hex_key_len + 4) : 64; // 安全的默认值
        memset(key_bytes, 0, key_len);
        free(key_bytes);
    }
    if (encrypt_data_key) {
        memset(encrypt_data_key, 0, strlen(encrypt_data_key));
        free(encrypt_data_key);
    }
    if (data_encrypt_key_hex) {
        memset(data_encrypt_key_hex, 0, strlen(data_encrypt_key_hex));
        free(data_encrypt_key_hex);
    }
    if (temp_protected_key) {
        memset(temp_protected_key, 0, strlen(temp_protected_key));
        free(temp_protected_key);
    }
    if (signature_key) {
        memset(signature_key, 0, strlen(signature_key));
        free(signature_key);
    }

    // 如果失败，清理可能已分配的内存
    if (!result.status) {
        if (protected_hex_key) free(protected_hex_key);
        if (encrypted_c) free(encrypted_c);
        if (signature) free(signature);
    }

    return result;
}

/**
 * @brief 安全释放解密结果结构体内存实现
 *
 * 安全地释放DecryptResult结构体中所有动态分配的内存。
 * 特别处理cJSON对象的释放，确保内存管理的正确性。
 *
 * @param result 要释放的结构体指针
 *
 * @implementation
 * - 首先检查结构体指针是否为NULL（防御性编程）
 * - 使用cJSON_Delete正确释放cJSON对象
 * - 使用free释放普通字符串内存
 * - 释放后立即将指针设置为NULL（防止悬空指针）
 * - 最后重置状态字段为false
 *
 * @safety
 * - NULL指针安全：对NULL指针调用不会产生任何副作用
 * - cJSON安全：使用cJSON_Delete而不是free来释放cJSON对象
 * - 重复调用安全：多次调用同一个结构体不会产生问题
 * - 悬空指针防护：释放后立即置NULL防止误用
 *
 * @note
 * - cJSON对象必须使用cJSON_Delete释放，不能使用普通的free
 * - timestamp字段可能为NULL，函数会安全处理这种情况
 * - 该函数不会释放结构体本身的内存（由调用者管理）
 */
void free_decrypt_result(DecryptResult *result) {
    if (!result) {
        return; // NULL指针安全处理
    }

    // 安全释放所有动态分配的内存
    if (result->data) {
        cJSON_Delete(result->data);
        result->data = NULL;
    }

    if (result->timestamp) {
        free(result->timestamp);
        result->timestamp = NULL;
    }

    // 重置状态
    result->status = false;
}

/**
 * @brief 高安全性解密请求数据函数实现
 *
 * 执行完整的解密和验证流程，逆向加密过程以恢复原始数据。
 * 包含严格的安全验证、密钥重现和完整的错误处理机制。
 *
 * @param encrypted_data 加密后的数据（Base64格式）
 * @param key_hex 保护密钥（十六进制格式）
 * @param sign_hex 数字签名字符串（Base64格式）
 * @return DecryptResult 解密结果结构体
 *
 * @implementation
 * 解密流程包含以下步骤：
 * 1. 参数验证确保所有输入有效
 * 2. 数字签名验证确保数据完整性
 * 3. 解码十六进制保护密钥
 * 4. 使用保护密钥解密得到原始时间戳
 * 5. 时间戳验证防止重放攻击（时间差不超过2秒）
 * 6. 重现加密过程中的密钥生成步骤
 * 7. 使用重现的密钥解密原始数据
 * 8. 解析JSON数据并验证格式
 * 9. 返回结构化的解密结果
 *
 * @security_verification
 * - 数字签名验证：确保数据未被篡改
 * - 时间戳验证：防止重放攻击，检查请求时效性
 * - 密钥验证：确保密钥格式和内容正确
 * - 解密验证：确保解密过程成功
 * - JSON验证：确保解密后的数据格式正确
 *
 * @memory_management
 * - 使用goto cleanup模式确保内存安全
 * - 对所有敏感数据进行安全清零
 * - 成功时将cJSON对象所有权转移给返回结构体
 * - 失败时安全释放所有临时分配的内存
 *
 * @error_handling
 * - 任何步骤失败都会跳转到cleanup进行清理
 * - 返回的结构体status字段指示操作是否成功
 * - 失败时data字段为NULL，timestamp字段为NULL
 */
DecryptResult decrypt_request_data(const char *encrypted_data, const char *key_hex, const char *sign_hex) {
    // 初始化结果结构体
    DecryptResult result = { false, NULL, NULL };

    // 临时变量声明
    char* signature_key = NULL;
    char* original_hex_key = NULL;
    uint8_t* key_bytes = NULL;
    char* encrypt_data_key = NULL;
    char* data_encrypt_key_hex = NULL;
    char* decrypted_result = NULL;
    char* protected_bytes = NULL;
    char* timestamp_str = NULL;
    size_t original_hex_key_len = 0;  // 保存original_hex_key长度

    // 参数验证
    if (!encrypted_data || !key_hex || !sign_hex) {
        return result;
    }

    // 检查输入数据长度，防止过大的输入
    if (strlen(encrypted_data) > 16384 || strlen(key_hex) > 1024 || strlen(sign_hex) > 1024) {
        LOG("decrypt_request_data: 输入数据过大");
        return result;
    }

    // 验证签名
    // const char* signature_key = "MyKey123";
    signature_key = decrypt_string_v2(SIGNATURE_KEYS, sizeof(SIGNATURE_KEYS));
    if (!signature_key) goto cleanup;
    if (!verify_secure_signature(encrypted_data, signature_key, sign_hex)) {
        goto cleanup;
    }

    // 步骤1: 解密保护密钥，恢复原始时间戳hex
    // 先将hex字符串转换为字节数组
    protected_bytes = hex_to_string(key_hex);
    if (!protected_bytes) goto cleanup;

    // 然后进行MSG解密
    size_t original_len = 0;
    original_hex_key = msg_decryption_with_key(protected_bytes, &original_len, PROTECTION_KEYS, sizeof(PROTECTION_KEYS));
    if (!original_hex_key) goto cleanup;
    original_hex_key_len = strlen(original_hex_key);  // 保存长度

    // 步骤1.5: 时间戳验证 - 防止重放攻击
    // 将十六进制时间戳转换回原始时间戳字符串
    timestamp_str = hex_to_string(original_hex_key);
    if (!timestamp_str) {
        LOG("❌ 时间戳hex解码失败");
        goto cleanup;
    }

    // 将时间戳字符串转换为time_t
    time_t request_time = (time_t)strtol(timestamp_str, NULL, 10);
    if (request_time == 0) {
        LOG("❌ 时间戳格式无效: %s", timestamp_str);
        goto cleanup;
    }

    // 获取当前时间
    time_t current_time = time(NULL);

    // 计算时间差（绝对值）
    time_t time_diff = current_time - request_time;
    if (time_diff < 0) time_diff = -time_diff;  // 取绝对值
    // 检查时间差是否超过允许的阈值
    if (time_diff > 3) {
        LOG("❌ 请求时间戳验证失败 - 时间差过大: %ld秒 (请求时间: %ld, 当前时间: %ld)",
               time_diff, request_time, current_time);
        goto cleanup;
    }

    LOG("✅ 时间戳验证通过 - 时间差: %ld秒", time_diff);

    // 保存时间戳字符串到结果中（可选）
    result.timestamp = timestamp_str;  // 将所有权转移给result

    // 步骤2: 重现密钥字符串加密过程（使用原始密钥）
    key_bytes = encrypt_string_v2(original_hex_key);
    if (!key_bytes) goto cleanup;

    // 步骤3: 重现MSG加密过程（使用原始密钥）
    size_t key_bytes_len = calculate_encrypted_size_v2(original_hex_key);
    encrypt_data_key = msg_encryption_with_key((const uint8_t*)original_hex_key, strlen(original_hex_key), key_bytes, key_bytes_len);
    if (!encrypt_data_key) goto cleanup;

    // 步骤4: 将MSG加密结果转换为hex
    data_encrypt_key_hex = string_to_hex(encrypt_data_key);
    if (!data_encrypt_key_hex) goto cleanup;

    // 步骤5: 使用重现的密钥解密数据
    size_t decrypted_len = 0;
    uint8_t* decrypted_bytes = decrypt_request_params_with_hex_key(encrypted_data, &decrypted_len, data_encrypt_key_hex);
    if (!decrypted_bytes || decrypted_len == 0) goto cleanup;

    // 检查解密后的数据长度，防止过大的分配
    if (decrypted_len > 10240) {  // 限制最大解密数据为10KB
        LOG("decrypt_request_data: 解密数据过大");
        free(decrypted_bytes);
        goto cleanup;
    }

    // 转换为字符串
    decrypted_result = malloc(decrypted_len + 1);
    if (!decrypted_result) {
        free(decrypted_bytes);
        goto cleanup;
    }

    memcpy(decrypted_result, decrypted_bytes, decrypted_len);
    decrypted_result[decrypted_len] = '\0';
    free(decrypted_bytes);

    // 解析JSON
    cJSON* json_data = cJSON_Parse(decrypted_result);
    if (!json_data) goto cleanup;

    // 设置成功结果
    result.status = true;
    result.data = json_data;
    // result.timestamp 已经在时间戳验证步骤中设置

cleanup:
    // 清理临时变量
    if (signature_key) {
        memset(signature_key, 0, strlen(signature_key));
        free(signature_key);
    }
    if (original_hex_key) {
        memset(original_hex_key, 0, original_hex_key_len);
        free(original_hex_key);
    }
    if (key_bytes) {
        // 使用保存的长度来计算加密大小
        size_t key_len = original_hex_key_len > 0 ? (16 + original_hex_key_len + 4) : 64; // 安全的默认值
        memset(key_bytes, 0, key_len);
        free(key_bytes);
    }
    if (encrypt_data_key) {
        memset(encrypt_data_key, 0, strlen(encrypt_data_key));
        free(encrypt_data_key);
    }
    if (data_encrypt_key_hex) {
        memset(data_encrypt_key_hex, 0, strlen(data_encrypt_key_hex));
        free(data_encrypt_key_hex);
    }
    if (decrypted_result) {
        free(decrypted_result);
    }
    if (protected_bytes) {
        memset(protected_bytes, 0, strlen(protected_bytes));
        free(protected_bytes);
    }
    if (timestamp_str && result.timestamp != timestamp_str) {
        // 只有当timestamp_str没有被转移给result时才释放
        free(timestamp_str);
    }

    // 如果失败，清理可能已分配的内存
    if (!result.status) {
        if (result.data) {
            cJSON_Delete(result.data);
            result.data = NULL;
        }
        if (result.timestamp) {
            free(result.timestamp);
            result.timestamp = NULL;
        }
        LOG("❌ 反向解密流程失败，已清理所有资源");
    }

    return result;
}

