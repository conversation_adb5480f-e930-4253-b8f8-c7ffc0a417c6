#ifndef HTTP_SERVICE_H
#define HTTP_SERVICE_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// HTTP请求回调函数类型
// 参数：method(GET/POST等), path(请求路径), headers(HTTP头部), body(请求体), client_socket(客户端socket)
// 返回：0=继续处理, 1=已处理完成
typedef int (*http_request_handler_t)(const char* method, const char* path, const char* headers, const char* body, int client_socket);

// HTTP服务配置结构
typedef struct {
    uint16_t port;                          // 监听端口
    const char* bind_address;               // 绑定地址 (NULL = 127.0.0.1, "0.0.0.0" = 所有接口)
    http_request_handler_t request_handler; // 请求处理回调函数 (可选)
    const char* service_name;               // 服务名称 (用于日志, 可选)
} http_service_config_t;

/**
 * 启动HTTP服务
 * @param config 服务配置
 * @return 0=成功, -1=失败
 */
int http_service_start(const http_service_config_t* config);

/**
 * 停止HTTP服务
 * @return 0=成功, -1=失败
 */
int http_service_stop(void);

/**
 * 发送HTTP响应
 * @param client_socket 客户端socket
 * @param status_code HTTP状态码 (如200, 404)
 * @param content_type 内容类型 (如"application/json")
 * @param body 响应体内容
 * @return 发送的字节数，-1表示失败
 */
int http_service_send_response(int client_socket, int status_code,
                              const char* content_type, const char* body);

/**
 * 发送JSON响应 (便捷函数)
 * @param client_socket 客户端socket
 * @param status_code HTTP状态码
 * @param json_body JSON字符串
 * @return 发送的字节数，-1表示失败
 */
int http_service_send_json(int client_socket, int status_code, const char* json_body);

/**
 * 创建默认配置
 * @param port 监听端口
 * @return 默认配置结构
 */
http_service_config_t http_service_create_default_config(uint16_t port);


/**
 * 开启防休眠
 * @param reason 防休眠原因
 * @return 0=成功, -1=失败
 */
int enable_no_sleep(const char *reason);

/**
 * 关闭防休眠
 */
void disable_no_sleep();

// int verify_request_signature(const char* headers);
// HTTP状态码常量
#define HTTP_STATUS_OK                200
#define HTTP_STATUS_BAD_REQUEST       400
#define HTTP_STATUS_NOT_FOUND         404
#define HTTP_STATUS_INTERNAL_ERROR    500

#ifdef __cplusplus
}
#endif

#endif // HTTP_SERVICE_H
