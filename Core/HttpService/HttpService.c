#include "HttpService.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <signal.h>
#include <sys/select.h>
#include <time.h>
#include <IOKit/pwr_mgt/IOPMLib.h>
#include "Log.h"

static IOPMAssertionID g_assertion_id = kIOPMNullAssertionID;


#define BUFFER_SIZE 4096                    // 缓冲区大小
#define RESPONSE_BUFFER_SIZE 8192           // 响应缓冲区大小

// 🔧 HTTP请求验证相关常量
#define MIN_HTTP_REQUEST_SIZE 14            // 最小HTTP请求长度: "GET / HTTP/1.1"
#define MAX_PATH_LENGTH 200                 // 最大路径长度
#define MAX_BODY_SIZE 10240                 // 最大请求体大小 (10KB)
#define SOCKET_TIMEOUT_SEC 10               // Socket超时时间 (秒)


// 🔧 极简HTTP服务状态
static volatile int g_server_socket = -1;
static volatile int g_is_running = 0;
static http_service_config_t g_config = {0};

// 内部函数声明
static int setup_http_server_socket(const http_service_config_t* config);
static void* http_server_thread_func(void* arg);
static void handle_http_client(void* arg);
static const char* get_status_text(int status_code);
static int is_malicious_request(const char* buffer);
static int is_valid_json_format(const char* body);

// 开启防休眠
int enable_no_sleep(const char *reason) {
    if (g_assertion_id != kIOPMNullAssertionID) {
        LOG("已经启用防休眠，无需重复调用。");
        return 0;
    }

    CFStringRef reasonStr = CFStringCreateWithCString(NULL,
                                                      reason ? reason : "KeepAlive",
                                                      kCFStringEncodingUTF8);

    IOReturn ret = IOPMAssertionCreateWithName(
            kIOPMAssertionTypeNoIdleSleep,
            kIOPMAssertionLevelOn,
            reasonStr,
            &g_assertion_id);

    if (reasonStr) CFRelease(reasonStr);

    if (ret == kIOReturnSuccess) {
        LOG("防休眠已启用，ID=%u\n", g_assertion_id);
        return 0;
    } else {
        LOG("启用防休眠失败，错误码=%d\n", ret);
        g_assertion_id = kIOPMNullAssertionID;
        return -1;
    }
}

// 关闭防休眠
void disable_no_sleep() {
    if (g_assertion_id != kIOPMNullAssertionID) {
        IOReturn ret = IOPMAssertionRelease(g_assertion_id);
        if (ret == kIOReturnSuccess) {
            LOG("防休眠已关闭");
        } else {
            LOG("错误码=%d\n", ret);
        }
        g_assertion_id = kIOPMNullAssertionID;
    } else {
        LOG("没有开启防休眠，不需要关闭");
    }
}

// 设置HTTP服务器socket
static int setup_http_server_socket(const http_service_config_t* config) {
    int server_socket;
    struct sockaddr_in server_addr;
    int opt = 1;

    // 创建socket
    server_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (server_socket < 0) {
        LOG("创建socket失败: %s", strerror(errno));
        return -1;
    }

    // 设置socket选项
    if (setsockopt(server_socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        LOG("设置SO_REUSEADDR失败: %s", strerror(errno));
        close(server_socket);
        return -1;
    }

    // 启用keepalive
    if (setsockopt(server_socket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt)) < 0) {
        LOG("设置SO_KEEPALIVE失败: %s", strerror(errno));
    }

    // 设置缓冲区大小
    int buffer_size = 65536;
    setsockopt(server_socket, SOL_SOCKET, SO_RCVBUF, &buffer_size, sizeof(buffer_size));
    setsockopt(server_socket, SOL_SOCKET, SO_SNDBUF, &buffer_size, sizeof(buffer_size));

    // 绑定地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;

    // 根据配置设置绑定地址
    // if (config->bind_address == NULL || strcmp(config->bind_address, "127.0.0.1") == 0) {
    //     server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
    // } else if (strcmp(config->bind_address, "0.0.0.0") == 0) {
    //     server_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    // } else {
    //     server_addr.sin_addr.s_addr = inet_addr(config->bind_address);
    // }
    server_addr.sin_addr.s_addr = inet_addr(config->bind_address);

    server_addr.sin_port = htons(config->port);

    if (bind(server_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        LOG("❌❌❌❌绑定地址失败，端口: %d, 错误: %s", config->port, strerror(errno));
        close(server_socket);
        return -1;
    }

    // 开始监听
    if (listen(server_socket, 20) < 0) {
        LOG("监听失败: %s", strerror(errno));
        close(server_socket);
        return -1;
    }

    const char* service_name = config->service_name ? config->service_name : "HttpService";
    (void)service_name;
    LOG("[%s] ✅ HTTP服务器启动成功，监听端口: %d", service_name, config->port);
    return server_socket;
}

// 🔧 公共API实现

// 启动HTTP服务
int http_service_start(const http_service_config_t* config) {
    if (!config || config->port == 0) {
        LOG("无效的配置参数");
        return -1;
    }

    if (g_is_running) {
        LOG("HTTP服务已经在运行");
        return 0;
    }

    // 复制配置
    g_config = *config;

    // 设置服务器socket
    g_server_socket = setup_http_server_socket(config);
    if (g_server_socket < 0) {
        return -1;
    }

    g_is_running = 1;

    // 创建服务器线程
    pthread_t server_thread;
    if (pthread_create(&server_thread, NULL, http_server_thread_func, NULL) != 0) {
        LOG("创建HTTP服务器线程失败");
        close(g_server_socket);
        g_server_socket = -1;
        g_is_running = 0;
        return -1;
    }

    // 分离线程，让其自动清理
    pthread_detach(server_thread);

    return 0;
}

// HTTP服务器主线程
static void* http_server_thread_func(void* arg) {
    struct sockaddr_in client_addr;
    socklen_t client_len = sizeof(client_addr);

    const char* service_name = g_config.service_name ? g_config.service_name : "HttpService";
    (void)service_name;
    LOG("[%s] 🚀 HTTP服务器线程启动", service_name);

    while (g_is_running) {
        int client_socket = accept(g_server_socket, (struct sockaddr*)&client_addr, &client_len);

        if (client_socket < 0) {
            if (g_is_running && errno != EINTR) {
                LOG("[%s] ❌ 接受连接失败: %s", service_name, strerror(errno));
            }
            continue;
        }

        // 直接在新线程中处理HTTP客户端
        pthread_t client_thread;
        int* client_socket_ptr = malloc(sizeof(int));
        if (client_socket_ptr) {
            *client_socket_ptr = client_socket;
            if (pthread_create(&client_thread, NULL, (void*(*)(void*))handle_http_client, client_socket_ptr) == 0) {
                pthread_detach(client_thread);
            } else {
                free(client_socket_ptr);
                close(client_socket);
            }
        } else {
            close(client_socket);
        }
    }

    LOG("[%s] 🛑 HTTP服务器线程结束", service_name);
    return NULL;
}

// 🔧 处理HTTP客户端请求
static void handle_http_client(void* arg) {
    int client_socket = *(int*)arg;
    free(arg);

    LOG(" 🔗 新HTTP客户端连接: socket=%d", client_socket);

    // 🔧 确保socket在任何情况下都会被关闭
    int socket_closed = 0;

    // 设置socket选项
    int opt = 1;
    setsockopt(client_socket, SOL_SOCKET, SO_KEEPALIVE, &opt, sizeof(opt));
    setsockopt(client_socket, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));

    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = SOCKET_TIMEOUT_SEC;  // 使用常量定义的超时时间
    timeout.tv_usec = 0;
    setsockopt(client_socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));

    char buffer[BUFFER_SIZE];
    ssize_t bytes_read = recv(client_socket, buffer, sizeof(buffer) - 1, 0);

    if (bytes_read > 0) {
        buffer[bytes_read] = '\0';

        // 🔧 详细记录接收到的数据
        LOG(" 📥 接收到 %zd 字节数据", bytes_read);

        // 🔧 基本数据校验 - 检查是否包含基本的HTTP请求结构
        if (bytes_read < MIN_HTTP_REQUEST_SIZE) {  // 使用常量定义的最小请求长度
            LOG(" ❌ 请求数据过短，疑似无效请求");
            goto cleanup;
        }

        // 🔧 检查是否包含HTTP协议标识
        if (!strstr(buffer, "HTTP/")) {
            LOG(" ❌ 请求中未找到HTTP协议标识");
            goto cleanup;
        }

        // 🔧 检查是否为恶意请求
        if (is_malicious_request(buffer)) {
            LOG(" ❌ 检测到潜在恶意请求");
            goto cleanup;
        }

        // 解析HTTP请求
        char method[16] = {0};
        char path[256] = {0};
        char version[16] = {0};

        // 🔧 解析HTTP请求行: POST /path HTTP/1.1
        if (sscanf(buffer, "%15s %255s %15s", method, path, version) == 3) {
            const char* service_name = g_config.service_name ? g_config.service_name : "HttpService";
            (void)service_name;

            // 🔧 验证HTTP版本
            if (strcmp(version, "HTTP/1.1") != 0 && strcmp(version, "HTTP/1.0") != 0) {
                LOG("[%s] ❌ 不支持的HTTP版本: %s", service_name, version);
                goto cleanup;
            }

            // 🔧 验证路径长度和格式
            if (strlen(path) == 0 || path[0] != '/') {
                LOG("[%s] ❌ 无效的请求路径: %s", service_name, path);
                goto cleanup;
            }

            // 🔧 验证路径长度限制
            if (strlen(path) > MAX_PATH_LENGTH) {
                LOG("[%s] ❌ 请求路径过长: %zu 字符", service_name, strlen(path));
                goto cleanup;
            }

            // 🔧 只接受POST请求
            if (strcmp(method, "POST") != 0) {
                LOG("[%s] ❌ 只接受POST请求，收到: %s", service_name, method);
                goto cleanup;  // 直接关闭连接，不响应
            }

            LOG("[%s] 📨 POST请求: %s", service_name, path);

            // 🔧 分离HTTP头部和请求体
            char* headers_end = strstr(buffer, "\r\n\r\n");
            char* headers = buffer;
            char* body = "";

            if (headers_end) {
                *headers_end = '\0';  // 截断头部
                body = headers_end + 4;  // 请求体开始位置
            }

            // 🔧 检查头部是否包含可疑内容
            if (is_malicious_request(headers)) {
                LOG("[%s] ❌ 检测到恶意HTTP头部", service_name);
                goto cleanup;
            }

            // 🔧 检查头部长度限制
            if (strlen(headers) > 4096) {  // 4KB头部限制
                LOG("[%s] ❌ HTTP头部过大: %zu 字节", service_name, strlen(headers));
                goto cleanup;
            }

            // 🔧 验证Content-Type (如果有请求体)
            if (strlen(body) > 0) {
                char* content_type = strstr(headers, "Content-Type:");
                if (!content_type) {
                    LOG("[%s] ❌ POST请求缺少Content-Type头", service_name);
                    goto cleanup;
                }

                // 🔧 检查Content-Type是否为application/json
                if (!strstr(content_type, "application/json")) {
                    LOG("[%s] ❌ 不支持的Content-Type，只接受application/json", service_name);
                    goto cleanup;
                }
            }

            // 🔧 验证Content-Length (如果有请求体)
            if (strlen(body) > 0) {
                char* content_length_header = strstr(headers, "Content-Length:");
                if (content_length_header) {
                    int declared_length = 0;
                    if (sscanf(content_length_header, "Content-Length: %d", &declared_length) == 1) {
                        int actual_length = (int)strlen(body);
                        if (declared_length != actual_length) {
                            LOG("[%s] ❌ Content-Length不匹配: 声明=%d, 实际=%d",
                                   service_name, declared_length, actual_length);
                            goto cleanup;
                        }
                    }
                }
            }

            // 🔧 验证请求体大小限制
            if (strlen(body) > MAX_BODY_SIZE) {  // 使用常量定义的大小限制
                LOG("[%s] ❌ 请求体过大: %zu 字节", service_name, strlen(body));
                goto cleanup;
            }

            // 🔧 验证请求体是否为有效JSON格式
            if (strlen(body) > 0 && !is_valid_json_format(body)) {
                LOG("[%s] ❌ 请求体不是有效的JSON格式", service_name);
                goto cleanup;
            }

            // 🔧 记录通过所有验证的请求
            LOG("[%s] ✅ 请求通过所有验证检查:", service_name);
            LOG("[%s]    - HTTP协议格式验证 ✓", service_name);
            LOG("[%s]    - 恶意内容检测 ✓", service_name);
            LOG("[%s]    - 路径格式验证 ✓", service_name);
            LOG("[%s]    - Content-Type验证 ✓", service_name);
            LOG("[%s]    - 请求体大小验证 ✓", service_name);
            LOG("[%s]    - JSON格式验证 ✓", service_name);
            LOG("[%s] 🚀 传递给处理器: %s", service_name, path);

            // 🔧 必须有自定义处理器，否则直接关闭连接
            if (g_config.request_handler) {
                int handler_result = g_config.request_handler(method, path, headers, body, client_socket);
                if (handler_result == 1) {
                    // 自定义处理器已处理完成
                    LOG("[%s] ✅ 请求处理完成: %s", service_name, path);
                    goto cleanup;
                } else {
                    // 处理器返回0表示未处理
                    LOG("[%s] ❌ 处理器未处理请求: %s", service_name, path);
                    goto cleanup;
                }
            } else {
                // 没有配置处理器
                LOG("[%s] ❌ 未配置请求处理器", service_name);
                goto cleanup;
            }
        } else {
            LOG(" ❌ 无效的HTTP请求");
            goto cleanup;  // 直接关闭连接
        }
    } else if (bytes_read == 0) {
        LOG(" ⚠️ 客户端提前关闭连接");
    } else {
        LOG(" ❌ 读取HTTP请求失败: %s", strerror(errno));
    }

cleanup:
    // 安全关闭socket
    if (!socket_closed) {
        close(client_socket);
        socket_closed = 1;
    }
    LOG(" 🔌 HTTP客户端断开: socket=%d", client_socket);
}


// 停止HTTP服务
int http_service_stop(void) {
    if (!g_is_running) {
        return 0;
    }

    const char* service_name = g_config.service_name ? g_config.service_name : "HttpService";
    (void)service_name;
    LOG("[%s] 🛑 正在停止HTTP服务器...", service_name);
    g_is_running = 0;

    // 关闭服务器socket
    if (g_server_socket >= 0) {
        close(g_server_socket);
        g_server_socket = -1;
    }

    LOG("[%s] ✅ HTTP服务器已停止", service_name);
    return 0;
}

// 获取状态文本
static const char* get_status_text(int status_code) {
    switch (status_code) {
        case 200: return "OK";
        case 400: return "Bad Request";
        case 404: return "Not Found";
        case 500: return "Internal Server Error";
        default: return "Unknown";
    }
}

// 发送HTTP响应
int http_service_send_response(int client_socket, int status_code,
                              const char* content_type, const char* body) {
    char response[RESPONSE_BUFFER_SIZE];
    const char* status_text = get_status_text(status_code);

    int header_len = snprintf(response, sizeof(response),
        "HTTP/1.1 %d %s\r\n"
        "Content-Type: %s\r\n"
        "Content-Length: %zu\r\n"
        "Connection: close\r\n"
        "Server: HttpService/1.0\r\n"
        "\r\n",
        status_code, status_text, content_type ? content_type : "text/plain",
        body ? strlen(body) : 0);

    if (body && (header_len + strlen(body) < sizeof(response))) {
        strcat(response, body);
    }

    ssize_t bytes_sent = send(client_socket, response, strlen(response), MSG_NOSIGNAL);
    return (int)bytes_sent;
}

// 发送JSON响应
int http_service_send_json(int client_socket, int status_code, const char* json_body) {
    return http_service_send_response(client_socket, status_code, "application/json; charset=utf-8", json_body);
}

// 创建默认配置
http_service_config_t http_service_create_default_config(uint16_t port) {
    
    http_service_config_t config = {0};
    config.port = port;
    config.bind_address = NULL;  // 默认127.0.0.1
    config.request_handler = NULL;
    config.service_name = NULL;
    return config;
}

/**
 * @brief 检查是否为恶意请求
 *
 * 检查HTTP请求中是否包含常见的恶意模式，如路径遍历、脚本注入等。
 *
 * @param buffer HTTP请求缓冲区
 * @return 1表示疑似恶意请求，0表示正常请求
 */
static int is_malicious_request(const char* buffer) {
    if (!buffer) return 1;

    // 检查路径遍历攻击
    if (strstr(buffer, "../") || strstr(buffer, "..\\")) {
        return 1;
    }

    // 检查脚本注入
    if (strstr(buffer, "<script") || strstr(buffer, "javascript:") ||
        strstr(buffer, "vbscript:") || strstr(buffer, "onload=") ||
        strstr(buffer, "onerror=") || strstr(buffer, "onclick=")) {
        return 1;
    }

    // 检查SQL注入常见模式
    if (strstr(buffer, "' OR ") || strstr(buffer, "\" OR ") ||
        strstr(buffer, "UNION SELECT") || strstr(buffer, "DROP TABLE") ||
        strstr(buffer, "INSERT INTO") || strstr(buffer, "DELETE FROM")) {
        return 1;
    }

    // 检查命令注入
    if (strstr(buffer, "$(") || strstr(buffer, "`") ||
        strstr(buffer, "&&") || strstr(buffer, "||") ||
        strstr(buffer, ";rm ") || strstr(buffer, ";cat ")) {
        return 1;
    }

    // 检查过长的URL编码
    int percent_count = 0;
    for (const char* p = buffer; *p; p++) {
        if (*p == '%') {
            percent_count++;
            if (percent_count > 20) {  // 过多的URL编码可能是攻击
                return 1;
            }
        }
    }

    return 0;  // 看起来是正常请求
}

/**
 * @brief 验证JSON格式的基本有效性
 *
 * 进行基本的JSON格式检查，包括开始和结束字符的匹配。
 * 这是一个轻量级的检查，不进行完整的JSON解析。
 *
 * @param body 请求体字符串
 * @return 1表示格式有效，0表示格式无效
 */
static int is_valid_json_format(const char* body) {
    if (!body || strlen(body) == 0) {
        return 1;  // 空请求体被认为是有效的
    }

    // 跳过前导空白字符
    const char* trimmed_body = body;
    while (*trimmed_body == ' ' || *trimmed_body == '\t' ||
           *trimmed_body == '\n' || *trimmed_body == '\r') {
        trimmed_body++;
    }

    // 检查是否以有效的JSON字符开始
    if (*trimmed_body != '{' && *trimmed_body != '[') {
        return 0;
    }

    // 找到字符串的实际结尾（忽略尾随空白）
    size_t body_len = strlen(trimmed_body);
    if (body_len == 0) return 0;

    const char* end = trimmed_body + body_len - 1;
    while (end > trimmed_body && (*end == ' ' || *end == '\t' ||
           *end == '\n' || *end == '\r')) {
        end--;
    }

    // 检查是否以有效的JSON字符结束
    if (*end != '}' && *end != ']') {
        return 0;
    }

    // 简单的括号匹配检查
    int brace_count = 0;
    int bracket_count = 0;
    int in_string = 0;
    int escape_next = 0;

    for (const char* p = trimmed_body; p <= end; p++) {
        if (escape_next) {
            escape_next = 0;
            continue;
        }

        if (*p == '\\') {
            escape_next = 1;
            continue;
        }

        if (*p == '"') {
            in_string = !in_string;
            continue;
        }

        if (!in_string) {
            if (*p == '{') brace_count++;
            else if (*p == '}') brace_count--;
            else if (*p == '[') bracket_count++;
            else if (*p == ']') bracket_count--;

            // 如果计数变为负数，说明括号不匹配
            if (brace_count < 0 || bracket_count < 0) {
                return 0;
            }
        }
    }

    // 最终检查括号是否完全匹配
    return (brace_count == 0 && bracket_count == 0);
}


// ------------- 未优化未启用 -------------

// 🔧 提取HTTP头部中的指定字段值
// static const char* get_header_value(const char* headers, const char* header_name) {
//     static char header_value[256];
//     char search_pattern[64];

//     // 构造搜索模式，如 "Authorization:"
//     snprintf(search_pattern, sizeof(search_pattern), "%s:", header_name);

//     char* header_line = strstr(headers, search_pattern);
//     if (!header_line) {
//         return NULL;
//     }

//     // 跳过头部名称和冒号
//     header_line += strlen(search_pattern);

//     // 跳过空格
//     while (*header_line == ' ' || *header_line == '\t') {
//         header_line++;
//     }

//     // 提取值直到行结束
//     char* line_end = strstr(header_line, "\r\n");
//     if (!line_end) {
//         line_end = header_line + strlen(header_line);
//     }

//     size_t value_len = line_end - header_line;
//     if (value_len >= sizeof(header_value)) {
//         value_len = sizeof(header_value) - 1;
//     }

//     strncpy(header_value, header_line, value_len);
//     header_value[value_len] = '\0';

//     return header_value;
// }

// 🔧 验证签名
// int verify_request_signature(const char* headers) {
//     const char* signature = get_header_value(headers, "Authorization");

//     if (!signature) {
//         LOG("[JDHttpService] ❌ 缺少Authorization头部");
//         return 0;
//     }

//     LOG("[JDHttpService] 🔍 收到签名: %s", signature);

//     // 🔧 验证固定签名值 "kkkkkkk"
//     if (strcmp(signature, "kkkkkkk") == 0) {
//         LOG("[JDHttpService] ✅ 签名验证成功");
//         return 1;
//     } else {
//         LOG("[JDHttpService] ❌ 签名验证失败，期望: kkkkkkk，实际: %s", signature);
//         return 0;
//     }
// }

/**
 * // 🔧 首先验证签名
    if (!verify_request_signature(headers)) {
        http_service_send_json(client_socket, HTTP_STATUS_BAD_REQUEST,
                              "{\"error\":\"Invalid signature\",\"message\":\"Authorization failed\"}");
        return 1; // 已处理（拒绝）
    }
 */