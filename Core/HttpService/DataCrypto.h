/**
 * @file DataCrypto.h
 * @brief 高安全性数据加密解密核心库
 *
 * 提供完整的数据加密和解密功能，包含多层安全措施、数字签名验证、
 * 时间戳生成、密钥强化等高级安全特性。适用于需要高安全性的数据传输场景。
 *
 * @features
 * - 多层加密保护：时间戳+字符串加密+MSG加密+保护密钥加密
 * - 数字签名验证：确保数据完整性和真实性
 * - 十六进制编码：增强传输安全性
 * - 完整内存管理：防止内存泄漏和悬空指针
 * - 错误处理机制：详细的错误检查和日志记录
 *
 * @usage_example
 * @code
 * // 加密数据
 * const char* json_data = "{\"user\":\"test\",\"data\":\"example\"}";
 * EncryptRequestData encrypt_result = encrypt_request_data(json_data);
 * if (encrypt_result.status) {
 *     // 使用加密结果：encrypt_result.key, encrypt_result.data, encrypt_result.sign
 *
 *     // 解密数据
 *     DecryptResult decrypt_result = decrypt_request_data(
 *         encrypt_result.data, encrypt_result.key, encrypt_result.sign);
 *     if (decrypt_result.status) {
 *         // 使用解密后的JSON数据：decrypt_result.data
 *         char* json_string = cJSON_Print(decrypt_result.data);
 *         printf("解密结果: %s\n", json_string);
 *         free(json_string);
 *     }
 *     free_decrypt_result(&decrypt_result);
 * }
 * free_encrypt_request_data(&encrypt_result);
 * @endcode
 *
 * @warning
 * - 所有返回的结构体都必须使用对应的释放函数进行内存清理
 * - 不要手动释放结构体中的单个字段
 * - 加密和解密操作都是CPU密集型的，避免在性能敏感的场景中频繁调用
 *
 * @thread_safety
 * 本库的函数不是线程安全的，如果在多线程环境中使用，需要调用者自行处理同步。
 *
 * <AUTHOR> Team
 * @version 1.0
 * @date 2024
 */

#ifndef DATACRYPTO_H
#define DATACRYPTO_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "cJSON.h" // 包含cJSON头文件

/**
 * @brief 加密请求数据结构体
 *
 * 存储完整加密操作的结果，包含经过多层安全处理的数据、密钥和数字签名。
 * 所有字段都经过专门的安全编码处理，提供高强度的数据保护。
 *
 * @note 结构体中的所有指针字段都是动态分配的，需要使用对应的释放函数
 * @warning 不要手动释放单个字段，必须使用 free_encrypt_request_data() 函数
 */
typedef struct {
    bool status;        /**< 操作状态：true=加密成功，false=加密失败或发生错误 */
    char *key;          /**< 保护密钥（十六进制格式），经过时间戳+多层加密+十六进制编码处理 */
    char *sign;         /**< 数字签名（Base64格式），用于验证数据完整性和真实性 */
    char *data;         /**< 加密后的请求数据（Base64格式），包含原始JSON数据的加密结果 */
} EncryptRequestData;

/**
 * @brief 解密结果数据结构体
 *
 * 存储解密操作的结果，包含解密后的JSON数据和相关元信息。
 * 提供对解密数据的直接访问，支持进一步的数据处理和分析。
 *
 * @note data字段是cJSON对象，可以直接使用cJSON API进行操作
 * @note timestamp字段可能为NULL，取决于解密过程中是否提取时间戳
 * @warning 不要手动释放单个字段，必须使用 free_decrypt_result() 函数
 */
typedef struct {
    bool status;        /**< 操作状态：true=解密成功，false=解密失败或验证失败 */
    cJSON* data;        /**< 解密后的JSON数据对象，可直接使用cJSON API进行操作和查询 */
    char *timestamp;    /**< 提取的时间戳字符串，可能为NULL（由调用者决定是否提取） */
} DecryptResult;


/**
 * @brief 将字符串转换为十六进制字符串
 *
 * 将输入字符串的每个字节转换为对应的十六进制表示。
 * 这是一个基础的编码函数，为后续的加密流程提供支持。
 *
 * @param str 输入字符串，不能为NULL
 * @return 十六进制字符串，失败返回NULL
 *
 * @note 转换规则：每个字节用2个十六进制字符表示，使用小写字母(a-f)
 * @note 输出字符串长度为输入字符串长度的2倍
 * @warning 返回的内存需要调用者释放
 * @warning 函数不验证输入字符串的有效性，调用者需确保输入有效
 *
 * @example
 * char* result = string_to_hex("ABC");  // 返回 "414243"
 * free(result);
 */
char* string_to_hex(const char *str);

/**
 * @brief 将十六进制字符串转换为普通字符串
 *
 * 将十六进制字符串转换回原始字节序列。
 * 这是 string_to_hex() 函数的逆向操作，支持完整的往返转换。
 *
 * @param hex 十六进制字符串，长度必须为偶数，不能为NULL
 * @return 转换后的原始字符串，失败返回NULL
 *
 * @note 转换规则：每2个十六进制字符转换为1个字节
 * @note 支持大小写十六进制字符(0-9, a-f, A-F)
 * @warning 返回的内存需要调用者释放
 * @warning 输入字符串长度必须为偶数
 * @warning 输入字符串只能包含有效的十六进制字符，否则返回NULL
 *
 * @example
 * char* result = hex_to_string("414243");  // 返回 "ABC"
 * free(result);
 */
char* hex_to_string(const char *hex);

/**
 * @brief 安全释放加密请求数据结构体的内存
 *
 * 安全地释放EncryptRequestData结构体中所有动态分配的内存，
 * 并将所有字段重置为安全的初始状态，防止内存泄漏和悬空指针。
 *
 * @param result 要释放的EncryptRequestData结构体指针，允许为NULL
 *
 * @note 函数对NULL指针调用是安全的，不会产生任何副作用
 * @note 释放后会将所有指针字段设置为NULL，status字段设置为false
 * @note 该函数不会释放结构体本身的内存（如果是动态分配的）
 * @warning 释放后不要再次使用结构体中的指针字段
 * @warning 不要手动释放结构体中的单个字段，统一使用此函数
 */
void free_encrypt_request_data(EncryptRequestData *result);

/**
 * @brief 高安全性加密请求数据函数
 *
 * 执行完整的多层加密流程，包含时间戳生成、密钥强化、数据加密和数字签名。
 * 采用多重安全措施，确保数据传输的机密性和完整性。
 *
 * 加密流程：
 * 1. 生成当前时间戳并转换为十六进制编码
 * 2. 对时间戳进行字符串加密和MSG加密
 * 3. 使用保护密钥对时间戳进行二次加密
 * 4. 将保护结果转换为十六进制格式
 * 5. 使用最终密钥加密原始请求数据
 * 6. 生成数字签名确保数据完整性
 *
 * @param request_data 原始请求数据（JSON格式字符串），不能为NULL
 * @return EncryptRequestData 加密结果结构体，包含所有加密组件
 *
 * @note 返回的结构体必须使用 free_encrypt_request_data() 释放内存
 * @note 如果加密失败，返回的结构体status字段为false，其他字段为NULL
 * @warning 输入的request_data必须是有效的字符串，函数不验证JSON格式
 * @warning 加密过程中如果任何步骤失败，整个操作将失败
 * @see free_encrypt_request_data() 内存释放函数
 * @see decrypt_request_data() 对应的解密函数
 */
EncryptRequestData encrypt_request_data(const char *request_data);

/**
 * @brief 安全释放解密结果结构体的内存
 *
 * 安全地释放DecryptResult结构体中所有动态分配的内存，
 * 包括cJSON对象和字符串，并将所有字段重置为安全状态。
 *
 * @param result 要释放的DecryptResult结构体指针，允许为NULL
 *
 * @note 函数对NULL指针调用是安全的，不会产生任何副作用
 * @note 会正确释放cJSON对象（使用cJSON_Delete）和字符串内存
 * @note 释放后会将所有指针字段设置为NULL，status字段设置为false
 * @note 该函数不会释放结构体本身的内存（如果是动态分配的）
 * @warning 释放后不要再次使用结构体中的指针字段
 * @warning 不要手动释放结构体中的单个字段，统一使用此函数
 */
void free_decrypt_result(DecryptResult *result);

/**
 * @brief 高安全性解密请求数据函数
 *
 * 执行完整的解密和验证流程，逆向加密过程以恢复原始数据。
 * 包含签名验证、密钥解密、数据解密和JSON解析等多个安全检查步骤。
 *
 * 解密流程：
 * 1. 验证数字签名确保数据完整性
 * 2. 解码十六进制编码的保护密钥
 * 3. 使用保护密钥解密得到原始时间戳
 * 4. 验证时间戳防止重放攻击（时间差不超过2秒）
 * 5. 重现加密过程中的密钥生成步骤
 * 6. 使用重现的密钥解密原始数据
 * 7. 解析JSON数据并返回结构化结果
 *
 * @param encrypted_data 加密后的数据（Base64格式），不能为NULL
 * @param key_hex 保护密钥（十六进制格式），不能为NULL
 * @param sign_hex 数字签名字符串（Base64格式），不能为NULL
 * @return DecryptResult 解密结果结构体，包含解密状态和数据
 *
 * @note 返回的结构体必须使用 free_decrypt_result() 释放内存
 * @note 如果解密失败，返回的结构体status字段为false，data字段为NULL
 * @note timestamp字段包含解密得到的时间戳字符串，用于调试和日志记录
 * @warning 所有输入参数都会进行严格验证，任何无效输入都会导致解密失败
 * @warning 签名验证失败将直接导致解密失败，确保数据未被篡改
 * @warning 时间戳验证失败将拒绝请求，防止重放攻击（时间差超过2秒）
 * @see free_decrypt_result() 内存释放函数
 * @see encrypt_request_data() 对应的加密函数
 */
DecryptResult decrypt_request_data(const char *encrypted_data, const char *key_hex, const char *sign_hex);


#endif // DATACRYPTO_H
