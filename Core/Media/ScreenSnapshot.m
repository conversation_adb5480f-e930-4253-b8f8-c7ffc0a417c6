#import "ScreenSnapshot.h"
#import <UIKit/UIKit.h>
#import "Log.h"

@implementation ScreenSnapshot

+ (NSString *)ScreenSnapshotAsBase64WithQuality:(CGFloat)quality {
    // 确保在主线程执行截图操作
    if (![NSThread isMainThread]) {
        __block NSString *base64String = nil;
        dispatch_sync(dispatch_get_main_queue(), ^{
            base64String = [self ScreenSnapshotAsBase64WithQuality:quality];
        });
        return base64String;
    }
    
    @try {
        // 使用更可靠的方法获取截图
        UIImage *screenshot = nil;
        
        // 方法1: 使用UIScreen的API直接获取截图
        CGSize screenSize = [UIScreen mainScreen].bounds.size;
        UIGraphicsBeginImageContextWithOptions(screenSize, NO, 0.0);
        
        // 获取所有窗口
        NSArray *windows = [UIApplication sharedApplication].windows;
        
        // 从后向前遍历窗口以确保正确的绘制顺序
        for (NSInteger i = 0; i < windows.count; i++) {
            UIWindow *window = windows[i];
            if (window.screen == [UIScreen mainScreen] && !window.hidden && window.alpha > 0) {
                // 使用drawViewHierarchyInRect方法，这通常比renderInContext更可靠
                [window drawViewHierarchyInRect:window.bounds afterScreenUpdates:YES];
            }
        }
        
        screenshot = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        
        // 如果方法1失败，尝试方法2
        if (!screenshot) {
            NSLOG(@"[ScreenSnapshot] 方法1失败，尝试方法2");
            
            // 方法2: 使用UIApplication的keyWindow
            UIWindow *keyWindow = nil;
            for (UIWindow *window in windows) {
                if (window.isKeyWindow) {
                    keyWindow = window;
                    break;
                }
            }
            
            if (!keyWindow && windows.count > 0) {
                keyWindow = windows[0];
            }
            
            if (keyWindow) {
                UIGraphicsBeginImageContextWithOptions(keyWindow.bounds.size, NO, 0.0);
                
                // 先尝试drawViewHierarchy
                BOOL success = [keyWindow drawViewHierarchyInRect:keyWindow.bounds afterScreenUpdates:YES];
                
                if (!success) {
                    // 如果失败，尝试renderInContext
                    CGContextRef context = UIGraphicsGetCurrentContext();
                    [keyWindow.layer renderInContext:context];
                }
                
                screenshot = UIGraphicsGetImageFromCurrentImageContext();
                UIGraphicsEndImageContext();
            }
        }
        
        // 如果成功获取截图，转换为base64
        if (screenshot) {
            NSLOG(@"[ScreenSnapshot] 成功获取截图，尺寸: %@", NSStringFromCGSize(screenshot.size));
            
            // 使用JPEG格式，应用质量参数
            NSData *imageData = UIImageJPEGRepresentation(screenshot, quality);
            if (imageData) {
                NSLOG(@"[ScreenSnapshot] 成功转换为JPEG，数据大小: %lu bytes", (unsigned long)imageData.length);
                return [imageData base64EncodedStringWithOptions:0];
            }
        }
        
        NSLOG(@"[ScreenSnapshot] 截图失败");
        return nil;
    } @catch (NSException *exception) {
        NSLOG(@"[ScreenSnapshot] 截图异常: %@", exception);
        if (UIGraphicsGetCurrentContext() != NULL) {
            UIGraphicsEndImageContext();
        }
        return nil;
    }
}

@end
