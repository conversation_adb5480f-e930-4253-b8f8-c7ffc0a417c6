#import "AudioManager.h"
#import <AudioToolbox/AudioToolbox.h>
#import "Log.h"

@interface AudioManager ()

@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic, strong) NSString *audioFilePath;

@end

@implementation AudioManager

+ (instancetype)sharedInstance {
    static AudioManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupAudioSession];
        [self createTestAudioFile];
    }
    return self;
}

- (void)setupAudioSession {
    NSLOG(@"[AudioManager] 设置音频会话");
    
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    NSError *error = nil;
    
    // 设置音频会话类别
    if (![audioSession setCategory:AVAudioSessionCategoryPlayback
                       withOptions:AVAudioSessionCategoryOptionMixWithOthers
                             error:&error]) {
        NSLOG(@"[AudioManager] 设置音频会话类别失败: %@", error.localizedDescription);
    }
    
    // 激活音频会话
    if (![audioSession setActive:YES error:&error]) {
        NSLOG(@"[AudioManager] 激活音频会话失败: %@", error.localizedDescription);
    }
}

- (void)createTestAudioFile {
    NSLOG(@"[AudioManager] 创建无声音频文件");
    
    // 创建临时文件路径
    NSString *tempDir = NSTemporaryDirectory();
    self.audioFilePath = [tempDir stringByAppendingPathComponent:@"silent.wav"];
    
    // 检查文件是否已存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:self.audioFilePath]) {
        NSLOG(@"[AudioManager] 无声音频文件已存在");
        return;
    }
    
    // WAV文件参数
    int sampleRate = 44100;
    int numChannels = 1;
    int bitsPerSample = 16;
    int numSamples = sampleRate * 1; // 1秒音频
    
    // 创建WAV文件头
    char header[44];
    memset(header, 0, sizeof(header));
    
    // RIFF块
    memcpy(header, "RIFF", 4);
    *(uint32_t *)(header + 4) = 36 + numSamples * numChannels * bitsPerSample / 8;
    memcpy(header + 8, "WAVE", 4);
    
    // fmt子块
    memcpy(header + 12, "fmt ", 4);
    *(uint32_t *)(header + 16) = 16; // fmt块大小
    *(uint16_t *)(header + 20) = 1; // PCM格式
    *(uint16_t *)(header + 22) = numChannels;
    *(uint32_t *)(header + 24) = sampleRate;
    *(uint32_t *)(header + 28) = sampleRate * numChannels * bitsPerSample / 8; // 字节率
    *(uint16_t *)(header + 32) = numChannels * bitsPerSample / 8; // 块对齐
    *(uint16_t *)(header + 34) = bitsPerSample;
    
    // data子块
    memcpy(header + 36, "data", 4);
    *(uint32_t *)(header + 40) = numSamples * numChannels * bitsPerSample / 8;
    
    // 创建音频数据
    NSMutableData *audioData = [NSMutableData dataWithBytes:header length:44];
    
    // 创建一个1秒的无声音频数据
    int16_t *samples = (int16_t *)calloc(numSamples * numChannels, sizeof(int16_t));
    if (samples) {
        // 全部填充0，创建无声音频
        memset(samples, 0, numSamples * numChannels * sizeof(int16_t));
        
        [audioData appendBytes:samples length:numSamples * numChannels * sizeof(int16_t)];
        free(samples);
        
        // 写入文件
        BOOL success = [audioData writeToFile:self.audioFilePath atomically:YES];
        if (success) {
            NSLOG(@"[AudioManager] 无声音频文件创建成功: %@", self.audioFilePath);
        } else {
            NSLOG(@"[AudioManager] 无声音频文件创建失败");
        }
    } else {
        NSLOG(@"[AudioManager] 内存分配失败");
    }
}

- (void)startAudio {
    NSLOG(@"[AudioManager] 开始播放测试音频");
    
    if (self.audioPlayer && self.audioPlayer.isPlaying) {
        NSLOG(@"[AudioManager] 音频已在播放中");
        return;
    }
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:self.audioFilePath]) {
        NSLOG(@"[AudioManager] 测试音频文件不存在，重新创建");
        [self createTestAudioFile];
    }
    
    NSError *error = nil;
    self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:[NSURL fileURLWithPath:self.audioFilePath] error:&error];
    
    if (error) {
        NSLOG(@"[AudioManager] 创建音频播放器失败: %@", error.localizedDescription);
        return;
    }
    
    self.audioPlayer.numberOfLoops = -1; // 无限循环
    self.audioPlayer.volume = 0.1; // 低音量
    
    if ([self.audioPlayer play]) {
        NSLOG(@"[AudioManager] 测试音频开始播放");
    } else {
        NSLOG(@"[AudioManager] 测试音频播放失败");
    }
}

- (void)stopAudio {
    NSLOG(@"[AudioManager] 停止播放音频");
    
    if (self.audioPlayer) {
        if (self.audioPlayer.isPlaying) {
            [self.audioPlayer stop];
            NSLOG(@"[AudioManager] 音频已停止");
        }
        self.audioPlayer = nil;
    }
}

- (BOOL)isPlaying {
    return (self.audioPlayer && self.audioPlayer.isPlaying);
}

@end