#ifndef JSON_UTILS_H
#define JSON_UTILS_H

#include "cJSON.h"
// #include "cJSON.h"

/**
 * @brief 将JSON字符串转换为cJSON对象
 * 
 * @param jsonString 要解析的JSON字符串
 * @return cJSON* 解析后的cJSON对象，失败时返回NULL
 * @note 调用者负责使用cJSON_Delete释放返回的对象
 */
cJSON* JSONUtils_ParseString(const char* jsonString);

/**
 * @brief 将cJSON对象转换为JSON字符串
 * 
 * @param json 要转换的cJSON对象
 * @param formatted 是否格式化输出 (1=格式化, 0=不格式化)
 * @return char* 生成的JSON字符串，失败时返回NULL
 * @note 调用者负责使用free释放返回的字符串
 */
char* JSONUtils_ToString(const cJSON* json, int formatted);

/**
 * @brief 创建一个简单的JSON对象
 * 
 * @param key 键名
 * @param value 值
 * @return cJSON* 创建的JSON对象，失败时返回NULL
 * @note 调用者负责使用cJSON_Delete释放返回的对象
 */
cJSON* JSONUtils_CreateSimpleObject(const char* key, const char* value);

/**
 * @brief 从JSON对象中获取字符串值
 * 
 * @param json JSON对象
 * @param key 键名
 * @return const char* 字符串值，不存在或类型不匹配时返回NULL
 */
const char* JSONUtils_GetStringValue(const cJSON* json, const char* key);

/**
 * @brief 从JSON对象中获取整数值
 * 
 * @param json JSON对象
 * @param key 键名
 * @param defaultValue 默认值（当键不存在或类型不匹配时返回）
 * @return int 整数值
 */
int JSONUtils_GetIntValue(const cJSON* json, const char* key, int defaultValue);

/**
 * @brief 从JSON对象中获取布尔值
 *
 * @param json JSON对象
 * @param key 键名
 * @param defaultValue 默认值（当键不存在或类型不匹配时返回）
 * @return int 布尔值 (1=true, 0=false)
 */
int JSONUtils_GetBoolValue(const cJSON* json, const char* key, int defaultValue);

/**
 * @brief 从JSON对象中获取子JSON对象
 *
 * @param json 父JSON对象
 * @param key 键名
 * @return cJSON* 子JSON对象，不存在或类型不匹配时返回NULL
 * @note 返回的对象是原JSON对象的一部分，不需要单独释放
 * @note 如果需要独立使用返回的对象，应该使用cJSON_Duplicate复制
 */
cJSON* JSONUtils_GetObjectValue(const cJSON* json, const char* key);

/**
 * @brief 从JSON对象中获取数组
 *
 * @param json JSON对象
 * @param key 键名
 * @return cJSON* JSON数组对象，不存在或类型不匹配时返回NULL
 * @note 返回的数组是原JSON对象的一部分，不需要单独释放
 * @note 如果需要独立使用返回的数组，应该使用cJSON_Duplicate复制
 */
cJSON* JSONUtils_GetArrayValue(const cJSON* json, const char* key);

/**
 * @brief 从JSON对象中获取浮点数值
 *
 * @param json JSON对象
 * @param key 键名
 * @param defaultValue 默认值（当键不存在或类型不匹配时返回）
 * @return double 浮点数值
 */
double JSONUtils_GetDoubleValue(const cJSON* json, const char* key, double defaultValue);

/**
 * @brief 检查JSON对象中是否存在指定的键
 *
 * @param json JSON对象
 * @param key 键名
 * @return int 存在返回1，不存在返回0
 */
int JSONUtils_HasKey(const cJSON* json, const char* key);

/**
 * @brief 获取JSON数组的大小
 *
 * @param array JSON数组对象
 * @return int 数组大小，如果不是数组则返回0
 */
int JSONUtils_GetArraySize(const cJSON* array);

/**
 * @brief 从JSON数组中获取指定索引的元素
 *
 * @param array JSON数组对象
 * @param index 索引（从0开始）
 * @return cJSON* 数组元素，索引无效时返回NULL
 * @note 返回的元素是原数组的一部分，不需要单独释放
 */
cJSON* JSONUtils_GetArrayItem(const cJSON* array, int index);

#endif /* JSON_UTILS_H */