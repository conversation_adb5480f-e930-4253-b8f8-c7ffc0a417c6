#include "JSONUtils.h"
#include <stdlib.h>
#include <string.h>
#include "Log.h"

// 添加一个新函数用于验证和清理UTF-8字符串
char* JSONUtils_SanitizeUTF8(const char* input) {
    if (input == NULL) {
        return NULL;
    }
    
    size_t len = strlen(input);
    char* sanitized = (char*)malloc(len + 1);
    if (sanitized == NULL) {
        return NULL;
    }
    
    size_t j = 0;
    for (size_t i = 0; i < len; i++) {
        unsigned char c = (unsigned char)input[i];
        
        // 检查UTF-8编码的有效性
        if (c <= 0x7F) {
            // 单字节ASCII字符
            sanitized[j++] = input[i];
        } else if (c >= 0xC2 && c <= 0xDF && i + 1 < len) {
            // 双字节UTF-8序列
            unsigned char c2 = (unsigned char)input[i + 1];
            if (c2 >= 0x80 && c2 <= 0xBF) {
                sanitized[j++] = input[i];
                sanitized[j++] = input[i + 1];
            } else {
                // 替换为问号或跳过
                sanitized[j++] = '?';
            }
            i++;
        } else if (c >= 0xE0 && c <= 0xEF && i + 2 < len) {
            // 三字节UTF-8序列
            unsigned char c2 = (unsigned char)input[i + 1];
            unsigned char c3 = (unsigned char)input[i + 2];
            if (c2 >= 0x80 && c2 <= 0xBF && c3 >= 0x80 && c3 <= 0xBF) {
                sanitized[j++] = input[i];
                sanitized[j++] = input[i + 1];
                sanitized[j++] = input[i + 2];
            } else {
                // 替换为问号或跳过
                sanitized[j++] = '?';
            }
            i += 2;
        } else if (c >= 0xF0 && c <= 0xF4 && i + 3 < len) {
            // 四字节UTF-8序列
            unsigned char c2 = (unsigned char)input[i + 1];
            unsigned char c3 = (unsigned char)input[i + 2];
            unsigned char c4 = (unsigned char)input[i + 3];
            if (c2 >= 0x80 && c2 <= 0xBF && c3 >= 0x80 && c3 <= 0xBF && c4 >= 0x80 && c4 <= 0xBF) {
                sanitized[j++] = input[i];
                sanitized[j++] = input[i + 1];
                sanitized[j++] = input[i + 2];
                sanitized[j++] = input[i + 3];
            } else {
                // 替换为问号或跳过
                sanitized[j++] = '?';
            }
            i += 3;
        } else {
            // 无效的UTF-8序列，替换为问号
            sanitized[j++] = '?';
        }
    }
    
    sanitized[j] = '\0';
    return sanitized;
}

// 修改JSONUtils_ParseString函数，使用清理后的字符串
cJSON* JSONUtils_ParseString(const char* jsonString) {
    if (jsonString == NULL) {
        return NULL;
    }
    
    // 清理输入字符串，确保是有效的UTF-8
    char* sanitized = JSONUtils_SanitizeUTF8(jsonString);
    if (sanitized == NULL) {
        return NULL;
    }
    
    // 使用cJSON_Parse解析清理后的JSON字符串
    cJSON* json = cJSON_Parse(sanitized);
    
    // 释放清理后的字符串
    free(sanitized);
    
    if (json == NULL) {
        // 解析失败，可以通过cJSON_GetErrorPtr获取错误位置
        const char* error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            // 这里可以添加日志记录错误信息
            LOG("JSON解析错误，位置: %s", error_ptr);
        }
        return NULL;
    }
    
    return json;
}

char* JSONUtils_ToString(const cJSON* json, int formatted) {
    if (json == NULL) {
        return NULL;
    }
    
    // 根据formatted参数决定是否格式化输出
    char* jsonString = formatted ? cJSON_Print(json) : cJSON_PrintUnformatted(json);
    if (jsonString == NULL) {
        // 转换失败
        return NULL;
    }
    
    return jsonString;
}

cJSON* JSONUtils_CreateSimpleObject(const char* key, const char* value) {
    if (key == NULL) {
        return NULL;
    }
    
    // 创建一个新的JSON对象
    cJSON* json = cJSON_CreateObject();
    if (json == NULL) {
        return NULL;
    }
    
    // 添加键值对
    if (value != NULL) {
        if (cJSON_AddStringToObject(json, key, value) == NULL) {
            cJSON_Delete(json);
            return NULL;
        }
    } else {
        if (cJSON_AddNullToObject(json, key) == NULL) {
            cJSON_Delete(json);
            return NULL;
        }
    }
    
    return json;
}

const char* JSONUtils_GetStringValue(const cJSON* json, const char* key) {
    if (json == NULL || key == NULL) {
        return NULL;
    }
    
    // 获取指定键的JSON项
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    if (item == NULL || !cJSON_IsString(item)) {
        return NULL;
    }
    
    return item->valuestring;
}

int JSONUtils_GetIntValue(const cJSON* json, const char* key, int defaultValue) {
    if (json == NULL || key == NULL) {
        return defaultValue;
    }
    
    // 获取指定键的JSON项
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    if (item == NULL || !cJSON_IsNumber(item)) {
        return defaultValue;
    }
    
    return item->valueint;
}

int JSONUtils_GetBoolValue(const cJSON* json, const char* key, int defaultValue) {
    if (json == NULL || key == NULL) {
        return defaultValue;
    }
    
    // 获取指定键的JSON项
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    if (item == NULL) {
        return defaultValue;
    }
    
    if (cJSON_IsBool(item)) {
        return cJSON_IsTrue(item) ? 1 : 0;
    }
    
    return defaultValue;
}

cJSON* JSONUtils_GetObjectValue(const cJSON* json, const char* key) {
    if (json == NULL || key == NULL) {
        return NULL;
    }

    // 获取指定键的JSON项
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    if (item == NULL || !cJSON_IsObject(item)) {
        return NULL;
    }

    return item;
}

cJSON* JSONUtils_GetArrayValue(const cJSON* json, const char* key) {
    if (json == NULL || key == NULL) {
        return NULL;
    }

    // 获取指定键的JSON项
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    if (item == NULL || !cJSON_IsArray(item)) {
        return NULL;
    }

    return item;
}

double JSONUtils_GetDoubleValue(const cJSON* json, const char* key, double defaultValue) {
    if (json == NULL || key == NULL) {
        return defaultValue;
    }

    // 获取指定键的JSON项
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    if (item == NULL || !cJSON_IsNumber(item)) {
        return defaultValue;
    }

    return item->valuedouble;
}

int JSONUtils_HasKey(const cJSON* json, const char* key) {
    if (json == NULL || key == NULL) {
        return 0;
    }

    // 检查是否存在指定键
    cJSON* item = cJSON_GetObjectItemCaseSensitive(json, key);
    return (item != NULL) ? 1 : 0;
}

int JSONUtils_GetArraySize(const cJSON* array) {
    if (array == NULL || !cJSON_IsArray(array)) {
        return 0;
    }

    return cJSON_GetArraySize(array);
}

cJSON* JSONUtils_GetArrayItem(const cJSON* array, int index) {
    if (array == NULL || !cJSON_IsArray(array) || index < 0) {
        return NULL;
    }

    return cJSON_GetArrayItem(array, index);
}