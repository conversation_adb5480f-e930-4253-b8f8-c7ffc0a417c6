# JSONUtils 使用示例

## 概述

JSONUtils 提供了一套完整的 JSON 操作工具函数，简化了 cJSON 库的使用。

## 基本用法

### 1. 解析 JSON 字符串

```c
#include "JSONUtils.h"

const char* json_str = "{\"name\":\"张三\",\"age\":25,\"active\":true}";
cJSON* json = JSONUtils_ParseString(json_str);
if (json) {
    // 使用 JSON 对象...
    cJSON_Delete(json);
}
```

### 2. 获取基本类型值

```c
// 获取字符串值
const char* name = JSONUtils_GetStringValue(json, "name");
if (name) {
    printf("姓名: %s\n", name);
}

// 获取整数值
int age = JSONUtils_GetIntValue(json, "age", 0);
printf("年龄: %d\n", age);

// 获取布尔值
int active = JSONUtils_GetBoolValue(json, "active", 0);
printf("状态: %s\n", active ? "激活" : "未激活");

// 获取浮点数值
double score = JSONUtils_GetDoubleValue(json, "score", 0.0);
printf("分数: %.2f\n", score);
```

### 3. 获取 JSON 对象

```c
const char* complex_json = "{"
    "\"user\": {"
        "\"id\": 123,"
        "\"profile\": {"
            "\"name\": \"张三\","
            "\"email\": \"<EMAIL>\""
        "}"
    "},"
    "\"settings\": {"
        "\"theme\": \"dark\","
        "\"notifications\": true"
    "}"
"}";

cJSON* root = JSONUtils_ParseString(complex_json);
if (root) {
    // 获取 user 对象
    cJSON* user = JSONUtils_GetObjectValue(root, "user");
    if (user) {
        int user_id = JSONUtils_GetIntValue(user, "id", 0);
        printf("用户ID: %d\n", user_id);
        
        // 获取嵌套的 profile 对象
        cJSON* profile = JSONUtils_GetObjectValue(user, "profile");
        if (profile) {
            const char* name = JSONUtils_GetStringValue(profile, "name");
            const char* email = JSONUtils_GetStringValue(profile, "email");
            printf("姓名: %s, 邮箱: %s\n", name, email);
        }
    }
    
    // 获取 settings 对象
    cJSON* settings = JSONUtils_GetObjectValue(root, "settings");
    if (settings) {
        const char* theme = JSONUtils_GetStringValue(settings, "theme");
        int notifications = JSONUtils_GetBoolValue(settings, "notifications", 0);
        printf("主题: %s, 通知: %s\n", theme, notifications ? "开启" : "关闭");
    }
    
    cJSON_Delete(root);
}
```

### 4. 处理数组

```c
const char* array_json = "{"
    "\"users\": ["
        "{\"name\": \"张三\", \"age\": 25},"
        "{\"name\": \"李四\", \"age\": 30},"
        "{\"name\": \"王五\", \"age\": 28}"
    "],"
    "\"numbers\": [1, 2, 3, 4, 5]"
"}";

cJSON* root = JSONUtils_ParseString(array_json);
if (root) {
    // 获取用户数组
    cJSON* users = JSONUtils_GetArrayValue(root, "users");
    if (users) {
        int user_count = JSONUtils_GetArraySize(users);
        printf("用户数量: %d\n", user_count);
        
        for (int i = 0; i < user_count; i++) {
            cJSON* user = JSONUtils_GetArrayItem(users, i);
            if (user) {
                const char* name = JSONUtils_GetStringValue(user, "name");
                int age = JSONUtils_GetIntValue(user, "age", 0);
                printf("用户[%d]: %s, 年龄: %d\n", i, name, age);
            }
        }
    }
    
    // 获取数字数组
    cJSON* numbers = JSONUtils_GetArrayValue(root, "numbers");
    if (numbers) {
        int count = JSONUtils_GetArraySize(numbers);
        printf("数字数组: ");
        for (int i = 0; i < count; i++) {
            cJSON* number = JSONUtils_GetArrayItem(numbers, i);
            if (number && cJSON_IsNumber(number)) {
                printf("%d ", number->valueint);
            }
        }
        printf("\n");
    }
    
    cJSON_Delete(root);
}
```

### 5. 检查键是否存在

```c
cJSON* json = JSONUtils_ParseString("{\"name\":\"张三\",\"age\":25}");
if (json) {
    if (JSONUtils_HasKey(json, "name")) {
        printf("包含 name 字段\n");
    }
    
    if (!JSONUtils_HasKey(json, "email")) {
        printf("不包含 email 字段\n");
    }
    
    cJSON_Delete(json);
}
```

### 6. 在加密请求服务中的实际应用

```c
// 在 CryptoRequestService 的响应处理中
CryptoRequestResult result = crypto_request_send_and_decrypt(&config);

if (result.success && result.response_data) {
    // 检查业务状态码
    int code = JSONUtils_GetIntValue(result.response_data, "code", 0);
    if (code == 200) {
        // 获取业务数据对象
        cJSON* data = JSONUtils_GetObjectValue(result.response_data, "data");
        if (data) {
            // 提取具体的业务字段
            bool status = JSONUtils_GetBoolValue(data, "status", false);
            const char* message = JSONUtils_GetStringValue(data, "message");
            
            if (status) {
                printf("业务处理成功: %s\n", message);
                
                // 处理具体的业务数据
                const char* address = JSONUtils_GetStringValue(data, "address");
                if (address) {
                    printf("地址: %s\n", address);
                }
                
                // 处理数组数据
                cJSON* items = JSONUtils_GetArrayValue(data, "items");
                if (items) {
                    int count = JSONUtils_GetArraySize(items);
                    for (int i = 0; i < count; i++) {
                        cJSON* item = JSONUtils_GetArrayItem(items, i);
                        // 处理每个项目...
                    }
                }
            } else {
                printf("业务处理失败: %s\n", message);
            }
        }
    } else {
        const char* error = JSONUtils_GetStringValue(result.response_data, "msg");
        printf("服务器错误 (code: %d): %s\n", code, error);
    }
}

crypto_request_free_result(&result);
```

## 错误处理

所有 JSONUtils 函数都会进行参数验证：

- 如果传入 NULL 指针，函数会安全返回默认值
- 如果键不存在或类型不匹配，会返回默认值或 NULL
- 数组操作会检查索引范围

## 内存管理

- `JSONUtils_ParseString()` 返回的对象需要用 `cJSON_Delete()` 释放
- `JSONUtils_GetObjectValue()` 和 `JSONUtils_GetArrayValue()` 返回的是原对象的引用，不需要单独释放
- 如果需要独立使用子对象，应该使用 `cJSON_Duplicate()` 复制

## 注意事项

1. 返回的字符串指针指向 cJSON 内部存储，不要修改或释放
2. 在父 JSON 对象被删除后，子对象引用会失效
3. 数组索引从 0 开始，超出范围会返回 NULL
