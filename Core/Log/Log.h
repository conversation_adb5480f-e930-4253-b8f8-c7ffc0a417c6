#ifndef DEBUG_LOG_H
#define DEBUG_LOG_H

#include <syslog.h>
#include <string.h>

#ifdef __OBJC__
    #import <Foundation/Foundation.h>
#endif

// 获取文件名（不包含路径）
#define __FILENAME__ (strrchr(__FILE__, '/') ? strrchr(__FILE__, '/') + 1 : __FILE__)

// 定义日志宏，只有在 DEBUG=1 时才编译日志代码
#if DEBUG
    // 基础日志宏，包含文件名、行号和函数名
    #define LOG_WITH_LOCATION(level, format, ...) \
        syslog(level, "[%s:%d %s()] " format, __FILENAME__, __LINE__, __FUNCTION__, ##__VA_ARGS__)

    // 简化的日志宏（兼容原有代码）
    #define LOG(format, ...) LOG_WITH_LOCATION(LOG_NOTICE, format, ##__VA_ARGS__)

    // 不同级别的日志宏
    #define DLOG_ERROR(format, ...) LOG_WITH_LOCATION(LOG_ERR, format, ##__VA_ARGS__)
    #define DLOG_WARNING(format, ...) LOG_WITH_LOCATION(LOG_WARNING, format, ##__VA_ARGS__)
    #define DLOG_INFO(format, ...) LOG_WITH_LOCATION(LOG_INFO, format, ##__VA_ARGS__)
    #define DLOG_DEBUG(format, ...) LOG_WITH_LOCATION(LOG_DEBUG, format, ##__VA_ARGS__)

    // 简单日志宏（不包含位置信息，用于简洁输出）
    #define LOG_SIMPLE(format, ...) syslog(LOG_NOTICE, format, ##__VA_ARGS__)
    #define DLOG_ERROR_SIMPLE(format, ...) syslog(LOG_ERR, format, ##__VA_ARGS__)
    #define DLOG_WARNING_SIMPLE(format, ...) syslog(LOG_WARNING, format, ##__VA_ARGS__)
    #define DLOG_INFO_SIMPLE(format, ...) syslog(LOG_INFO, format, ##__VA_ARGS__)
    #define DLOG_DEBUG_SIMPLE(format, ...) syslog(LOG_DEBUG, format, ##__VA_ARGS__)

    // NSLog 日志宏（仅在 Objective-C 环境下可用）
    #ifdef __OBJC__
        // 带位置信息的 NSLog 宏
        #define NSLOG_WITH_LOCATION(format, ...) \
            NSLog(@"[%s:%d %s()] " format, __FILENAME__, __LINE__, __FUNCTION__, ##__VA_ARGS__)

        // NSLog 日志宏
        #define NSLOG(format, ...) NSLOG_WITH_LOCATION(format, ##__VA_ARGS__)
        #define NSLOG_ERROR(format, ...) NSLOG_WITH_LOCATION(@"❌ ERROR: " format, ##__VA_ARGS__)
        #define NSLOG_WARNING(format, ...) NSLOG_WITH_LOCATION(@"⚠️ WARNING: " format, ##__VA_ARGS__)
        #define NSLOG_INFO(format, ...) NSLOG_WITH_LOCATION(@"ℹ️ INFO: " format, ##__VA_ARGS__)
        #define NSLOG_DEBUG(format, ...) NSLOG_WITH_LOCATION(@"🐛 DEBUG: " format, ##__VA_ARGS__)

        // 简单 NSLog 宏（不包含位置信息）
        #define NSLOG_SIMPLE(format, ...) NSLog(format, ##__VA_ARGS__)
        #define NSLOG_ERROR_SIMPLE(format, ...) NSLog(@"❌ ERROR: " format, ##__VA_ARGS__)
        #define NSLOG_WARNING_SIMPLE(format, ...) NSLog(@"⚠️ WARNING: " format, ##__VA_ARGS__)
        #define NSLOG_INFO_SIMPLE(format, ...) NSLog(@"ℹ️ INFO: " format, ##__VA_ARGS__)
        #define NSLOG_DEBUG_SIMPLE(format, ...) NSLog(@"🐛 DEBUG: " format, ##__VA_ARGS__)
    #endif

#else
    // 空宏，编译时会被移除
    #define LOG_WITH_LOCATION(level, format, ...)
    #define LOG(format, ...)
    #define DLOG_ERROR(format, ...)
    #define DLOG_WARNING(format, ...)
    #define DLOG_INFO(format, ...)
    #define DLOG_DEBUG(format, ...)
    #define LOG_SIMPLE(format, ...)
    #define DLOG_ERROR_SIMPLE(format, ...)
    #define DLOG_WARNING_SIMPLE(format, ...)
    #define DLOG_INFO_SIMPLE(format, ...)
    #define DLOG_DEBUG_SIMPLE(format, ...)

    // NSLog 空宏
    #ifdef __OBJC__
        #define NSLOG_WITH_LOCATION(format, ...)
        #define NSLOG(format, ...)
        #define NSLOG_ERROR(format, ...)
        #define NSLOG_WARNING(format, ...)
        #define NSLOG_INFO(format, ...)
        #define NSLOG_DEBUG(format, ...)
        #define NSLOG_SIMPLE(format, ...)
        #define NSLOG_ERROR_SIMPLE(format, ...)
        #define NSLOG_WARNING_SIMPLE(format, ...)
        #define NSLOG_INFO_SIMPLE(format, ...)
        #define NSLOG_DEBUG_SIMPLE(format, ...)
    #endif
#endif

#endif // DEBUG_LOG_H
