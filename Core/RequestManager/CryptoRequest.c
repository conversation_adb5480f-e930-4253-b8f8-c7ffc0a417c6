/**
 * @file CryptoRequestService.c
 * @brief 加密请求服务模块实现
 * 
 * 实现完整的加密请求发送和响应解密功能。
 * 包含严格的内存管理和错误处理机制。
 */

#include "CryptoRequest.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <stdio.h>

// 引入加密相关的头文件
#include "StringCryptor_v2.h"
#include "Signature.h"
#include "KeyCryptor.h"
#include "ParamsCryptor.h"
#include "Sha256.h"
#include "MD5.h"
#include "SocketHTTP.h"
#include "JSONUtils.h"
#include "Log.h"

// 内部常量定义
#define DEFAULT_TIMEOUT_SECONDS 30
#define DEFAULT_HEADERS "Content-Type: application/json"
#define MAX_ERROR_MESSAGE_SIZE 512
#define TIMESTAMP_BUFFER_SIZE 32

// http://47.108.169.125:9000/tools/v1
// const uint8_t serverUrlKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x67, 0x25, 0x68, 0xDF, 0x79, 0x06, 0x07, 0x46, 0x92, 0x00, 0x35, 0xB1, 0x4C, 0x94, 0xE7, 0x80, 0x29, 0x11, 0x7D, 0xFB, 0x94, 0x1D, 0x1E, 0xAF, 0xC7, 0x43, 0x82, 0xA9, 0x7E, 0x79, 0x9B, 0x74, 0x5E, 0x56, 0x5E, 0x5E, 0x0A, 0xAC, 0x61 };

// http://192.168.188.100:9001
// const uint8_t serverUrlKey[] =  { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x67, 0x25, 0x68, 0xDF, 0x79, 0x06, 0x07, 0x6E, 0x40, 0x6D, 0x1B, 0xCB, 0x7B, 0xE7, 0x38, 0xA5, 0x33, 0xA0, 0x9E, 0xCD, 0x8F, 0xCC, 0x1B, 0xE7, 0xC3, 0x43, 0xF2, 0xA7, 0xE6, 0xB9, 0x81 };

// http://47.108.169.125:9001
const uint8_t serverUrlKey[] =  { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x67, 0x25, 0x68, 0xDF, 0x79, 0x06, 0x07, 0x46, 0x92, 0x00, 0x35, 0xB1, 0x4C, 0x94, 0xE7, 0x80, 0x29, 0x11, 0x7D, 0xFB, 0x94, 0x1D, 0x1E, 0xAF, 0xC7, 0x4B, 0x0D, 0x69, 0x9C, 0x7F };

// 签名密钥常量
static const uint8_t SIGNATURE_KEYS[] = {
    0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 
    0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 
    0x4C, 0x1F, 0xA4, 0xBB, 0x75, 0x16, 0x6E, 0x78, 
    0x7A, 0xB3, 0x31, 0x31
};

// 加密密钥常量
static const uint8_t ENCRYPTION_KEY[] = {
    0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 
    0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10,
    0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 
    0x99, 0xAA, 0xBB, 0xCC, 0xDD, 0xEE, 0xFF, 0x00
};

// 内部函数声明
static char* string_to_hex(const char* str);
static char* generate_encryption_key(void);
static char* create_request_json(const char* encrypted_data, const char* signature, const char* timestamp);
static CryptoRequestResult create_error_result(const char* error_message);
static bool verify_response_signature(const char* data, const char* signature, const char* verification_key);
static char* decrypt_response_data(const char* encrypted_data, const char* decryption_key);

/**
 * @brief 字符串转十六进制
 */
static char* string_to_hex(const char* str) {
    if (!str) return NULL;

    size_t len = strlen(str);

    // 防止整数溢出：检查输入长度
    if (len > 5120) {  // 限制最大输入长度为5KB
        return NULL;
    }

    // 检查乘法是否会溢出
    if (len > SIZE_MAX / 2 - 1) {
        return NULL;
    }

    char* hex_str = malloc(len * 2 + 1);
    if (!hex_str) return NULL;
    
    for (size_t i = 0; i < len; i++) {
        sprintf(hex_str + i * 2, "%02x", (unsigned char)str[i]);
    }
    hex_str[len * 2] = '\0';
    
    return hex_str;
}

/**
 * @brief 生成加密密钥
 * 
 * 执行完整的密钥生成流程：时间戳 -> SHA256 -> MD5 -> 加密 -> 十六进制
 */
static char* generate_encryption_key(void) {
    char* result = NULL;
    char* sha256_result = NULL;
    char* md5_result = NULL;
    char* encrypted_result = NULL;
    
    // 生成时间戳
    time_t now = time(NULL);
    char timestamp[TIMESTAMP_BUFFER_SIZE];
    snprintf(timestamp, sizeof(timestamp), "%ld", now);
    
    // SHA256处理
    sha256_result = sha256_string(timestamp);
    if (!sha256_result) goto cleanup;
    
    // MD5处理
    md5_result = md5_string(sha256_result);
    if (!md5_result) goto cleanup;
    
    // 加密处理
    encrypted_result = msg_encryption_with_key(
        (const uint8_t*)md5_result, strlen(md5_result), 
        ENCRYPTION_KEY, sizeof(ENCRYPTION_KEY)
    );
    if (!encrypted_result) goto cleanup;
    
    // 转换为十六进制
    result = string_to_hex(encrypted_result);
    
cleanup:
    if (sha256_result) free(sha256_result);
    if (md5_result) free(md5_result);
    if (encrypted_result) free(encrypted_result);
    
    return result;
}

/**
 * @brief 创建请求JSON
 */
static char* create_request_json(const char* encrypted_data, const char* signature, const char* timestamp) {
    if (!encrypted_data || !signature || !timestamp) return NULL;

    // 检查输入长度，防止整数溢出
    size_t data_len = strlen(encrypted_data);
    size_t sig_len = strlen(signature);
    size_t ts_len = strlen(timestamp);

    // 防止整数溢出：检查单个字符串长度和总长度
    if (data_len > 10240 || sig_len > 1024 || ts_len > 64) {
        return NULL;  // 输入过长
    }

    size_t json_size = data_len + sig_len + ts_len + 100;

    // 再次检查总长度
    if (json_size > 20480) {  // 限制最大JSON大小为20KB
        return NULL;
    }

    char* json_str = malloc(json_size);
    if (!json_str) return NULL;
    
    snprintf(json_str, json_size, 
        "{\"data\":\"%s\",\"sign\":\"%s\",\"ts\":\"%s\"}", 
        encrypted_data, signature, timestamp);
    
    return json_str;
}

/**
 * @brief 创建错误结果
 */
static CryptoRequestResult create_error_result(const char* error_message) {
    CryptoRequestResult result = {0};
    result.success = false;
    result.http_status_code = 0;
    result.response_data = NULL;
    // result.raw_response = NULL;
    
    if (error_message) {
        size_t msg_len = strlen(error_message);

        // 限制错误消息长度，防止过大的分配
        if (msg_len > MAX_ERROR_MESSAGE_SIZE - 1) {
            msg_len = MAX_ERROR_MESSAGE_SIZE - 1;
        }

        result.error_message = malloc(msg_len + 1);
        if (result.error_message) {
            strncpy(result.error_message, error_message, msg_len);
            result.error_message[msg_len] = '\0';
        }
    }
    
    return result;
}

/**
 * @brief 验证响应签名
 */
static bool verify_response_signature(const char* data, const char* signature, const char* verification_key) {
    if (!data || !signature || !verification_key) return false;
    
    char* sign_key = sha256_string(verification_key);
    if (!sign_key) return false;
    
    bool valid = verify_secure_signature(data, sign_key, signature);
    free(sign_key);
    
    return valid;
}

/**
 * @brief 解密响应数据
 */
static char* decrypt_response_data(const char* encrypted_data, const char* decryption_key) {
    if (!encrypted_data || !decryption_key) return NULL;

    size_t decrypted_len = 0;
    uint8_t* decrypted_bytes = decrypt_request_params_with_hex_key(
        encrypted_data, &decrypted_len, decryption_key);

    if (!decrypted_bytes || decrypted_len == 0) {
        return NULL;
    }

    // 检查解密后的数据长度，防止过大的分配
    if (decrypted_len > 10240) {  // 限制最大解密数据为10KB
        free(decrypted_bytes);
        return NULL;
    }

    // 转换为字符串
    char* result = malloc(decrypted_len + 1);
    if (result) {
        memcpy(result, decrypted_bytes, decrypted_len);
        result[decrypted_len] = '\0';
    }

    free(decrypted_bytes);
    return result;
}

/**
 * @brief 发送加密请求并解密响应 - 主函数实现
 */

CryptoRequestResult crypto_request_send_and_decrypt(const CryptoRequestConfig* config) {
    // 参数验证 - request_data 现在是可选的
    if (!config) {
        return create_error_result("配置参数无效：config 不能为空");
    }
    // 初始化结果
    CryptoRequestResult result = {0};

    // 临时变量声明（用于统一清理）
    char *action = NULL;
    char *product_id = NULL;
    char *version = NULL;
    char *serial_number_key = NULL;
    char* encryption_key = NULL;
    char* encrypted_data = NULL;
    char* signature_key = NULL;
    char* signature = NULL;
    char* request_json = NULL;
    char* response = NULL;
    char* decrypted_response = NULL;
    cJSON* response_json = NULL;
    char* server_url = NULL;
    char* decryption_key_for_timestamp = NULL;
    char* decrypted_timestamp_str = NULL;
    char* enhanced_request_data = NULL;
    char* decrypted_version_data = NULL;
    char* decrypted_product_id_data = NULL;

    // 生成当前时间戳
    time_t now = time(NULL);
    char timestamp[TIMESTAMP_BUFFER_SIZE];
    snprintf(timestamp, sizeof(timestamp), "%ld", now);

    LOG("开始加密请求流程，时间戳: %s", timestamp);

    const uint8_t acKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x2F, 0xE9, 0x64, 0x97, 0x87, 0x03, 0xDD, 0x22, 0x07, 0x03 };
    action = decrypt_string_v2(acKey, sizeof(acKey));
    if (!action) {
        LOG("action 解密失败");
        goto cleanup;
    }
    LOG("action: %s", action);

    const uint8_t productIdKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x6F, 0xA5, 0x4B, 0x9A, 0x29, 0xEE };
    product_id = decrypt_string_v2(productIdKey, sizeof(productIdKey));
    if (!product_id) {
        LOG("product_id 解密失败");
        goto cleanup;
    }

    LOG("product_id: %s", product_id);

    const uint8_t versionKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xB7, 0xB0, 0x71, 0xC6, 0xF2, 0x0C, 0x01, 0xD8, 0xCC, 0xEC, 0xA8 };
    version = decrypt_string_v2(versionKey, sizeof(versionKey));
    if (!version) {
        LOG("version 解密失败");
        goto cleanup;
    }
    LOG("version: %s", version);

    

    // 步骤0: 处理请求数据，添加 version、product_id、action 和 serial_number 参数
    cJSON* request_json_obj = NULL;
    

    // 检查是否需要处理参数（有任何额外参数或者没有提供 request_data）
    bool need_process_params = (config->version || config->product_id || config->action || config->serial_number || config->deviceInfo);
    bool has_request_data = (config->request_data && strlen(config->request_data) > 0);

    if (need_process_params || !has_request_data) {
        if (has_request_data) {
            // 如果提供了 request_data，解析现有的 JSON
            request_json_obj = cJSON_Parse(config->request_data);
            if (!request_json_obj) {
                LOG("解析请求数据JSON失败");
                result = create_error_result("解析请求数据JSON失败");
                goto cleanup;
            }
            LOG("使用提供的请求数据: %s", config->request_data);
        } else {
            // 如果没有提供 request_data，创建新的空 JSON 对象
            request_json_obj = cJSON_CreateObject();
            if (!request_json_obj) {
                LOG("创建请求数据JSON对象失败");
                result = create_error_result("创建请求数据JSON对象失败");
                goto cleanup;
            }
            LOG("创建新的空JSON对象用于请求数据");
        }
        // 添加 serial_number 参数（可选）
        if (config->serial_number && strlen(config->serial_number) > 0) {
            const uint8_t serialNumberKey[] = { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x5F, 0xBE, 0x71, 0x96, 0x37, 0x78, 0x05, 0x40, 0xE4, 0xE5, 0x59, 0xC1, 0x4B, 0x33, 0x80, 0x87 };
            serial_number_key = decrypt_string_v2(serialNumberKey, sizeof(serialNumberKey));
            if (!serial_number_key) {
                LOG("serial_number_key 解密失败");
                goto cleanup;
            }
            LOG("serial_number_key: %s", serial_number_key);

            cJSON* serial_number_item = cJSON_CreateString(config->serial_number);
            if (serial_number_item) {
                cJSON_AddItemToObject(request_json_obj, serial_number_key, serial_number_item);
                LOG("已添加设备序列号: %s", config->serial_number);
            } else {
                LOG("添加设备序列号失败");
            }
        }

        // 添加 deviceInfo 参数（可选，JSON字符串）
        if (config->deviceInfo && strlen(config->deviceInfo) > 0) {
            // 解析 deviceInfo JSON 字符串
            cJSON* device_info_json = cJSON_Parse(config->deviceInfo);
            if (device_info_json) {
                cJSON_AddItemToObject(request_json_obj, "deviceInfo", device_info_json);
                LOG("已添加设备信息: %s", config->deviceInfo);
            } else {
                // 如果解析失败，作为普通字符串添加
                cJSON* device_info_item = cJSON_CreateString(config->deviceInfo);
                if (device_info_item) {
                    cJSON_AddItemToObject(request_json_obj, "deviceInfo", device_info_item);
                    LOG("已添加设备信息（字符串格式）: %s", config->deviceInfo);
                } else {
                    LOG("添加设备信息失败");
                }
            }
        }

        // 添加 version 参数
        if (config->version) {
            decrypted_version_data = decrypt_string_v2(config->version, config->version_length);
            if (!decrypted_version_data) {
                LOG("version 解密失败");
                goto cleanup;
            }

            cJSON* version_item = cJSON_CreateString(decrypted_version_data);
            if (version_item) {
                cJSON_AddItemToObject(request_json_obj, version, version_item);
                LOG("已添加版本号: %s", decrypted_version_data);
            }
        }

        // 添加 id 参数
        if (config->product_id) {
            decrypted_product_id_data = decrypt_string_v2(config->product_id, config->product_id_length);
            if (!decrypted_product_id_data) {
                LOG("product_id 解密失败");
                goto cleanup;
            }

            cJSON* id_item = cJSON_CreateString(decrypted_product_id_data);
            if (id_item) {
                cJSON_AddItemToObject(request_json_obj, product_id, id_item);
                LOG("已添加插件ID: %s", decrypted_product_id_data);
            }
        }

        // 添加 action 参数
        if (config->action) {
            cJSON* action_item = cJSON_CreateString(config->action);
            if (action_item) {
                cJSON_AddItemToObject(request_json_obj, action, action_item);
                LOG("已添加操作类型: %s", config->action);
            }
        }

        // 转换回 JSON 字符串
        enhanced_request_data = cJSON_Print(request_json_obj);
        cJSON_Delete(request_json_obj);

        if (!enhanced_request_data) {
            result = create_error_result("生成增强请求数据失败");
            goto cleanup;
        }

        LOG("增强请求数据生成成功: %s", enhanced_request_data);
    }

    // 确定最终使用的请求数据
    const char* final_request_data = NULL;
    if (enhanced_request_data) {
        // 优先使用增强后的数据
        final_request_data = enhanced_request_data;
    } else if (config->request_data && strlen(config->request_data) > 0) {
        // 使用原始数据
        final_request_data = config->request_data;
    } else {
        // 如果都没有，创建一个空的 JSON 对象
        enhanced_request_data = strdup("{}");
        final_request_data = enhanced_request_data;
        LOG("使用默认空JSON对象: %s", final_request_data);
    }

    if (!final_request_data) {
        result = create_error_result("无法确定请求数据");
        goto cleanup;
    }

    // 步骤1: 生成加密密钥
    encryption_key = generate_encryption_key();
    if (!encryption_key) {
        result = create_error_result("生成加密密钥失败");
        goto cleanup;
    }

    LOG("加密密钥生成成功");

    // 步骤2: 加密请求数据（使用增强后的数据）
    encrypted_data = encrypt_request_params_with_hex_key(
        (const uint8_t*)final_request_data,
        strlen(final_request_data),
        encryption_key
    );
    if (!encrypted_data) {
        result = create_error_result("加密请求数据失败");
        goto cleanup;
    }

    LOG("请求数据加密成功");

    // 步骤3: 生成数字签名
    signature_key = decrypt_string_v2(SIGNATURE_KEYS, sizeof(SIGNATURE_KEYS));
    if (!signature_key) {
        result = create_error_result("获取签名密钥失败");
        goto cleanup;
    }

    signature = get_secure_signature(encrypted_data, signature_key);
    if (!signature) {
        result = create_error_result("生成数字签名失败");
        goto cleanup;
    }

    LOG("数字签名生成成功");

    // 步骤4: 创建请求JSON
    request_json = create_request_json(encrypted_data, signature, timestamp);
    if (!request_json) {
        result = create_error_result("创建请求JSON失败");
        goto cleanup;
    }

    LOG("请求JSON创建成功");

    // 步骤5: 发送HTTP请求
    const char* headers = config->custom_headers ? config->custom_headers : DEFAULT_HEADERS;
    int status_code = 0;
    
    // 解密基础服务器URL
    char* base_server_url = decrypt_string_v2(serverUrlKey, sizeof(serverUrlKey));
    if (!base_server_url) {
        LOG("解密服务器地址失败");
        result = create_error_result("URL error");
        goto cleanup;
    }

    // 构建最终的服务器URL（基础URL + path）
    if (config->path && strlen(config->path) > 0) {
        // 计算所需的缓冲区大小
        size_t base_len = strlen(base_server_url);
        size_t path_len = strlen(config->path);
        size_t total_len = base_len + path_len + 1; // +1 for null terminator

        // 检查基础URL是否以'/'结尾，path是否以'/'开头
        bool base_ends_with_slash = (base_len > 0 && base_server_url[base_len - 1] == '/');
        bool path_starts_with_slash = (config->path[0] == '/');

        if (base_ends_with_slash && path_starts_with_slash) {
            // 都有'/'，需要去掉一个
            total_len--;
        } else if (!base_ends_with_slash && !path_starts_with_slash) {
            // 都没有'/'，需要添加一个
            total_len++;
        }

        server_url = malloc(total_len);
        if (!server_url) {
            free(base_server_url);
            result = create_error_result("分配URL内存失败");
            goto cleanup;
        }

        // 拼接URL
        if (base_ends_with_slash && path_starts_with_slash) {
            // 去掉path开头的'/'
            snprintf(server_url, total_len, "%s%s", base_server_url, config->path + 1);
        } else if (!base_ends_with_slash && !path_starts_with_slash) {
            // 添加'/'
            snprintf(server_url, total_len, "%s/%s", base_server_url, config->path);
        } else {
            // 直接拼接
            snprintf(server_url, total_len, "%s%s", base_server_url, config->path);
        }

        LOG("构建最终URL: %s (基础: %s + 路径: %s)", server_url, base_server_url, config->path);
        free(base_server_url);
    } else {
        // 没有提供path，直接使用基础URL
        server_url = base_server_url;
        LOG("使用基础URL: %s", server_url);
    }

    bool http_success = socket_post_request_sync(
        server_url, request_json, headers, &response, &status_code);

    LOG("HTTP请求响应: %s", response);

    if (!http_success) {
        char error_msg[MAX_ERROR_MESSAGE_SIZE];
        snprintf(error_msg, sizeof(error_msg),
            "HTTP请求失败: %s", response ? response : "网络错误");
        result = create_error_result(error_msg);
        goto cleanup;
    }

    result.http_status_code = status_code;
    LOG("HTTP请求完成，状态码: %d", status_code);

    // 检查HTTP状态码，只有200才继续处理响应
    if (status_code != 200) {
        char error_msg[MAX_ERROR_MESSAGE_SIZE];
        snprintf(error_msg, sizeof(error_msg), "HTTP状态码错误: %d", status_code);

        // 创建错误结果但保留实际的状态码
        result = create_error_result(error_msg);
        result.http_status_code = status_code;  // 确保状态码被正确保留

        LOG("HTTP状态码验证失败: %d", status_code);
        goto cleanup;
    }

    LOG("HTTP状态码验证通过: %d", status_code);

    // 保存原始响应用于调试
    // if (response) {
    //     result.raw_response = malloc(strlen(response) + 1);
    //     if (result.raw_response) {
    //         strcpy(result.raw_response, response);
    //     }
    // }

    // 步骤6: 解析响应JSON
    response_json = JSONUtils_ParseString(response);
    if (!response_json) {
        result = create_error_result("响应JSON解析失败");
        goto cleanup;
    }

    const char* response_data = JSONUtils_GetStringValue(response_json, "data");
    const char* response_sign = JSONUtils_GetStringValue(response_json, "sign");
    const char* response_key = JSONUtils_GetStringValue(response_json, "key");

    if (!response_data || !response_sign || !response_key) {
        result = create_error_result("响应JSON格式无效：缺少data、sign或key字段");
        goto cleanup;
    }

    LOG("响应JSON解析成功，包含key字段");

    // 步骤7: 解密并验证服务器时间戳
    // 服务器返回的key字段是加密的时间戳，需要用encryption_key的MD5值来解密
    decryption_key_for_timestamp = md5_string(encryption_key);
    if (!decryption_key_for_timestamp) {
        result = create_error_result("生成时间戳解密密钥失败");
        goto cleanup;
    }
    LOG("时间戳解密密钥生成成功: %s", decryption_key_for_timestamp);

    decrypted_timestamp_str = decrypt_response_data(response_key, decryption_key_for_timestamp);
    if (!decrypted_timestamp_str) {
        result = create_error_result("服务器时间戳解密失败");
        goto cleanup;
    }
    LOG("服务器时间戳解密成功: %s", decrypted_timestamp_str);

    // 将解密后的时间戳字符串转换为time_t
    time_t server_timestamp = (time_t)strtol(decrypted_timestamp_str, NULL, 10);
    if (server_timestamp == 0) {
        char error_msg[MAX_ERROR_MESSAGE_SIZE];
        snprintf(error_msg, sizeof(error_msg),
            "服务器时间戳格式无效: %s", decrypted_timestamp_str);
        result = create_error_result(error_msg);
        goto cleanup;
    }

    // 获取当前本地时间
    time_t current_time = time(NULL);

    // 计算服务器时间与本地时间的差值（绝对值）
    time_t time_diff = current_time - server_timestamp;
    if (time_diff < 0) time_diff = -time_diff;  // 取绝对值

    // 检查时间差是否超过5秒
    const time_t MAX_TIME_DIFF_SECONDS = 5;
    if (time_diff > MAX_TIME_DIFF_SECONDS) {
        // char error_msg[MAX_ERROR_MESSAGE_SIZE];
        // snprintf(error_msg, sizeof(error_msg),
        //     "服务器时间戳验证失败 - 时间差过大: %ld秒 (阈值: %ld秒, 服务器时间: %ld, 本地时间: %ld)",
        //     time_diff, MAX_TIME_DIFF_SECONDS, server_timestamp, current_time);
        // result = create_error_result(error_msg);
        result = create_error_result("时间误差太大");
        goto cleanup;
    }

    LOG("✅ 服务器时间戳验证通过 - 时间差: %ld秒 (服务器: %ld, 本地: %ld)",
           time_diff, server_timestamp, current_time);

    // 步骤8: 验证响应签名
    if (!verify_response_signature(response_data, response_sign, encryption_key)) {
        result = create_error_result("响应签名验证失败");
        goto cleanup;
    }

    LOG("响应签名验证成功");

    // 步骤9: 解密响应数据
    decrypted_response = decrypt_response_data(response_data, encryption_key);
    if (!decrypted_response) {
        result = create_error_result("响应数据解密失败");
        goto cleanup;
    }

    LOG("响应数据解密成功");
    
    LOG("✅✅✅✅✅✅✅----> 解密成功响应数据: %s", decrypted_response);

    // 步骤10: 解析解密后的JSON数据
    result.response_data = JSONUtils_ParseString(decrypted_response);
    if (!result.response_data) {
        result = create_error_result("解密后的数据不是有效的JSON格式");
        goto cleanup;
    }

    // 设置成功状态
    result.success = true;
    result.error_message = NULL;

    LOG("加密请求流程完成，处理成功");

cleanup:
    // 安全清理所有临时变量
    if (action) free(action);
    if (product_id) free(product_id);
    if (version) free(version);
    if (serial_number_key) free(serial_number_key);
    if (decrypted_version_data) free(decrypted_version_data);
    if (decrypted_product_id_data) free(decrypted_product_id_data);
    if (encryption_key) {
        memset(encryption_key, 0, strlen(encryption_key));
        free(encryption_key);
    }
    if (encrypted_data) free(encrypted_data);
    if (signature_key) {
        memset(signature_key, 0, strlen(signature_key));
        free(signature_key);
    }
    if (signature) free(signature);
    if (request_json) free(request_json);
    if (response) free(response);
    if (decrypted_response) {
        memset(decrypted_response, 0, strlen(decrypted_response));
        free(decrypted_response);
    }
    if (response_json) cJSON_Delete(response_json);

    // 清理增强请求数据
    if (enhanced_request_data) {
        free(enhanced_request_data);
    }

    // 清理新增的变量
    if (server_url) {
        memset(server_url, 0, strlen(server_url));
        free(server_url);
    }
    if (decryption_key_for_timestamp) {
        memset(decryption_key_for_timestamp, 0, strlen(decryption_key_for_timestamp));
        free(decryption_key_for_timestamp);
    }
    if (decrypted_timestamp_str) {
        memset(decrypted_timestamp_str, 0, strlen(decrypted_timestamp_str));
        free(decrypted_timestamp_str);
    }

    // 如果失败，确保清理已分配的结果内存
    if (!result.success) {
        if (result.response_data) {
            cJSON_Delete(result.response_data);
            result.response_data = NULL;
        }
    }

    return result;
}

/**
 * @brief 安全释放加密请求结果结构体的内存
 */
void crypto_request_free_result(CryptoRequestResult* result) {
    if (!result) return;

    // 释放cJSON对象
    if (result->response_data) {
        cJSON_Delete(result->response_data);
        result->response_data = NULL;
    }

    // 释放错误信息字符串
    if (result->error_message) {
        free(result->error_message);
        result->error_message = NULL;
    }

    // 释放原始响应字符串
    // raw_response 字段已被注释掉，无需释放

    // 重置其他字段
    result->success = false;
    result->http_status_code = 0;
}
