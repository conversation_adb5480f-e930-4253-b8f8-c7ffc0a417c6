/**
 * @file CryptoRequestService.h
 * @brief 加密请求服务模块
 * 
 * 提供完整的加密请求发送和响应解密功能，包含内存管理和错误处理。
 * 封装了复杂的加密流程，提供简单易用的API接口。
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @date 2024
 */

#ifndef CRYPTO_REQUEST_SERVICE_H
#define CRYPTO_REQUEST_SERVICE_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "cJSON.h"

/**
 * @brief 加密请求响应结果结构体
 * 
 * 封装了加密请求的完整响应结果，包含状态、数据和错误信息。
 * 所有字段都经过安全的内存管理，使用后需要调用释放函数。
 */
typedef struct {
    bool success;           /**< 请求是否成功：true=成功，false=失败 */
    int http_status_code;   /**< HTTP状态码，如200、404、500等 */
    cJSON* response_data;   /**< 解密后的响应数据（JSON对象），可直接使用cJSON API */
    char* error_message;    /**< 错误信息字符串，成功时为NULL */
    // char* raw_response;     /**< 原始HTTP响应内容，用于调试，可能为NULL */
} CryptoRequestResult;

/**
 * @brief 加密请求配置结构体
 *
 * 包含发送加密请求所需的所有配置参数。
 * 提供灵活的配置选项，支持不同的服务端点和加密参数。
 */
typedef struct {
    // const char* server_url;     /**< 服务器URL，必须提供，如"http://192.168.1.100:9000/api" */
    const char* path;           /**< API路径，会与解密后的服务器基础URL拼接，如"/api/test" */
    const char* request_data;   /**< 要发送的请求数据（JSON格式字符串），可选，如果为空则自动创建 */
    const char* custom_headers; /**< 自定义HTTP头部，可选，默认为"Content-Type: application/json" */
    int timeout_seconds;        /**< 请求超时时间（秒），默认30秒，0表示使用默认值 */
    const char* action;         /**< 操作类型，会自动追加到请求数据中 */
    const char* serial_number;  /**< 设备序列号，可选，会自动追加到请求数据中 */
    const char* deviceInfo;     /**< 设备信息JSON字符串，可选，会自动追加到请求数据中 */
    const uint8_t* version;     /**< 版本号加密数据，会自动解密并追加到请求数据中 */
    size_t version_length;      /**< 版本号加密数据长度 */
    const uint8_t* product_id;  /**< 产品ID加密数据，会自动解密并追加到请求数据中 */
    size_t product_id_length;   /**< 产品ID加密数据长度 */
} CryptoRequestConfig;

/**
 * @brief 发送加密请求并解密响应
 * 
 * 执行完整的加密请求流程：
 * 1. 生成时间戳并进行多层加密处理
 * 2. 加密请求数据并生成数字签名
 * 3. 发送HTTP POST请求到指定服务器
 * 4. 检查HTTP状态码（只接受200）
 * 5. 解密并验证服务器时间戳（时间差不超过10秒）
 * 6. 验证响应签名确保数据完整性
 * 7. 解密响应数据并解析为JSON格式
 * 8. 返回结构化的结果数据
 * 
 * @param config 请求配置参数，包含URL、数据等信息
 * @return CryptoRequestResult 请求结果结构体，包含所有响应信息
 * 
 * @note 返回的结构体必须使用 crypto_request_free_result() 释放内存
 * @note 如果请求失败，success字段为false，error_message包含错误描述
 * @note 只有HTTP状态码为200时才会继续处理响应，其他状态码会导致失败
 * @note http_status_code字段始终包含实际的HTTP状态码，供调用者判断
 * @note 函数内部处理所有的内存分配和释放，调用者只需处理返回结果
 * 
 * @warning config参数不能为NULL
 * @warning 网络请求可能耗时较长，建议在后台线程中调用
 *
 * @example
 * CryptoRequestConfig config = {
 *     .request_data = "{\"data\":\"hello\"}",  // 可选，为空时自动创建
 *     .custom_headers = NULL,                  // 使用默认头部
 *     .timeout_seconds = 30,
 *     .path = "/api/test",                     // API路径，与基础URL拼接
 *     .action = "test_action",                 // 操作类型
 *     .serial_number = "ABC123",               // 设备序列号（可选）
 *     .deviceInfo = "{\"model\":\"iPhone\",\"os\":\"iOS\"}", // 设备信息JSON（可选）
 *     .version = version_data,                 // 版本号加密数据
 *     .version_length = version_data_length,   // 版本号数据长度
 *     .product_id = product_id_data,           // 产品ID加密数据
 *     .product_id_length = product_id_data_length  // 产品ID数据长度
 * };
 * 
 * CryptoRequestResult result = crypto_request_send_and_decrypt(&config);
 * if (result.success) {
 *     // 处理成功响应
 *     char* json_string = cJSON_Print(result.response_data);
 *     printf("响应数据: %s\n", json_string);
 *     free(json_string);
 * } else {
 *     // 处理错误
 *     printf("请求失败: %s\n", result.error_message);
 * }
 * crypto_request_free_result(&result);
 * 
 * @see crypto_request_free_result() 内存释放函数
 */
CryptoRequestResult crypto_request_send_and_decrypt(const CryptoRequestConfig* config);

/**
 * @brief 安全释放加密请求结果结构体的内存
 * 
 * 安全地释放CryptoRequestResult结构体中所有动态分配的内存，
 * 包括cJSON对象、字符串等，并将所有字段重置为安全状态。
 * 
 * @param result 要释放的CryptoRequestResult结构体指针，允许为NULL
 * 
 * @note 函数对NULL指针调用是安全的，不会产生任何副作用
 * @note 会正确释放cJSON对象（使用cJSON_Delete）和字符串内存
 * @note 释放后会将所有指针字段设置为NULL，success字段设置为false
 * @note 该函数不会释放结构体本身的内存（如果是动态分配的）
 * 
 * @warning 释放后不要再次使用结构体中的指针字段
 * @warning 不要手动释放结构体中的单个字段，统一使用此函数
 */
void crypto_request_free_result(CryptoRequestResult* result);


#endif // CRYPTO_REQUEST_SERVICE_H
