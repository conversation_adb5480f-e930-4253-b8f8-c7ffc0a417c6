#ifndef SOCKET_HTTP_H
#define SOCKET_HTTP_H


#include <stdbool.h>

// 同步POST请求
// url: 请求URL (格式: "http://domain.com/path" 或 "https://domain.com/path")
// data: POST数据
// headers: HTTP头信息，格式为"Key1: Value1\nKey2: Value2"，可为NULL
// response: 输出参数，用于存储响应内容，调用者负责释放内存
// statusCode: 输出参数，用于存储HTTP状态码
// 返回值: 请求是否成功
bool socket_post_request_sync(const char* url, const char* data, const char* headers, char** response, int* statusCode);

// 同步GET请求
// url: 请求URL (格式: "http://domain.com/path" 或 "https://domain.com/path")
// headers: HTTP头信息，格式为"Key1: Value1\nKey2: Value2"，可为NULL
// response: 输出参数，用于存储响应内容，调用者负责释放内存
// statusCode: 输出参数，用于存储HTTP状态码
// 返回值: 请求是否成功
bool socket_get_request_sync(const char* url, const char* headers, char** response, int* statusCode);

#endif /* SOCKET_HTTP_H
*/


/**

// 目标URL
    const char* url = "http://47.108.169.125:5001/callback";

    // 要发送的JSON数据
    const char* post_data = "{\"message\":\"Hello from C client!\", \"timestamp\":1719639375}";

    // 自定义HTTP头部（可选），这里我们添加了一个Authorization头
    const char* custom_headers = "Authorization: Bearer your_auth_token_here\n"
                                 "User-Agent: C_Socket_Client/1.0";

    char* response_body = NULL; // 用于存储服务器返回的响应体
    int status_code = 0;        // 用于存储HTTP状态码

    // printf("--- 准备发送HTTP POST请求 ---\n");
    // printf("目标URL: %s\n", url);
    // printf("发送数据: %s\n", post_data);
    // printf("自定义头部:\n%s\n", custom_headers);
    // printf("---------------------------\n\n");

    // 调用同步POST请求函数
    if (socket_post_request_sync(url, post_data, custom_headers, &response_body, &status_code)) {
        // printf("--- 请求成功 ---\n");
        // printf("HTTP 状态码: %d\n", status_code);
        syslog(LOG_NOTICE, "HTTP 状态码: %d", status_code);
        if (response_body) {
            // printf("服务器响应体:\n%s\n", response_body);
            syslog(LOG_NOTICE, "服务器响应体:\n%s", response_body);
            // 释放由 socket_post_request_sync 内部 malloc 分配的内存
            free(response_body);
        } else {
            printf("服务器未返回响应体。\n");
            syslog(LOG_NOTICE, "服务器未返回响应体。");
        }
    } else {
        // printf("--- 请求失败 ---\n");
        // printf("请检查 syslog 获取详细错误信息。\n");
        syslog(LOG_NOTICE, "--- 请求失败 ---");
    }
        
*/