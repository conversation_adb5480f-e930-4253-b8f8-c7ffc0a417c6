#include "SocketHTTP.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <netdb.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/select.h>
#include "Log.h"

// 解析URL
static bool parse_url(const char* url, char** host, char** path, int* port, bool* use_ssl) {
    if (!url || !host || !path || !port || !use_ssl) {
        return false;
    }

    *host = NULL;
    *path = NULL;
    *port = 0; // 初始化端口
    *use_ssl = false; // 初始化SSL状态

    const char* original_url = url; // 保存原始URL指针
    (void)original_url;

    // 检查URL是否以http://或https://开头
    if (strncmp(url, "http://", 7) == 0) {
        *use_ssl = false;
        url += 7;
        *port = 80;
    } else if (strncmp(url, "https://", 8) == 0) {
        *use_ssl = true;
        url += 8;
        *port = 443;
    } else {
        LOG("URL必须以http://或https://开头: %s", original_url);
        return false;
    }

    // 查找主机名结束位置
    const char* path_start = strchr(url, '/');
    // const char* temp_url_ptr = url; // 用于处理主机名和端口

    // 提取主机名（可能包含端口号）
    size_t host_len;
    if (!path_start) {
        // 如果没有路径，则URL的剩余部分都是主机名
        host_len = strlen(url);
        *path = strdup("/"); // 默认为根路径
        if (!*path) {
            LOG("内存分配失败 (path)");
            return false;
        }
    } else {
        host_len = path_start - url;
        *path = strdup(path_start);
        if (!*path) {
            LOG("内存分配失败 (path)");
            return false;
        }
    }

    char* temp_host = (char*)malloc(host_len + 1);
    if (!temp_host) {
        LOG("内存分配失败 (temp_host)");
        free(*path);
        *path = NULL;
        return false;
    }

    strncpy(temp_host, url, host_len);
    temp_host[host_len] = '\0';

    // 检查是否有端口号
    char* port_str = strchr(temp_host, ':');
    if (port_str) {
        *port_str = '\0'; // 截断主机名，只保留主机部分
        int parsed_port = atoi(port_str + 1);
        if (parsed_port > 0 && parsed_port <= 65535) {
            *port = parsed_port;
        } else {
            LOG("URL中端口号无效，使用默认端口: %d", *port);
            // 端口无效则使用默认端口，无需修改*port
        }
    }

    *host = temp_host;
    return true;
}

// 设置socket为非阻塞模式
static int set_nonblocking(int sock) {
    int flags = fcntl(sock, F_GETFL, 0);
    if (flags == -1) {
        LOG("获取socket标志失败: %s", strerror(errno));
        return -1;
    }

    if (fcntl(sock, F_SETFL, flags | O_NONBLOCK) == -1) {
        LOG("设置socket为非阻塞模式失败: %s", strerror(errno));
        return -1;
    }

    return 0;
}

// 创建socket连接（非阻塞方式）
static int create_socket_connection(const char* host, int port) {
    struct addrinfo hints, *res, *p;
    int status;
    int sock;

    memset(&hints, 0, sizeof hints);
    hints.ai_family = AF_UNSPEC; // 允许IPv4或IPv6
    hints.ai_socktype = SOCK_STREAM;

    char port_str[6];
    snprintf(port_str, sizeof(port_str), "%d", port);

    if ((status = getaddrinfo(host, port_str, &hints, &res)) != 0) {
        LOG("无法解析主机名或IP地址 %s: %s", host, gai_strerror(status));
        return -1;
    }

    // 遍历所有结果并尝试连接
    for (p = res; p != NULL; p = p->ai_next) {
        if ((sock = socket(p->ai_family, p->ai_socktype, p->ai_protocol)) == -1) {
            LOG("创建socket失败: %s", strerror(errno));
            continue;
        }

        // 设置超时（减少到5秒）
        struct timeval timeout;
        timeout.tv_sec = 5;  // 5秒超时
        timeout.tv_usec = 0;

        if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout)) < 0) {
            LOG("设置接收超时失败: %s", strerror(errno));
        }

        if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout)) < 0) {
            LOG("设置发送超时失败: %s", strerror(errno));
        }

        // 设置为非阻塞模式
        if (set_nonblocking(sock) < 0) {
            close(sock);
            continue;
        }

        // 尝试连接服务器（非阻塞方式）
        int ret = connect(sock, p->ai_addr, p->ai_addrlen);
        if (ret < 0) {
            if (errno != EINPROGRESS) {
                LOG("连接服务器失败 %s:%d, 错误: %s", host, port, strerror(errno));
                close(sock);
                continue;
            }

            // 使用select等待连接完成（iOS兼容）
            fd_set write_fds;
            FD_ZERO(&write_fds);
            FD_SET(sock, &write_fds);

            struct timeval timeout;
            timeout.tv_sec = 5;  // 5秒超时
            timeout.tv_usec = 0;

            // 等待连接完成
            int select_result = select(sock + 1, NULL, &write_fds, NULL, &timeout);
            if (select_result < 0) {
                LOG("select错误: %s", strerror(errno));
                close(sock);
                continue;
            } else if (select_result == 0) {
                LOG("连接服务器超时 %s:%d", host, port);
                close(sock);
                continue;
            }

            // 检查连接是否成功
            int error = 0;
            socklen_t len = sizeof(error);
            if (getsockopt(sock, SOL_SOCKET, SO_ERROR, &error, &len) < 0 || error != 0) {
                LOG("连接服务器失败 %s:%d, 错误: %s", host, port, strerror(error));
                close(sock);
                continue;
            }
        }

        // 连接成功，将socket设回阻塞模式
        int flags = fcntl(sock, F_GETFL, 0);
        if (flags == -1 || fcntl(sock, F_SETFL, flags & ~O_NONBLOCK) == -1) {
            LOG("将socket设回阻塞模式失败: %s", strerror(errno));
            // 即使失败，也尝试继续，但此socket可能行为异常
        }

        freeaddrinfo(res); // 释放addrinfo结构
        return sock; // 返回成功连接的socket
    }

    freeaddrinfo(res); // 释放addrinfo结构
    LOG("无法连接到任何地址 %s:%d", host, port);
    return -1; // 所有尝试都失败
}

// 构建HTTP请求
static char* build_http_request(const char* host, const char* path, const char* data, const char* headers) {
    if (!host || !path || !data) {
        LOG("构建HTTP请求参数无效");
        return NULL;
    }

    // 计算Content-Length
    size_t content_length = strlen(data);

    // 估算请求大小（多留一些空间以防万一）
    size_t estimated_size = 512 + strlen(path) + strlen(host) + content_length;
    if (headers) {
        estimated_size += strlen(headers);
    }

    // 分配内存
    char* request = (char*)malloc(estimated_size);
    if (!request) {
        LOG("内存分配失败 (HTTP请求)");
        return NULL;
    }

    // 构建基本请求头
    int offset = snprintf(request, estimated_size,
                          "POST %s HTTP/1.1\r\n"
                          "Host: %s\r\n"
                          "Content-Length: %zu\r\n"
                          "Content-Type: application/json; charset=utf-8\r\n"
                          "Connection: close\r\n", // 添加Connection: close 确保服务器关闭连接，便于判断响应结束
                          path, host, content_length);

    if (offset < 0 || (size_t)offset >= estimated_size) {
        LOG("HTTP请求头构建失败或缓冲区太小");
        free(request);
        return NULL;
    }

    // 添加自定义头
    if (headers) {
        // 由于strtok会修改原始字符串，我们复制一份
        char* headers_copy = strdup(headers);
        if (!headers_copy) {
            LOG("内存分配失败 (headers_copy)");
            free(request);
            return NULL;
        }

        char* line = headers_copy;
        char* next_line;
        while ((next_line = strchr(line, '\n')) != NULL) {
            *next_line = '\0'; // 替换换行符为null终止符
            // 确保不会超出缓冲区大小
            offset += snprintf(request + offset, estimated_size - offset, "%s\r\n", line);
            if (offset < 0 || (size_t)offset >= estimated_size) {
                LOG("HTTP自定义头构建失败或缓冲区太小");
                free(headers_copy);
                free(request);
                return NULL;
            }
            line = next_line + 1;
        }
        // 处理最后一行（如果没有以换行符结尾）
        if (*line != '\0') {
            offset += snprintf(request + offset, estimated_size - offset, "%s\r\n", line);
            if (offset < 0 || (size_t)offset >= estimated_size) {
                LOG("HTTP自定义头构建失败或缓冲区太小");
                free(headers_copy);
                free(request);
                return NULL;
            }
        }
        free(headers_copy);
    }

    // 添加空行和请求体
    offset += snprintf(request + offset, estimated_size - offset, "\r\n%s", data);
    if (offset < 0 || (size_t)offset >= estimated_size) {
        LOG("HTTP请求体构建失败或缓冲区太小");
        free(request);
        return NULL;
    }

    return request;
}

// 解析HTTP响应
static bool parse_http_response(const char* response, size_t response_size, char** body, int* status_code) {
    if (!response || !body || !status_code) {
        LOG("解析HTTP响应参数无效");
        return false;
    }

    *body = NULL;
    *status_code = 0;

    // 查找状态码
    if (response_size < 12 || strncmp(response, "HTTP/1.", 7) != 0) { // 至少需要"HTTP/1.X XXX\r\n"
        LOG("无效的HTTP响应格式: %.*s", (int)response_size > 20 ? 20 : (int)response_size, response);
        return false;
    }
    *status_code = atoi(response + 9); // HTTP/1.1 XXX

    // 查找响应体开始位置（两个连续的\r\n）
    const char* body_start = strstr(response, "\r\n\r\n");
    if (!body_start) {
        LOG("无法找到响应体分隔符");
        // 即使没有响应体，也可能是一个有效的响应，例如204 No Content
        // 如果需要返回空body，可以在这里处理
        *body = strdup(""); // 返回一个空字符串作为body
        if (!*body) {
             LOG("内存分配失败 (空body)");
        }
        return true;
    }

    body_start += 4;  // 跳过\r\n\r\n

    // 计算响应体长度
    size_t body_length = response_size - (body_start - response);

    // 分配内存并复制响应体
    *body = (char*)malloc(body_length + 1);
    if (!*body) {
        LOG("内存分配失败 (响应体)");
        return false;
    }

    memcpy(*body, body_start, body_length);
    (*body)[body_length] = '\0';

    return true;
}

// 同步POST请求
bool socket_post_request_sync(const char* url, const char* data, const char* headers, char** response_body, int* statusCode) {
    if (!url || !data || !response_body || !statusCode) {
        LOG("HTTP请求参数无效");
        return false;
    }

    LOG("开始HTTP请求: %s", url);

    *response_body = NULL;
    *statusCode = 0;

    char* host = NULL;
    char* path = NULL;
    int port = 0; // 初始化为0
    bool use_ssl = false;

    // 解析URL
    if (!parse_url(url, &host, &path, &port, &use_ssl)) {
        LOG("URL解析失败: %s", url);
        return false;
    }

    // 检查是否需要SSL
    if (use_ssl) {
        LOG("当前实现不支持HTTPS，请使用HTTP。URL: %s", url);
        free(host);
        free(path);
        return false;
    }

    // 创建socket连接
    LOG("正在连接服务器: %s:%d", host, port);
    int sock = create_socket_connection(host, port);
    if (sock == -1) {
        free(host);
        free(path);
        return false;
    }

    // 构建HTTP请求
    char* request = build_http_request(host, path, data, headers);
    if (!request) {
        LOG("构建HTTP请求失败");
        close(sock);
        free(host);
        free(path);
        return false;
    }

    // 发送请求
    LOG("发送POST请求到: %s%s", host, path);
    if (send(sock, request, strlen(request), 0) == -1) {
        LOG("发送请求失败: %s", strerror(errno));
        close(sock);
        free(request);
        free(host);
        free(path);
        return false;
    }

    // 接收响应
    char buffer[4096];
    char* resp_buffer = NULL;
    size_t resp_size = 0;
    ssize_t bytes_received;

    // 使用select监控socket是否有数据可读（iOS兼容）
    bool headers_received = false; // 标志是否已接收到完整头部

    while (1) {
        // 设置select参数
        fd_set read_fds;
        FD_ZERO(&read_fds);
        FD_SET(sock, &read_fds);

        struct timeval timeout;
        timeout.tv_sec = 5;  // 5秒超时
        timeout.tv_usec = 0;

        // 等待数据，最多等待5秒
        int select_result = select(sock + 1, &read_fds, NULL, NULL, &timeout);

        if (select_result < 0) {
            LOG("select错误: %s", strerror(errno));
            break;
        } else if (select_result == 0) {
            // 超时
            LOG("接收响应超时");
            break;
        }

        // 有数据可读
        bytes_received = recv(sock, buffer, sizeof(buffer) - 1, 0);

        if (bytes_received <= 0) {
            // 连接关闭或错误
            if (bytes_received < 0) {
                LOG("接收数据错误: %s", strerror(errno));
            }
            break; // 结束接收循环
        }

        // 重新分配内存
        char* new_buffer = (char*)realloc(resp_buffer, resp_size + bytes_received + 1);
        if (!new_buffer) {
            LOG("内存分配失败 (resp_buffer)");
            free(resp_buffer); // 释放之前已分配的内存
            close(sock);
            free(request);
            free(host);
            free(path);
            return false;
        }

        resp_buffer = new_buffer;

        // 复制新接收的数据
        memcpy(resp_buffer + resp_size, buffer, bytes_received);
        resp_size += bytes_received;
        resp_buffer[resp_size] = '\0'; // 确保null终止

        // 检查是否已接收完整响应头
        if (!headers_received) {
            if (strstr(resp_buffer, "\r\n\r\n")) {
                headers_received = true;
                // 可以从头部提取Content-Length来更精确地判断响应体是否接收完整
                // 这里为了简化，我们依赖连接关闭或recv返回0
            }
        }
    }

    // 关闭socket
    close(sock);

    // 解析响应
    bool success = false;
    if (resp_buffer && resp_size > 0) {
        success = parse_http_response(resp_buffer, resp_size, response_body, statusCode);
        LOG("HTTP请求完成，状态码: %d", *statusCode);
    } else {
        LOG("未收到响应数据或响应为空");
    }

    // 清理资源
    free(resp_buffer);
    free(request);
    free(host);
    free(path);

    return success;
}

// 构建HTTP GET请求
static char* build_http_get_request(const char* host, const char* path, const char* headers) {
    if (!host || !path) {
        LOG("构建HTTP GET请求参数无效");
        return NULL;
    }

    // 估算请求大小（多留一些空间以防万一）
    size_t estimated_size = 512 + strlen(path) + strlen(host);
    if (headers) {
        estimated_size += strlen(headers);
    }

    // 分配内存
    char* request = (char*)malloc(estimated_size);
    if (!request) {
        LOG("内存分配失败 (HTTP GET请求)");
        return NULL;
    }

    // 构建基本GET请求头
    int offset = snprintf(request, estimated_size,
                          "GET %s HTTP/1.1\r\n"
                          "Host: %s\r\n"
                          "Connection: close\r\n", // 添加Connection: close 确保服务器关闭连接
                          path, host);

    if (offset < 0 || (size_t)offset >= estimated_size) {
        LOG("HTTP GET请求头构建失败或缓冲区太小");
        free(request);
        return NULL;
    }

    // 添加自定义头
    if (headers) {
        // 由于strtok会修改原始字符串，我们复制一份
        char* headers_copy = strdup(headers);
        if (!headers_copy) {
            LOG("内存分配失败 (headers_copy)");
            free(request);
            return NULL;
        }

        char* line = headers_copy;
        char* next_line;
        while ((next_line = strchr(line, '\n')) != NULL) {
            *next_line = '\0'; // 替换换行符为null终止符
            // 确保不会超出缓冲区大小
            offset += snprintf(request + offset, estimated_size - offset, "%s\r\n", line);
            if (offset < 0 || (size_t)offset >= estimated_size) {
                LOG("HTTP GET自定义头构建失败或缓冲区太小");
                free(headers_copy);
                free(request);
                return NULL;
            }
            line = next_line + 1;
        }
        // 处理最后一行（如果没有以换行符结尾）
        if (*line != '\0') {
            offset += snprintf(request + offset, estimated_size - offset, "%s\r\n", line);
            if (offset < 0 || (size_t)offset >= estimated_size) {
                LOG("HTTP GET自定义头构建失败或缓冲区太小");
                free(headers_copy);
                free(request);
                return NULL;
            }
        }
        free(headers_copy);
    }

    // 添加空行结束请求头
    offset += snprintf(request + offset, estimated_size - offset, "\r\n");
    if (offset < 0 || (size_t)offset >= estimated_size) {
        LOG("HTTP GET请求结束符构建失败或缓冲区太小");
        free(request);
        return NULL;
    }

    return request;
}

// 同步GET请求
bool socket_get_request_sync(const char* url, const char* headers, char** response_body, int* statusCode) {
    if (!url || !response_body || !statusCode) {
        LOG("HTTP GET请求参数无效");
        return false;
    }

    LOG("开始HTTP GET请求: %s", url);

    *response_body = NULL;
    *statusCode = 0;

    char* host = NULL;
    char* path = NULL;
    int port = 0;
    bool use_ssl = false;

    // 解析URL
    if (!parse_url(url, &host, &path, &port, &use_ssl)) {
        LOG("URL解析失败: %s", url);
        return false;
    }

    // 检查是否需要SSL
    if (use_ssl) {
        LOG("当前实现不支持HTTPS，请使用HTTP。URL: %s", url);
        free(host);
        free(path);
        return false;
    }

    // 创建socket连接
    LOG("正在连接服务器: %s:%d", host, port);
    int sock = create_socket_connection(host, port);
    if (sock == -1) {
        free(host);
        free(path);
        return false;
    }

    // 构建HTTP GET请求
    char* request = build_http_get_request(host, path, headers);
    if (!request) {
        LOG("构建HTTP GET请求失败");
        close(sock);
        free(host);
        free(path);
        return false;
    }

    // 发送请求
    LOG("发送GET请求到: %s%s", host, path);
    if (send(sock, request, strlen(request), 0) == -1) {
        LOG("发送GET请求失败: %s", strerror(errno));
        close(sock);
        free(request);
        free(host);
        free(path);
        return false;
    }

    // 接收响应
    char buffer[4096];
    char* resp_buffer = NULL;
    size_t resp_size = 0;
    bool headers_received = false;

    while (1) {
        ssize_t bytes_received = recv(sock, buffer, sizeof(buffer) - 1, 0);
        if (bytes_received <= 0) {
            // 连接关闭或出错，停止接收
            break;
        }

        buffer[bytes_received] = '\0';

        // 重新分配缓冲区以容纳新数据
        char* new_buffer = (char*)realloc(resp_buffer, resp_size + bytes_received + 1);
        if (!new_buffer) {
            LOG("内存重新分配失败");
            free(resp_buffer);
            close(sock);
            free(request);
            free(host);
            free(path);
            return false;
        }

        resp_buffer = new_buffer;

        // 复制新接收的数据
        memcpy(resp_buffer + resp_size, buffer, bytes_received);
        resp_size += bytes_received;
        resp_buffer[resp_size] = '\0';

        // 检查是否已接收完整响应头
        if (!headers_received) {
            if (strstr(resp_buffer, "\r\n\r\n")) {
                headers_received = true;
            }
        }
    }

    // 关闭socket
    close(sock);

    // 解析响应
    bool success = false;
    if (resp_buffer && resp_size > 0) {
        success = parse_http_response(resp_buffer, resp_size, response_body, statusCode);
        LOG("HTTP GET请求完成，状态码: %d", *statusCode);
    } else {
        LOG("未收到响应数据或响应为空");
    }

    // 清理资源
    free(resp_buffer);
    free(request);
    free(host);
    free(path);

    return success;
}