#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include "Base64.h"
#include "Sha256.h"
#include "Signature.h"

// 安全常量
#define SALT_SIZE 32
#define KEY_SIZE 32
#define IV_SIZE 16
#define HMAC_SIZE 32

// 线程安全的随机数生成器状态
static pthread_mutex_t random_mutex = PTHREAD_MUTEX_INITIALIZER;
static int random_initialized = 0;

// 线程安全的伪随机数生成（仅作为最后回退）
static uint32_t thread_safe_rand(uint32_t* seed) {
    // 使用线性同余生成器（LCG）
    *seed = (*seed * 1103515245 + 12345) & 0x7fffffff;
    return *seed;
}

// 安全随机数生成（线程安全版本）
static int secure_random_bytes(uint8_t* buffer, size_t size) {
    // 首先尝试系统随机数
    int fd = open("/dev/urandom", O_RDONLY);
    if (fd >= 0) {
        ssize_t bytes_read = read(fd, buffer, size);
        close(fd);
        if (bytes_read == (ssize_t)size) {
            return 0;
        }
    }

    // 回退方案：线程安全的伪随机数
    pthread_mutex_lock(&random_mutex);

    if (!random_initialized) {
        // 使用更好的种子：时间 + 进程ID + 线程ID
        struct timeval tv;
        gettimeofday(&tv, NULL);
        random_initialized = 1;
    }

    // 为每个线程生成独立的种子
    uint32_t seed = (uint32_t)(time(NULL) ^ getpid() ^ (uintptr_t)pthread_self());

    for (size_t i = 0; i < size; i++) {
        buffer[i] = thread_safe_rand(&seed) & 0xFF;
    }

    pthread_mutex_unlock(&random_mutex);
    return 0;
}

// PBKDF2简化实现（用于密钥派生）
static void pbkdf2_sha256(const char* password, size_t password_len,
                         const uint8_t* salt, size_t salt_len,
                         int iterations, uint8_t* output, size_t output_len) {
    SHA256_CTX ctx;
    uint8_t temp[SHA256_BLOCK_SIZE];
    uint8_t u[SHA256_BLOCK_SIZE];
    
    // 简化的PBKDF2实现
    for (size_t i = 0; i < output_len; i += SHA256_BLOCK_SIZE) {
        // 初始化
        sha256_init(&ctx);
        sha256_update(&ctx, (uint8_t*)password, password_len);
        sha256_update(&ctx, salt, salt_len);
        
        // 添加计数器
        uint32_t counter = (uint32_t)(i / SHA256_BLOCK_SIZE + 1);
        uint8_t counter_bytes[4] = {
            (counter >> 24) & 0xFF,
            (counter >> 16) & 0xFF,
            (counter >> 8) & 0xFF,
            counter & 0xFF
        };
        sha256_update(&ctx, counter_bytes, 4);
        sha256_final(&ctx, u);
        
        memcpy(temp, u, SHA256_BLOCK_SIZE);
        
        // 迭代
        for (int j = 1; j < iterations; j++) {
            sha256_init(&ctx);
            sha256_update(&ctx, u, SHA256_BLOCK_SIZE);
            sha256_final(&ctx, u);
            
            for (int k = 0; k < SHA256_BLOCK_SIZE; k++) {
                temp[k] ^= u[k];
            }
        }
        
        // 复制到输出
        size_t copy_len = (output_len - i < SHA256_BLOCK_SIZE) ? 
                         (output_len - i) : SHA256_BLOCK_SIZE;
        memcpy(output + i, temp, copy_len);
    }
}

// HMAC-SHA256实现
static void hmac_sha256(const uint8_t* key, size_t key_len,
                       const uint8_t* data, size_t data_len,
                       uint8_t* output) {
    uint8_t ipad[64], opad[64];
    uint8_t key_pad[64];
    SHA256_CTX ctx;
    
    // 准备密钥
    memset(key_pad, 0, 64);
    if (key_len > 64) {
        sha256_init(&ctx);
        sha256_update(&ctx, key, key_len);
        sha256_final(&ctx, key_pad);
    } else {
        memcpy(key_pad, key, key_len);
    }
    
    // 准备ipad和opad
    for (int i = 0; i < 64; i++) {
        ipad[i] = key_pad[i] ^ 0x36;
        opad[i] = key_pad[i] ^ 0x5C;
    }
    
    // 内部哈希
    sha256_init(&ctx);
    sha256_update(&ctx, ipad, 64);
    sha256_update(&ctx, data, data_len);
    sha256_final(&ctx, output);
    
    // 外部哈希
    sha256_init(&ctx);
    sha256_update(&ctx, opad, 64);
    sha256_update(&ctx, output, SHA256_BLOCK_SIZE);
    sha256_final(&ctx, output);
}

// 简化的AES-256-CTR模式加密（使用XOR流密码模拟）
static void stream_encrypt(const uint8_t* key, const uint8_t* iv,
                          const uint8_t* input, uint8_t* output, size_t len) {
    SHA256_CTX ctx;
    uint8_t keystream[SHA256_BLOCK_SIZE];
    uint64_t counter = 0;
    
    for (size_t i = 0; i < len; i += SHA256_BLOCK_SIZE) {
        // 生成密钥流
        sha256_init(&ctx);
        sha256_update(&ctx, key, KEY_SIZE);
        sha256_update(&ctx, iv, IV_SIZE);
        
        uint8_t counter_bytes[8];
        for (int j = 0; j < 8; j++) {
            counter_bytes[j] = (counter >> (j * 8)) & 0xFF;
        }
        sha256_update(&ctx, counter_bytes, 8);
        sha256_final(&ctx, keystream);
        
        // XOR加密
        size_t block_len = (len - i < SHA256_BLOCK_SIZE) ? 
                          (len - i) : SHA256_BLOCK_SIZE;
        for (size_t j = 0; j < block_len; j++) {
            output[i + j] = input[i + j] ^ keystream[j];
        }
        
        counter++;
    }
}

// 安全签名生成函数（线程安全版本）
char* get_secure_signature(const char* input, const char* secret_key) {
    if (!input || !secret_key) {
        return NULL;
    }

    size_t input_len = strlen(input);
    size_t secret_len = strlen(secret_key);

    // 初始化所有指针为NULL，便于错误处理
    uint8_t* encrypted = NULL;
    uint8_t* hmac_input = NULL;
    uint8_t* signature_data = NULL;
    char* base64_signature = NULL;

    // 生成随机盐和IV
    uint8_t salt[SALT_SIZE];
    uint8_t iv[IV_SIZE];

    if (secure_random_bytes(salt, SALT_SIZE) != 0 ||
        secure_random_bytes(iv, IV_SIZE) != 0) {
        goto cleanup;
    }

    // 从密钥派生加密密钥
    uint8_t derived_key[KEY_SIZE];
    pbkdf2_sha256(secret_key, secret_len, salt, SALT_SIZE, 10000,
                  derived_key, KEY_SIZE);

    // 加密输入数据
    encrypted = malloc(input_len);
    if (!encrypted) {
        goto cleanup;
    }

    stream_encrypt(derived_key, iv, (uint8_t*)input, encrypted, input_len);

    // 计算HMAC
    uint8_t hmac[HMAC_SIZE];

    // 创建HMAC输入：salt + iv + encrypted_data
    size_t hmac_input_len = SALT_SIZE + IV_SIZE + input_len;
    hmac_input = malloc(hmac_input_len);
    if (!hmac_input) {
        goto cleanup;
    }

    memcpy(hmac_input, salt, SALT_SIZE);
    memcpy(hmac_input + SALT_SIZE, iv, IV_SIZE);
    memcpy(hmac_input + SALT_SIZE + IV_SIZE, encrypted, input_len);

    hmac_sha256(derived_key, KEY_SIZE, hmac_input, hmac_input_len, hmac);

    // 创建最终签名：salt + iv + hmac
    size_t signature_len = SALT_SIZE + IV_SIZE + HMAC_SIZE;
    signature_data = malloc(signature_len);
    if (!signature_data) {
        goto cleanup;
    }

    memcpy(signature_data, salt, SALT_SIZE);
    memcpy(signature_data + SALT_SIZE, iv, IV_SIZE);
    memcpy(signature_data + SALT_SIZE + IV_SIZE, hmac, HMAC_SIZE);

    // 转换为Base64
    base64_signature = base64_encode(signature_data, signature_len);

cleanup:
    // 安全清理敏感数据
    memset(derived_key, 0, KEY_SIZE);
    memset(salt, 0, SALT_SIZE);
    memset(iv, 0, IV_SIZE);

    if (encrypted) {
        memset(encrypted, 0, input_len);
        free(encrypted);
    }
    if (hmac_input) {
        memset(hmac_input, 0, hmac_input_len);
        free(hmac_input);
    }
    if (signature_data) {
        memset(signature_data, 0, signature_len);
        free(signature_data);
    }

    return base64_signature;
}

// 签名验证函数（线程安全版本）
bool verify_secure_signature(const char* input, const char* secret_key,
                            const char* signature_b64) {
    if (!input || !secret_key || !signature_b64) {
        return false;
    }

    size_t input_len = strlen(input);
    size_t secret_len = strlen(secret_key);

    // 初始化所有指针为NULL，便于错误处理
    uint8_t* decoded = NULL;
    uint8_t* encrypted = NULL;
    uint8_t* hmac_input = NULL;
    bool result = false;

    // 解码Base64签名
    int decoded_len;
    decoded = base64_decode(signature_b64, &decoded_len);
    if (!decoded || decoded_len != SALT_SIZE + IV_SIZE + HMAC_SIZE) {
        goto cleanup;
    }

    // 提取组件
    uint8_t* salt = decoded;
    uint8_t* iv = decoded + SALT_SIZE;
    uint8_t* received_hmac = decoded + SALT_SIZE + IV_SIZE;

    // 使用相同的盐派生密钥
    uint8_t derived_key[KEY_SIZE];
    pbkdf2_sha256(secret_key, secret_len, salt, SALT_SIZE, 10000,
                  derived_key, KEY_SIZE);

    // 加密输入数据
    encrypted = malloc(input_len);
    if (!encrypted) {
        goto cleanup;
    }

    stream_encrypt(derived_key, iv, (uint8_t*)input, encrypted, input_len);

    // 计算HMAC
    uint8_t computed_hmac[HMAC_SIZE];

    // 创建HMAC输入：salt + iv + encrypted_data
    size_t hmac_input_len = SALT_SIZE + IV_SIZE + input_len;
    hmac_input = malloc(hmac_input_len);
    if (!hmac_input) {
        goto cleanup;
    }

    memcpy(hmac_input, salt, SALT_SIZE);
    memcpy(hmac_input + SALT_SIZE, iv, IV_SIZE);
    memcpy(hmac_input + SALT_SIZE + IV_SIZE, encrypted, input_len);

    hmac_sha256(derived_key, KEY_SIZE, hmac_input, hmac_input_len, computed_hmac);

    // 比较HMAC（防时序攻击的安全比较）
    result = true;
    for (int i = 0; i < HMAC_SIZE; i++) {
        result &= (computed_hmac[i] == received_hmac[i]);
    }

cleanup:
    // 安全清理敏感数据
    memset(derived_key, 0, KEY_SIZE);
    memset(computed_hmac, 0, HMAC_SIZE);

    if (encrypted) {
        memset(encrypted, 0, input_len);
        free(encrypted);
    }
    if (hmac_input) {
        memset(hmac_input, 0, hmac_input_len);
        free(hmac_input);
    }
    if (decoded) {
        free(decoded);
    }

    return result;
}
