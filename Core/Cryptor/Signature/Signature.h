#ifndef Signature_H
#define Signature_H

#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 生成安全签名
 * 
 * @param input 要签名的输入数据
 * @param secret_key 密钥（应该是强密码）
 * @return Base64编码的签名字符串，失败返回NULL
 *         调用者负责释放返回的内存
 */
char* get_secure_signature(const char* input, const char* secret_key);

/**
 * 验证签名
 *
 * @param input 原始输入数据
 * @param secret_key 密钥
 * @param signature_b64 Base64编码的签名
 * @return true表示验证成功，false表示验证失败
 */
bool verify_secure_signature(const char* input, const char* secret_key,
                            const char* signature_b64);

#ifdef __cplusplus
}
#endif

#endif // Signature_H
