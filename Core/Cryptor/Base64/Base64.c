#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <limits.h>
 
#define BASE64_TABLE "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
 
// Base64编码函数
char* base64_encode(const unsigned char* input, int length) {
    // 输入验证
    if (!input || length < 0) {
        return NULL;
    }

    // 处理空输入
    if (length == 0) {
        char* encoded = (char*)malloc(1);
        if (encoded) {
            encoded[0] = '\0';
        }
        return encoded;
    }

    // 检查长度是否会导致整数溢出
    if (length > INT_MAX / 4 - 3) {
        return NULL;
    }

    int encoded_len = 4 * ((length + 2) / 3); // 计算编码后的长度
    char* encoded = (char*)malloc(encoded_len + 1); // 分配内存
    if (!encoded) {
        return NULL;
    }

    int i, j = 0;

    for (i = 0; i < length; i += 3) {
        int val = (input[i] << 16);  // 左移16位
        if (i + 1 < length) {
            val |= (input[i + 1] << 8); // 合并第二个字节
        }
        if (i + 2 < length) {
            val |= input[i + 2]; // 合并第三个字节
        }

        // 依次提取4个6位数据，转换为Base64字符
        encoded[j++] = BASE64_TABLE[(val >> 18) & 0x3F];  // 获取高6位
        encoded[j++] = BASE64_TABLE[(val >> 12) & 0x3F];  // 获取第二高6位
        encoded[j++] = (i + 1 < length) ? BASE64_TABLE[(val >> 6) & 0x3F] : '=';  // 获取第三高6位，若不足则填充
        encoded[j++] = (i + 2 < length) ? BASE64_TABLE[val & 0x3F] : '=';  // 获取低6位，若不足则填充
    }
    encoded[j] = '\0'; // 末尾添加结束符
    return encoded;
}
 
// Base64解码函数
unsigned char* base64_decode(const char* input, int* output_len) {
    // 输入验证
    if (!input || !output_len) {
        if (output_len) *output_len = 0;
        return NULL;
    }

    int length = strlen(input);

    // 检查长度
    if (length % 4 != 0 || length == 0) {
        *output_len = 0;
        return NULL; // 输入长度不是4的倍数，返回NULL
    }

    // 计算解码后的数据长度
    *output_len = length / 4 * 3;
    if (length >= 1 && input[length - 1] == '=') (*output_len)--;  // 判断末尾填充符号
    if (length >= 2 && input[length - 2] == '=') (*output_len)--;

    // 检查输出长度是否合理
    if (*output_len < 0) {
        *output_len = 0;
        return NULL;
    }

    unsigned char* decoded = (unsigned char*)malloc(*output_len);
    if (!decoded) {
        *output_len = 0;
        return NULL;
    }

    int i, j = 0, val = 0, valb = -8;

    for (i = 0; i < length; i++) {
        char c = input[i];
        if (c == '=') break;

        // 查找Base64字符的对应值
        int idx = -1;
        if (c >= 'A' && c <= 'Z') {
            idx = c - 'A';
        } else if (c >= 'a' && c <= 'z') {
            idx = c - 'a' + 26;
        } else if (c >= '0' && c <= '9') {
            idx = c - '0' + 52;
        } else if (c == '+') {
            idx = 62;
        } else if (c == '/') {
            idx = 63;
        } else {
            // 无效字符
            free(decoded);
            *output_len = 0;
            return NULL;
        }

        val = (val << 6) | idx;
        valb += 6;

        // 每解码出一个字节
        if (valb >= 0) {
            if (j >= *output_len) {
                // 防止缓冲区溢出
                free(decoded);
                *output_len = 0;
                return NULL;
            }
            decoded[j++] = (unsigned char)((val >> valb) & 0xFF);
            valb -= 8;
        }
    }
    return decoded;
}