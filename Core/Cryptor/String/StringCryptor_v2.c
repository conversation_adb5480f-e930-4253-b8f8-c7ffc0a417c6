#include "StringCryptor_v2.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/time.h>

// ================================
// 类型定义和常量
// ================================

typedef uint8_t byte_t;

// 简化的全局状态
typedef struct {
    volatile int initialized;
    volatile int cleanup_needed;
    pthread_mutex_t mutex;
    pthread_once_t init_once;
} crypto_state_t;

// 使用静态初始化，避免复杂的初始化逻辑
static crypto_state_t g_crypto_state = {
    .initialized = 0,
    .cleanup_needed = 0,
    .mutex = PTHREAD_MUTEX_INITIALIZER,
    .init_once = PTHREAD_ONCE_INIT
};

// 原始密钥模板 - 与原版本保持一致
static const byte_t master_key_template[32] = {
    0x8F ^ 0x55, 0x45 ^ 0x55, 0xA2 ^ 0x55, 0xD1 ^ 0x55, 0x3C ^ 0x55, 0x98 ^ 0x55, 0xE7 ^ 0x55, 0x56 ^ 0x55,
    0xB9 ^ 0x55, 0x0F ^ 0x55, 0x7D ^ 0x55, 0x2E ^ 0x55, 0x6A ^ 0x55, 0xC4 ^ 0x55, 0x1B ^ 0x55, 0x83 ^ 0x55,
    0x5D ^ 0x55, 0xF2 ^ 0x55, 0x91 ^ 0x55, 0x4E ^ 0x55, 0xA7 ^ 0x55, 0x30 ^ 0x55, 0xC8 ^ 0x55, 0x67 ^ 0x55,
    0xB2 ^ 0x55, 0x19 ^ 0x55, 0xD5 ^ 0x55, 0x8E ^ 0x55, 0x40 ^ 0x55, 0xF9 ^ 0x55, 0x76 ^ 0x55, 0x23 ^ 0x55
};

// 原始IV模板 - 与原版本保持一致
static const byte_t fixed_iv_template[16] = {
    0x12 ^ 0xAA, 0x34 ^ 0xAA, 0x56 ^ 0xAA, 0x78 ^ 0xAA, 0x9A ^ 0xAA, 0xBC ^ 0xAA, 0xDE ^ 0xAA, 0xF0 ^ 0xAA,
    0xAB ^ 0xAA, 0xCD ^ 0xAA, 0xEF ^ 0xAA, 0x12 ^ 0xAA, 0x34 ^ 0xAA, 0x56 ^ 0xAA, 0x78 ^ 0xAA, 0x9A ^ 0xAA
};

// 线程本地密钥存储
static __thread byte_t master_key[32];
static __thread byte_t fixed_iv[16];
static __thread int keys_initialized = 0;

// ================================
// 内部辅助函数
// ================================

// 简化的线程本地密钥初始化
static void ensure_keys_initialized(void) {
    if (!keys_initialized) {
        // 从模板复制并解混淆
        for (int i = 0; i < 32; i++) {
            master_key[i] = master_key_template[i] ^ 0x55;
        }
        for (int i = 0; i < 16; i++) {
            fixed_iv[i] = fixed_iv_template[i] ^ 0xAA;
        }
        keys_initialized = 1;
    }
}

// 全局初始化函数
static void init_crypto_once(void) {
    // 简单的一次性初始化
    g_crypto_state.initialized = 1;
}

// 安全的内存清零
static void secure_memset(void* ptr, int value, size_t size) {
    volatile unsigned char* p = (volatile unsigned char*)ptr;
    while (size--) {
        *p++ = value;
    }
}

// ================================
// 核心加密解密算法 (与原版本兼容)
// ================================

// 加密函数 - 保持与原版本相同的算法
static int encrypt_core(const char* input, byte_t* output, size_t* out_len) {
    ensure_keys_initialized();
    
    size_t input_len = strlen(input);
    
    // 复制IV到输出
    memcpy(output, fixed_iv, 16);
    
    // 加密数据 - 与原版本完全相同的算法
    for (size_t i = 0; i < input_len; i++) {
        byte_t b = input[i];
        b ^= master_key[i % 32];                    // 密钥XOR
        b = (b << 3) | (b >> 5);                    // 循环左移
        b ^= fixed_iv[i % 16];                      // IV XOR
        b += (i * 7 + 13) % 251;                    // 位置依赖加法
        b ^= master_key[(i + 16) % 32];             // 第二次密钥XOR
        
        // 额外的混淆层 - 基于前一个字节
        if (i > 0) {
            b ^= output[16 + i - 1] >> 4;
        }
        
        output[16 + i] = b;
    }
    
    // 添加校验和 - 与原版本相同
    byte_t sum1 = 0x33, sum2 = 0xAA;
    for (size_t i = 0; i < input_len; i++) {
        sum1 = (sum1 + output[16 + i]) % 251;
        sum2 = (sum2 + sum1) % 251;
    }
    
    // 固定噪声值，确保确定性加密
    byte_t noise = 0x07;
    
    output[16 + input_len] = sum1 ^ noise;
    output[16 + input_len + 1] = sum2 ^ noise;
    output[16 + input_len + 2] = ((sum1 ^ sum2) ^ 0xFF) ^ noise;
    output[16 + input_len + 3] = ((sum1 + sum2) % 251) ^ noise;
    
    *out_len = 16 + input_len + 4;
    return 0;
}

// 解密函数 - 保持与原版本相同的算法
static int decrypt_core(const byte_t* input, size_t input_len, char* output) {
    ensure_keys_initialized();
    
    // 检查输入长度
    if (input_len < 20) return -1;
    
    // 计算数据长度
    size_t data_len = input_len - 16 - 4;
    
    // 验证校验和
    byte_t sum1 = 0x33, sum2 = 0xAA;
    for (size_t i = 0; i < data_len; i++) {
        sum1 = (sum1 + input[16 + i]) % 251;
        sum2 = (sum2 + sum1) % 251;
    }
    
    // 提取噪声
    byte_t noise = (input[16 + data_len] ^ sum1) & 0x0F;
    
    // 验证校验和
    if ((sum1 ^ noise) != input[16 + data_len] || 
        (sum2 ^ noise) != input[16 + data_len + 1] ||
        (((sum1 ^ sum2) ^ 0xFF) ^ noise) != input[16 + data_len + 2] ||
        (((sum1 + sum2) % 251) ^ noise) != input[16 + data_len + 3]) {
        return -2;
    }
    
    // 解密数据 - 与原版本完全相同的算法
    for (size_t i = 0; i < data_len; i++) {
        byte_t b = input[16 + i];
        
        // 反向额外混淆层
        if (i > 0) {
            b ^= input[16 + i - 1] >> 4;
        }
        
        b ^= master_key[(i + 16) % 32];             // 反向第二次密钥XOR
        b -= (i * 7 + 13) % 251;                    // 反向位置依赖加法
        b ^= fixed_iv[i % 16];                      // 反向IV XOR
        b = (b >> 3) | (b << 5);                    // 反向循环左移
        b ^= master_key[i % 32];                    // 反向密钥XOR
        
        output[i] = b;
    }
    
    output[data_len] = '\0';
    return 0;
}

// ================================
// 公共API函数实现
// ================================

uint8_t* encrypt_string_v2(const char* input) {
    // 参数验证
    if (!input) {
        return NULL;
    }

    size_t input_len = strlen(input);
    if (input_len == 0 || input_len > 8192) {  // 限制最大长度
        return NULL;
    }

    // 确保初始化
    pthread_once(&g_crypto_state.init_once, init_crypto_once);

    // 计算缓冲区大小
    size_t buffer_size = 16 + input_len + 4;

    // 分配内存
    uint8_t* buffer = (uint8_t*)malloc(buffer_size);
    if (!buffer) {
        return NULL;
    }

    // 初始化缓冲区
    memset(buffer, 0, buffer_size);

    // 加密
    size_t actual_len = 0;
    int result = encrypt_core(input, buffer, &actual_len);

    if (result != 0 || actual_len != buffer_size) {
        secure_memset(buffer, 0, buffer_size);
        free(buffer);
        return NULL;
    }

    return buffer;
}

char* decrypt_string_v2(const uint8_t* encrypted, size_t encrypted_len) {
    // 参数验证
    if (!encrypted || encrypted_len < 20 || encrypted_len > 10240) {
        return NULL;
    }

    // 确保初始化
    pthread_once(&g_crypto_state.init_once, init_crypto_once);

    // 计算缓冲区大小
    size_t buffer_size = encrypted_len - 16 - 4 + 1;  // +1 for null terminator

    // 分配内存
    char* buffer = (char*)calloc(1, buffer_size);
    if (!buffer) {
        return NULL;
    }

    // 解密
    int result = decrypt_core(encrypted, encrypted_len, buffer);

    if (result != 0) {
        secure_memset(buffer, 0, buffer_size);
        free(buffer);
        return NULL;
    }

    // 验证结果
    size_t actual_len = strlen(buffer);
    if (actual_len == 0 || actual_len >= buffer_size) {
        secure_memset(buffer, 0, buffer_size);
        free(buffer);
        return NULL;
    }

    return buffer;
}

// ================================
// 辅助函数实现
// ================================

size_t calculate_encrypted_size_v2(const char* input) {
    if (!input) {
        return 0;
    }
    size_t input_len = strlen(input);
    return 16 + input_len + 4;
}

size_t calculate_decrypted_size_v2(size_t encrypted_len) {
    if (encrypted_len < 20) {
        return 0;
    }
    return encrypted_len - 16 - 4 + 1;
}

int string_cryptor_v2_init(void) {
    pthread_once(&g_crypto_state.init_once, init_crypto_once);
    return g_crypto_state.initialized ? 0 : -1;
}

void string_cryptor_v2_cleanup(void) {
    pthread_mutex_lock(&g_crypto_state.mutex);

    // 清理线程本地数据
    if (keys_initialized) {
        secure_memset(master_key, 0, sizeof(master_key));
        secure_memset(fixed_iv, 0, sizeof(fixed_iv));
        keys_initialized = 0;
    }

    // 标记需要清理
    g_crypto_state.cleanup_needed = 1;
    g_crypto_state.initialized = 0;

    pthread_mutex_unlock(&g_crypto_state.mutex);
}

int string_cryptor_v2_health_check(void) {
    return g_crypto_state.initialized && !g_crypto_state.cleanup_needed;
}

void print_hex_v2(const uint8_t* data, size_t len) {
    if (!data || len == 0) {
        printf("{ }\n");
        return;
    }

    // 限制最大长度
    size_t max_len = (len > 64) ? 64 : len;

    printf("{ ");
    for (size_t i = 0; i < max_len; i++) {
        printf("0x%02X%s", data[i], (i + 1 < max_len) ? ", " : " ");
        if ((i + 1) % 8 == 0 && i + 1 < max_len) {
            printf("\n  ");
        }
    }
    if (len > 64) {
        printf("... (truncated)");
    }
    printf("}\n");
}

// 自动清理函数
__attribute__((destructor))
static void auto_cleanup_v2(void) {
    string_cryptor_v2_cleanup();
}
