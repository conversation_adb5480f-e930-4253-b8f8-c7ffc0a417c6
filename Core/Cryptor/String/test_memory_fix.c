/**
 * @file test_memory_fix.c
 * @brief StringCryptor 内存修复验证测试
 * 
 * 用于验证连续解密不再导致内存崩溃的测试程序
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <time.h>
#include "StringCryptor.h"

// 测试数据
static const uint8_t test_keys[][32] = {
    { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x2F, 0xE9, 0x64, 0x97, 0x87, 0x03, 0xDD, 0x22, 0x07, 0x03 },
    { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xDF, 0x8E, 0x1A, 0x40, 0xEA, 0xE9, 0x21, 0x30, 0x1E },
    { 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0, 0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x9A, 0x07, 0xBB, 0x91, 0x98, 0x27, 0xB1, 0x02, 0xED, 0x17, 0xE8 }
};

static const size_t test_key_sizes[] = { 26, 25, 26 };
static const int NUM_TEST_KEYS = 3;

/**
 * @brief 单线程连续解密测试
 */
void test_sequential_decrypt() {
    printf("=== 单线程连续解密测试 ===\n");
    
    for (int round = 0; round < 10; round++) {
        printf("第 %d 轮测试...\n", round + 1);
        
        char* results[NUM_TEST_KEYS] = {NULL};
        int success_count = 0;
        
        // 连续解密所有测试密钥
        for (int i = 0; i < NUM_TEST_KEYS; i++) {
            results[i] = decrypt_string_auto(test_keys[i], test_key_sizes[i]);
            if (results[i]) {
                success_count++;
                printf("  密钥 %d 解密成功: %s\n", i, results[i]);
            } else {
                printf("  密钥 %d 解密失败\n", i);
            }
        }
        
        // 安全清理
        for (int i = 0; i < NUM_TEST_KEYS; i++) {
            if (results[i]) {
                memset(results[i], 0, strlen(results[i]));
                free(results[i]);
            }
        }
        
        printf("  本轮成功: %d/%d\n", success_count, NUM_TEST_KEYS);
        
        // 短暂休息
        usleep(10000); // 10ms
    }
    
    printf("单线程测试完成！\n\n");
}

/**
 * @brief 多线程测试的工作函数
 */
void* thread_decrypt_worker(void* arg) {
    int thread_id = *(int*)arg;
    printf("线程 %d 开始工作\n", thread_id);
    
    for (int i = 0; i < 5; i++) {
        int key_index = i % NUM_TEST_KEYS;
        char* result = decrypt_string_auto(test_keys[key_index], test_key_sizes[key_index]);
        
        if (result) {
            printf("线程 %d: 密钥 %d 解密成功\n", thread_id, key_index);
            memset(result, 0, strlen(result));
            free(result);
        } else {
            printf("线程 %d: 密钥 %d 解密失败\n", thread_id, key_index);
        }
        
        // 随机休息
        usleep((rand() % 5000) + 1000); // 1-6ms
    }
    
    printf("线程 %d 完成工作\n", thread_id);
    return NULL;
}

/**
 * @brief 多线程并发解密测试
 */
void test_concurrent_decrypt() {
    printf("=== 多线程并发解密测试 ===\n");
    
    const int NUM_THREADS = 4;
    pthread_t threads[NUM_THREADS];
    int thread_ids[NUM_THREADS];
    
    // 创建线程
    for (int i = 0; i < NUM_THREADS; i++) {
        thread_ids[i] = i;
        int result = pthread_create(&threads[i], NULL, thread_decrypt_worker, &thread_ids[i]);
        if (result != 0) {
            printf("创建线程 %d 失败: %d\n", i, result);
            return;
        }
    }
    
    // 等待所有线程完成
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }
    
    printf("多线程测试完成！\n\n");
}

/**
 * @brief 内存压力测试
 */
void test_memory_pressure() {
    printf("=== 内存压力测试 ===\n");
    
    const int ITERATIONS = 100;
    int success_count = 0;
    
    for (int i = 0; i < ITERATIONS; i++) {
        if (i % 10 == 0) {
            printf("进度: %d/%d\n", i, ITERATIONS);
        }
        
        // 快速连续解密
        for (int j = 0; j < NUM_TEST_KEYS; j++) {
            char* result = decrypt_string_auto(test_keys[j], test_key_sizes[j]);
            if (result) {
                success_count++;
                memset(result, 0, strlen(result));
                free(result);
            }
        }
    }
    
    printf("内存压力测试完成！\n");
    printf("总成功次数: %d/%d (%.1f%%)\n\n", 
           success_count, ITERATIONS * NUM_TEST_KEYS,
           (double)success_count / (ITERATIONS * NUM_TEST_KEYS) * 100.0);
}

/**
 * @brief 主测试函数
 */
int main() {
    printf("StringCryptor 内存修复验证测试\n");
    printf("================================\n\n");
    
    // 初始化随机数种子
    srand((unsigned int)time(NULL));
    
    // 运行各种测试
    test_sequential_decrypt();
    test_concurrent_decrypt();
    test_memory_pressure();
    
    printf("🎉 所有测试完成！如果程序正常结束，说明内存问题已修复。\n");
    
    return 0;
}
