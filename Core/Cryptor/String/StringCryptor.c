#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <signal.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#include <dlfcn.h>
#include <math.h>  // 用于fabs()函数
#include <pthread.h>  // 线程安全支持
#include <errno.h>
#include "Log.h"

// 混淆宏定义 - 修改为使用固定名称
#define _CONCAT(x, y) x##y
#define CONCAT(x, y) _CONCAT(x, y)
#define OBFUSCATE(x) (x)
// HIDE_STRING 宏已移除 - 未使用

// 简化的混淆宏 - 保留基本混淆但减少性能开销
#define LIGHT_OBFUSCATION do { \
    volatile int dummy = 0x12345678; \
    dummy ^= 0xDEADBEEF; \
    (void)dummy; \
} while(0)

// 使用固定后缀而非行号
#define VERIFY_INTEGRITY_NAME verify_integrity_impl
#define SECURITY_CHECK_NAME security_check_impl
#define INIT_SECURITY_NAME init_security_impl
#define SECURE_ENCRYPT_NAME secure_encrypt_impl
#define SECURE_DECRYPT_NAME secure_decrypt_impl

// 类型定义
typedef uint8_t byte_t;
typedef uint32_t word_t;

// 线程安全的全局状态 - 增强版本
typedef struct {
    volatile int integrity_ok;
    volatile int check_counter;
    volatile int keys_initialized;
    volatile unsigned int rand_seed;
    volatile uint32_t first_hash;
    volatile int init_in_progress;  // 新增：防止重复初始化
    volatile int cleanup_needed;    // 新增：标记是否需要清理
    pthread_mutex_t mutex;
    pthread_once_t init_once;
    // 新增：内存对齐填充，防止false sharing
    char padding[64 - (sizeof(int) * 5 + sizeof(uint32_t) + sizeof(unsigned int) + sizeof(pthread_mutex_t) + sizeof(pthread_once_t)) % 64];
} security_state_t;

// 使用静态初始化确保线程安全
static security_state_t g_security_state = {
    .integrity_ok = 1,
    .check_counter = 0,
    .keys_initialized = 0,
    .rand_seed = 0x12345678,
    .first_hash = 0,
    .init_in_progress = 0,
    .cleanup_needed = 0,
    .mutex = PTHREAD_MUTEX_INITIALIZER,
    .init_once = PTHREAD_ONCE_INIT,
    .padding = {0}
};

// 改进的线程安全随机数生成器 - 减少锁竞争和内存访问
static __thread unsigned int tls_rand_seed = 0;
static __thread int tls_initialized = 0;
static __thread int tls_call_count = 0;  // 新增：调用计数器

static int fast_rand(void) {
    if (!tls_initialized) {
        // 使用更安全的初始化方式
        struct timeval tv;
        gettimeofday(&tv, NULL);

        // 减少锁的持有时间
        pthread_mutex_lock(&g_security_state.mutex);
        unsigned int current_seed = g_security_state.rand_seed;
        g_security_state.rand_seed = (current_seed + 1) % 0x7FFFFFFF;  // 防止溢出
        pthread_mutex_unlock(&g_security_state.mutex);

        // 使用多个熵源初始化种子
        tls_rand_seed = current_seed ^
                       (unsigned int)(uintptr_t)pthread_self() ^
                       (unsigned int)tv.tv_usec ^
                       (unsigned int)tv.tv_sec;
        tls_initialized = 1;
        tls_call_count = 0;
    }

    // 定期重新初始化种子，防止周期性
    tls_call_count++;
    if (tls_call_count > 1000) {
        tls_initialized = 0;
        return fast_rand();  // 递归重新初始化
    }

    // 使用更好的随机数算法（xorshift）
    tls_rand_seed ^= tls_rand_seed << 13;
    tls_rand_seed ^= tls_rand_seed >> 17;
    tls_rand_seed ^= tls_rand_seed << 5;

    return tls_rand_seed % 32768;
}

// 原始密钥模板 - 只读常量，不会被修改
static const byte_t master_key_template[32] = {
    0x8F ^ 0x55, 0x45 ^ 0x55, 0xA2 ^ 0x55, 0xD1 ^ 0x55, 0x3C ^ 0x55, 0x98 ^ 0x55, 0xE7 ^ 0x55, 0x56 ^ 0x55,
    0xB9 ^ 0x55, 0x0F ^ 0x55, 0x7D ^ 0x55, 0x2E ^ 0x55, 0x6A ^ 0x55, 0xC4 ^ 0x55, 0x1B ^ 0x55, 0x83 ^ 0x55,
    0x5D ^ 0x55, 0xF2 ^ 0x55, 0x91 ^ 0x55, 0x4E ^ 0x55, 0xA7 ^ 0x55, 0x30 ^ 0x55, 0xC8 ^ 0x55, 0x67 ^ 0x55,
    0xB2 ^ 0x55, 0x19 ^ 0x55, 0xD5 ^ 0x55, 0x8E ^ 0x55, 0x40 ^ 0x55, 0xF9 ^ 0x55, 0x76 ^ 0x55, 0x23 ^ 0x55
};

// 原始IV模板 - 只读常量，不会被修改
static const byte_t fixed_iv_template[16] = {
    0x12 ^ 0xAA, 0x34 ^ 0xAA, 0x56 ^ 0xAA, 0x78 ^ 0xAA, 0x9A ^ 0xAA, 0xBC ^ 0xAA, 0xDE ^ 0xAA, 0xF0 ^ 0xAA,
    0xAB ^ 0xAA, 0xCD ^ 0xAA, 0xEF ^ 0xAA, 0x12 ^ 0xAA, 0x34 ^ 0xAA, 0x56 ^ 0xAA, 0x78 ^ 0xAA, 0x9A ^ 0xAA
};

// 线程安全的工作密钥 - 每次使用时从模板复制
static __thread byte_t master_key[32];
static __thread byte_t fixed_iv[16];
static __thread int keys_copied = 0;

// 函数声明
static int VERIFY_INTEGRITY_NAME(void);
static int SECURITY_CHECK_NAME(void);
static void INIT_SECURITY_NAME(void);
static int SECURE_ENCRYPT_NAME(const char *input, byte_t *output, size_t *out_len);
static int SECURE_DECRYPT_NAME(const byte_t *input, size_t input_len, char *output);

// 公共API函数声明 - 只保留核心函数
uint8_t* encrypt_string_auto(const char* input);  // 核心加密函数
char *decrypt_string_auto(const uint8_t *encrypted, size_t encrypted_len);  // 核心解密函数

// 辅助函数声明
size_t calculate_encrypted_size(const char* input);
size_t calculate_decrypted_size(size_t encrypted_len);

// 轻量级混淆延迟 - 减少性能开销
static void light_delay(void) {
    volatile int dummy = 0;
    for (volatile int i = 0; i < (fast_rand() % 10 + 1); i++) {
        dummy = (dummy * 31 + i) ^ 0xAAAA;
    }
    (void)dummy; // 防止编译器优化
}
// 调试器检测功能已移除，简化安全检查

// 代码完整性校验 - 使用FNV-1a哈希算法（线程安全版本）
static int VERIFY_INTEGRITY_NAME(void) {
    // 获取代码段起始地址
    uintptr_t code_start = (uintptr_t)&VERIFY_INTEGRITY_NAME;

    // 使用FNV-1a哈希算法
    const uint32_t FNV_PRIME = 16777619;
    const uint32_t FNV_OFFSET_BASIS = 2166136261;

    uint32_t hash = FNV_OFFSET_BASIS;

    // 计算代码段哈希值
    for (int i = 0; i < 256; i++) {
        byte_t byte = *((unsigned char*)(code_start + i));
        hash ^= byte;
        hash *= FNV_PRIME;

        // 添加位置依赖性，使哈希更难以预测
        if (i % 2 == 0) {
            hash = (hash << 7) | (hash >> 25); // 循环左移7位
        } else {
            hash = (hash << 11) | (hash >> 21); // 循环左移11位
        }
    }

    // 添加额外的混淆
    hash ^= (hash >> 16);
    hash *= 0x85ebca6b;
    hash ^= (hash >> 13);
    hash *= 0xc2b2ae35;
    hash ^= (hash >> 16);

    // 线程安全的哈希值比较 - 使用全局状态中的first_hash
    pthread_mutex_lock(&g_security_state.mutex);
    if (g_security_state.first_hash == 0) {
        g_security_state.first_hash = hash;
        pthread_mutex_unlock(&g_security_state.mutex);
        return 1;
    }

    int result = (hash == g_security_state.first_hash);
    pthread_mutex_unlock(&g_security_state.mutex);
    return result;
}

// 简化的安全性检查（改进版本）
static int SECURITY_CHECK_NAME(void) {
    int result;
    int should_verify = 0;

    pthread_mutex_lock(&g_security_state.mutex);

    // 定期进行完整性检查
    g_security_state.check_counter++;
    if (g_security_state.check_counter % 100 == 0) {
        should_verify = 1;
    }

    result = g_security_state.integrity_ok;
    pthread_mutex_unlock(&g_security_state.mutex);

    // 在锁外进行完整性检查，避免死锁
    if (should_verify) {
        if (!VERIFY_INTEGRITY_NAME()) {
            pthread_mutex_lock(&g_security_state.mutex);
            g_security_state.integrity_ok = 0;
            result = 0;
            pthread_mutex_unlock(&g_security_state.mutex);
        }
    }

    return result;
}

// 线程安全的密钥初始化函数
static void ensure_thread_keys_initialized(void) {
    if (!keys_copied) {
        // 从只读模板复制到线程本地存储
        for (int i = 0; i < 32; i++) {
            master_key[i] = master_key_template[i] ^ 0x55;
        }
        for (int i = 0; i < 16; i++) {
            fixed_iv[i] = fixed_iv_template[i] ^ 0xAA;
        }
        keys_copied = 1;
    }
}

// 改进的线程安全初始化函数
static void init_security_once(void) {
    // 确保线程本地密钥已初始化
    ensure_thread_keys_initialized();

    // 防止重复初始化全局状态
    if (__sync_bool_compare_and_swap(&g_security_state.init_in_progress, 0, 1)) {
        // 使用内存屏障确保写入顺序
        __sync_synchronize();

        // 标记初始化完成
        g_security_state.keys_initialized = 1;
        g_security_state.init_in_progress = 0;
    } else {
        // 等待其他线程完成初始化
        while (g_security_state.init_in_progress) {
            usleep(100);  // 等待100微秒
        }
    }
}

// 初始化安全环境
static void INIT_SECURITY_NAME(void) {
    // 使用pthread_once确保只初始化一次
    pthread_once(&g_security_state.init_once, init_security_once);

    // 初始安全性检查
    SECURITY_CHECK_NAME();
}

// 简化的加密函数
int SECURE_ENCRYPT_NAME(const char *input, byte_t *output, size_t *out_len) {
    // 基本的完整性检查
    if (!SECURITY_CHECK_NAME()) {
        return -1; // 完整性检查失败
    }
    
    size_t input_len = strlen(input);
    
    // 复制IV到输出
    memcpy(output, fixed_iv, 16);
    
    // 加密数据
    for (size_t i = 0; i < input_len; i++) {
        
        // 简单的多层加密
        byte_t b = input[i];
        b ^= master_key[i % 32];                    // 密钥XOR
        b = (b << 3) | (b >> 5);                    // 循环左移
        b ^= fixed_iv[i % 16];                      // IV XOR
        b += (i * 7 + 13) % 251;                    // 位置依赖加法
        b ^= master_key[(i + 16) % 32];             // 第二次密钥XOR
        
        // 额外的混淆层 - 基于前一个字节
        if (i > 0) {
            b ^= output[16 + i - 1] >> 4;
        }
        
        output[16 + i] = b;
        
        // 中间安全检查
        if (i % 10 == 0 && !SECURITY_CHECK_NAME()) {
            memset(output, 0, 16 + input_len + 4);
            *out_len = 0;
            return -1;
        }
    }
    
    // 添加简单校验和
    byte_t sum1 = 0x33, sum2 = 0xAA;
    for (size_t i = 0; i < input_len; i++) {
        sum1 = (sum1 + output[16 + i]) % 251;
        sum2 = (sum2 + sum1) % 251;
    }
    
    // 添加固定噪声到校验和 (确保相同输入产生相同输出)
    // byte_t noise = fast_rand() % 16;  // ❌ 随机噪声
    byte_t noise = 0x07;  // 固定噪声值
    
    output[16 + input_len] = sum1 ^ noise;
    output[16 + input_len + 1] = sum2 ^ noise;
    output[16 + input_len + 2] = ((sum1 ^ sum2) ^ 0xFF) ^ noise;
    output[16 + input_len + 3] = ((sum1 + sum2) % 251) ^ noise;
    
    *out_len = 16 + input_len + 4;
    return 0;
}

int SECURE_DECRYPT_NAME(const byte_t *input, size_t input_len, char *output) {
    // 确保线程本地密钥已初始化
    ensure_thread_keys_initialized();

    // 基本的完整性检查
    if (!SECURITY_CHECK_NAME()) {
        return -1; // 完整性检查失败
    }

    // 轻量混淆
    LIGHT_OBFUSCATION;
    
    // 检查输入长度
    if (input_len < 20) return -1;
    
    // 计算数据长度
    size_t data_len = input_len - 16 - 4;
    
    // 轻量混淆
    light_delay();
    
    // 验证校验和
    byte_t sum1 = 0x33, sum2 = 0xAA;
    for (size_t i = 0; i < data_len; i++) {
        sum1 = (sum1 + input[16 + i]) % 251;
        sum2 = (sum2 + sum1) % 251;
        
        // 轻量混淆
        if (i % 13 == 0) {
            LIGHT_OBFUSCATION;
        }
    }
    
    // 提取噪声
    byte_t noise = (input[16 + data_len] ^ sum1) & 0x0F;
    
    // 轻量混淆
    LIGHT_OBFUSCATION;
    
    // 验证校验和
    if ((sum1 ^ noise) != input[16 + data_len] || 
        (sum2 ^ noise) != input[16 + data_len + 1] ||
        (((sum1 ^ sum2) ^ 0xFF) ^ noise) != input[16 + data_len + 2] ||
        (((sum1 + sum2) % 251) ^ noise) != input[16 + data_len + 3]) {
        
        // 轻量混淆
        light_delay();
        return -2;
    }
    
    // 解密数据 - 增加随机延迟和无用指令
    for (size_t i = 0; i < data_len; i++) {
        // 轻量混淆
        if (i % 5 == 0) {
            LIGHT_OBFUSCATION;
        }
        
        // 轻量混淆
        if (i % 8 == 0) {
            light_delay();
        }
        
        byte_t b = input[16 + i];
        
        // 反向额外混淆层
        if (i > 0) {
            b ^= input[16 + i - 1] >> 4;
        }
        
        // 轻量混淆
        if (i % 3 == 2) {
            LIGHT_OBFUSCATION;
        }
        
        b ^= master_key[(i + 16) % 32];             // 反向第二次密钥XOR
        b -= (i * 7 + 13) % 251;                    // 反向位置依赖加法
        
        // 轻量混淆
        if (i % 6 == 3) {
            LIGHT_OBFUSCATION;
        }
        
        b ^= fixed_iv[i % 16];                      // 反向IV XOR
        b = (b >> 3) | (b << 5);                    // 反向循环左移
        b ^= master_key[i % 32];                    // 反向密钥XOR
        
        output[i] = b;
        
        // 中间安全检查
        if (i % 10 == 0 && !SECURITY_CHECK_NAME()) {
            memset(output, 0, data_len);
            return -1;
        }
    }
    
    // 轻量混淆
    light_delay();
    
    output[data_len] = '\0';
    return 0;
}
// 打印十六进制
// void print_hex(const byte_t *data, size_t len) {
//     printf("{ ");
//     LOG("{ ");
//     for (size_t i = 0; i < len; i++) {
//         printf("0x%02X%s", data[i], i + 1 < len ? ", " : " ");
//         LOg("0x%02X%s", data[i], i + 1 < len ? ", " : " ");
//         if ((i + 1) % 8 == 0 && i + 1 < len) printf("\n  ");
//     }
//     printf(" }\n");
//     LOG(" }\n");
// }

void print_hex(const byte_t *data, size_t len) {
    // 参数验证
    if (!data || len == 0) {
        printf("{ }\n");
        LOG("{ }");
        return;
    }

    // 限制最大长度，防止缓冲区溢出
    size_t max_len = (len > 100) ? 100 : len;

    printf("{ ");
    for (size_t i = 0; i < max_len; i++) {
        printf("0x%02X%s", data[i], (i + 1 < max_len) ? ", " : " ");
    }
    if (len > 100) {
        printf("... (truncated)");
    }
    printf("}\n");

    char buffer[1024] = {0};
    size_t offset = 0;
    offset += snprintf(buffer + offset, sizeof(buffer) - offset, "{ ");
    for (size_t i = 0; i < max_len && offset < sizeof(buffer) - 32; i++) {
        offset += snprintf(buffer + offset, sizeof(buffer) - offset,
                           "0x%02X%s", data[i], (i + 1 < max_len) ? ", " : " ");
    }
    if (len > 100 && offset < sizeof(buffer) - 20) {
        offset += snprintf(buffer + offset, sizeof(buffer) - offset, "... (truncated)");
    }
    snprintf(buffer + offset, sizeof(buffer) - offset, "}");
    LOG("%s", buffer);
}


/**
 * @brief 计算加密后需要的缓冲区大小
 *
 * @param input 要加密的字符串
 * @return size_t 加密后的数据大小（字节）
 */
size_t calculate_encrypted_size(const char* input) {
    if (!input) {
        return 0;
    }

    size_t input_len = strlen(input);

    // 公式: 16字节IV + 明文长度 + 4字节校验和
    return 16 + input_len + 4;
}

/**
 * @brief 计算解密后需要的缓冲区大小
 *
 * @param encrypted_len 加密数据的长度
 * @return size_t 解密后的数据大小（字节，包含null终止符）
 */
size_t calculate_decrypted_size(size_t encrypted_len) {
    if (encrypted_len < 20) {  // 最小有效加密数据长度
        return 0;
    }

    // 公式: 加密长度 - 16字节IV - 4字节校验和 + 1字节null终止符
    return encrypted_len - 16 - 4 + 1;
}

/**
 * @brief 自动内存管理的解密函数（推荐使用）
 *
 * 这个函数内部自动计算所需的缓冲区大小并分配内存，
 * 调用者无需关心缓冲区大小计算，使用更简单。
 *
 * @param encrypted 加密数据
 * @param encrypted_len 加密数据长度
 * @return char* 解密后的字符串，失败返回NULL。调用者负责释放内存。
 *
 * @note 相比 decrypt_to_buffer，这个函数更简单易用
 * @note 内部自动计算精确的缓冲区大小，避免内存浪费
 * @note 线程安全，可并发调用
 *
 * @example
 * char *result = decrypt_string_auto(encrypted_data, encrypted_len);
 * if (result) {
 *     printf("解密结果: %s\n", result);
 *     free(result);  // 记得释放内存
 * }
 */
char *decrypt_string_auto(const uint8_t *encrypted, size_t encrypted_len) {
    // 增强的参数验证
    if (!encrypted) {
        return NULL;
    }

    // 检查长度范围，防止整数溢出
    if (encrypted_len < 20 || encrypted_len > 10240) {  // 限制最大长度
        return NULL;
    }

    // 检查指针有效性（简单的地址范围检查）
    if ((uintptr_t)encrypted < 0x1000) {  // 防止空指针附近的地址
        return NULL;
    }

    // 确保线程本地密钥已初始化
    ensure_thread_keys_initialized();

    // 等待全局初始化完成
    while (g_security_state.init_in_progress) {
        usleep(100);  // 等待100微秒
    }

    // 初始化安全环境 - 确保密钥已解混淆
    INIT_SECURITY_NAME();

    // 再次检查初始化状态
    if (!g_security_state.keys_initialized) {
        return NULL;
    }

    // 自动计算精确的缓冲区大小
    size_t required_buffer_size = calculate_decrypted_size(encrypted_len);
    if (required_buffer_size == 0 || required_buffer_size > 8192) {  // 限制最大缓冲区
        return NULL;
    }

    // 使用calloc代替malloc+memset，更安全
    char *buffer = calloc(1, required_buffer_size);
    if (!buffer) {
        return NULL;  // 内存分配失败
    }

    // 在栈上创建输入数据的副本，防止输入数据被修改
    uint8_t *input_copy = malloc(encrypted_len);
    if (!input_copy) {
        free(buffer);
        return NULL;
    }
    memcpy(input_copy, encrypted, encrypted_len);

    // 直接调用底层解密函数
    int result = SECURE_DECRYPT_NAME(input_copy, encrypted_len, buffer);

    // 清理输入副本
    memset(input_copy, 0, encrypted_len);
    free(input_copy);

    if (result != 0) {
        // 解密失败 - 安全地清理并返回NULL
        memset(buffer, 0, required_buffer_size);  // 清零敏感数据
        free(buffer);
        return NULL;
    }

    // 验证解密结果的有效性
    size_t actual_len = strlen(buffer);
    if (actual_len == 0 || actual_len >= required_buffer_size) {
        memset(buffer, 0, required_buffer_size);
        free(buffer);
        return NULL;
    }

    return buffer;  // 成功，返回解密后的字符串
}

/**
 * @brief 清理StringCryptor资源
 *
 * 在程序退出时调用，清理所有全局状态和敏感数据
 */
void string_cryptor_cleanup(void) {
    pthread_mutex_lock(&g_security_state.mutex);

    // 清零线程本地敏感数据
    if (keys_copied) {
        memset(master_key, 0, sizeof(master_key));
        memset(fixed_iv, 0, sizeof(fixed_iv));
        keys_copied = 0;
    }

    // 重置全局状态
    g_security_state.integrity_ok = 0;
    g_security_state.check_counter = 0;
    g_security_state.keys_initialized = 0;
    g_security_state.rand_seed = 0;
    g_security_state.first_hash = 0;
    g_security_state.init_in_progress = 0;
    g_security_state.cleanup_needed = 1;

    pthread_mutex_unlock(&g_security_state.mutex);
}

/**
 * @brief 自动清理函数 - 程序退出时自动调用
 */
__attribute__((destructor))
static void auto_cleanup(void) {
    string_cryptor_cleanup();
}

/**
 * @brief 检查StringCryptor状态
 *
 * @return 1 if healthy, 0 if corrupted
 */
int string_cryptor_health_check(void) {
    return g_security_state.integrity_ok &&
           g_security_state.keys_initialized &&
           !g_security_state.cleanup_needed;
}

/**
 * @brief 最简洁的加密函数（推荐使用）
 *
 * 这是最简单易用的加密方式，内部自动计算缓冲区大小并分配内存，
 * 调用者只需传入字符串，无需关心任何内存管理细节。
 *
 * @param input 要加密的字符串
 * @return uint8_t* 加密后的数据，失败返回NULL。调用者负责释放内存。
 *                  可以通过 calculate_encrypted_size(input) 获取数据长度。
 *
 * @note 这是最简洁的加密接口，无需传递长度参数
 * @note 内部自动计算精确的缓冲区大小，避免内存浪费
 * @note 线程安全，可并发调用
 * @note 如需获取加密后的长度，使用 calculate_encrypted_size(input)
 *
 * @example
 * uint8_t *encrypted = encrypt_string_auto("Hello, World!");
 * if (encrypted) {
 *     size_t encrypted_len = calculate_encrypted_size("Hello, World!");
 *     printf("加密成功，长度: %zu\n", encrypted_len);
 *     free(encrypted);  // 记得释放内存
 * }
 */
uint8_t* encrypt_string_auto(const char* input) {
    // 参数验证
    if (!input) {
        return NULL;
    }

    // 检查输入长度限制
    size_t input_len = strlen(input);
    if (input_len == 0 || input_len > 200) {  // 限制最大输入长度
        return NULL;
    }

    // 初始化安全环境
    INIT_SECURITY_NAME();

    // 自动计算精确的缓冲区大小
    size_t required_buffer_size = calculate_encrypted_size(input);
    if (required_buffer_size == 0) {
        return NULL;  // 计算失败
    }

    // 动态分配精确大小的缓冲区
    uint8_t* buffer = (uint8_t*)malloc(required_buffer_size);
    if (!buffer) {
        return NULL;  // 内存分配失败
    }

    // 初始化缓冲区为零，确保安全
    memset(buffer, 0, required_buffer_size);

    // 直接调用底层加密函数
    size_t actual_encrypted_len = 0;
    int result = SECURE_ENCRYPT_NAME(input, buffer, &actual_encrypted_len);

    if (result != 0) {
        // 加密失败 - 安全地清理并返回NULL
        memset(buffer, 0, required_buffer_size);  // 清零敏感数据
        free(buffer);
        return NULL;
    }

    // 验证长度是否匹配（调试用，生产环境可以移除）
    if (actual_encrypted_len != required_buffer_size) {
        // 这种情况不应该发生，但为了安全起见
        memset(buffer, 0, required_buffer_size);
        free(buffer);
        return NULL;
    }

    return buffer;  // 成功，返回加密后的数据
}

#ifndef THREAD_SAFETY_TEST
int main() {
//     // printf("=== 缓冲区大小计算演示 ===\n");
//     // const char* test_strings = "SpringBoard";
//     // size_t str_len = strlen(test_strings);
//     // size_t encrypted_size = calculate_encrypted_size(test_strings);
//     // printf("  加密后大小: %zu 字节 (公式: 16 + %zu + 4)\n", encrypted_size, str_len);

//     printf("\n=== 核心函数测试 ===\n");
//     const char *test_string = "SpringBoard";

//     // 使用核心加密函数
//     uint8_t* encrypted = encrypt_string_auto(test_string);
//     if (encrypted) {
//         size_t encrypted_len = calculate_encrypted_size(test_string);
//         printf("✅ 加密成功，长度: %zu\n", encrypted_len);
//         print_hex(encrypted, encrypted_len);

//         // 使用核心解密函数
//         char *decrypted = decrypt_string_auto(encrypted, encrypted_len);
//         if (decrypted) {
//             printf("✅ 解密成功: %s\n", decrypted);
//             free(decrypted);
//         } else {
//             printf("❌ 解密失败\n");
//         }

//         free(encrypted);
//     } else {
//         printf("❌ 加密失败\n");
//     }

    // 先生成正确的加密数据
    // const char *original_text = "SpringBoard";
    // printf("=== 重新生成加密数据 ===\n");
    // printf("原始文本: %s\n", original_text);

    // uint8_t* encrypted = encrypt_string_auto(original_text);
    // if (encrypted) {
    //     size_t encrypted_len = calculate_encrypted_size(original_text);
    //     printf("✅ 加密成功，长度: %zu\n", encrypted_len);
    //     printf("加密数据: ");
    //     print_hex(encrypted, encrypted_len);

    //     // 测试解密
    //     char *decrypted = decrypt_string_auto(encrypted, encrypted_len);
    //     if (decrypted) {
    //         printf("✅ 解密成功: %s\n", decrypted);
    //         free(decrypted);
    //     } else {
    //         printf("❌ 解密失败\n");
    //     }

    //     free(encrypted);
    // } else {
    //     printf("❌ 加密失败\n");
    // }

    printf("\n=== 测试单独解密（模拟实际使用场景）===\n");
    static const uint8_t serviceNameKey[] = {0x12,0x34,0x56,0x78,0x9A,0xBC,0xDE,0xF0,0xAB,0xCD,0xEF,0x12,0x34,0x56,0x78,0x9A,0x5C,0x46,0x7E,0x96,0x9F,0x4A,0xE6,0x1E,0x01,0x6B,0xA9,0x03,0xD6,0x2D,0xD2};
    size_t key_len = sizeof(serviceNameKey);

    printf("加密数据长度: %zu\n", key_len);
    printf("加密数据: ");
    print_hex(serviceNameKey, key_len);

    char *progname = decrypt_string_auto(serviceNameKey, key_len);
    if (progname) {
        printf("✅ 单独解密成功: %s\n", progname);
        free(progname);
    } else {
        printf("❌ 单独解密失败\n");
    }

}
#endif