#ifndef STRINGCRYPTOR_H
#define STRINGCRYPTOR_H

/**
 * @file string_cryptor.h
 * @brief 线程安全的字符串加密解密库
 * @version 1.0
 * @date 2024
 *
 * 提供高效、安全的字符串加密解密功能，支持两种内存管理模式：
 * 1. 自动内存管理（推荐）：使用 encrypt_to_buffer + decrypt_to_buffer
 * 2. 手动内存管理：使用 encrypt_to_uint8 + decrypt_string
 */

#include <stdint.h>     // uint8_t 类型
#include <stddef.h>     // size_t 类型




#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 常量定义
// ================================

/** 加密数据的固定开销：16字节IV + 4字节校验和 */
#define ENCRYPTION_OVERHEAD 20

/** 推荐的缓冲区大小，适合大部分应用场景 */
#define RECOMMENDED_BUFFER_SIZE 256

/** 最大支持的明文长度 */
#define MAX_PLAINTEXT_LENGTH 200

// ================================
// 核心加密解密函数（推荐使用）
// ================================

/**
 * @brief 最简洁的加密函数（推荐使用）
 *
 * 这是最简单易用的加密方式，内部自动计算缓冲区大小并分配内存，
 * 调用者只需传入字符串，无需关心任何内存管理细节。
 *
 * @param input 要加密的字符串
 * @return uint8_t* 加密后的数据，失败返回NULL。调用者负责释放内存。
 *                  可以通过 calculate_encrypted_size(input) 获取数据长度。
 *
 * @note 这是最简洁的加密接口，无需传递长度参数
 * @note 内部自动计算精确的缓冲区大小，避免内存浪费
 * @note 线程安全，可并发调用
 * @note 如需获取加密后的长度，使用 calculate_encrypted_size(input)
 *
 * @example
 * uint8_t *encrypted = encrypt_string_auto("Hello, World!");
 * if (encrypted) {
 *     size_t encrypted_len = calculate_encrypted_size("Hello, World!");
 *     printf("加密成功，长度: %zu\n", encrypted_len);
 *     free(encrypted);  // 记得释放内存
 * }
 */
uint8_t* encrypt_string_auto(const char* input);

/**
 * @brief 自动内存管理的解密函数（推荐使用）
 *
 * 这是最简单易用的解密方式，内部自动计算缓冲区大小并分配内存，
 * 调用者无需关心缓冲区大小计算。
 *
 * @param encrypted 加密数据
 * @param encrypted_len 加密数据长度
 * @return char* 解密后的字符串，失败返回NULL。调用者负责释放内存。
 *
 * @note 相比其他解密函数，这个函数更简单易用
 * @note 内部自动计算精确的缓冲区大小，避免内存浪费
 * @note 线程安全，可并发调用
 * @note 返回的字符串已包含null终止符
 *
 * @example
 * char *result = decrypt_string_auto(encrypted_data, encrypted_len);
 * if (result) {
 *     printf("解密结果: %s\n", result);
 *     free(result);  // 记得释放内存
 * }
 */
char *decrypt_string_auto(const uint8_t* encrypted, size_t encrypted_len);

// ================================
// 辅助计算函数
// ================================

/**
 * @brief 计算加密后需要的缓冲区大小
 *
 * 用于预先计算加密缓冲区的精确大小，避免浪费内存。
 *
 * @param input 要加密的字符串
 * @return size_t 加密后的数据大小（字节），输入无效时返回0
 *
 * @note 计算公式：16字节IV + strlen(input) + 4字节校验和
 *
 * @example
 * const char* text = "Hello, World!";
 * size_t required_size = calculate_encrypted_size(text);
 * uint8_t* buffer = malloc(required_size);
 */
size_t calculate_encrypted_size(const char* input);

/**
 * @brief 计算解密后需要的缓冲区大小
 *
 * 用于预先计算解密缓冲区的精确大小，包含null终止符。
 *
 * @param encrypted_len 加密数据的长度
 * @return size_t 解密后的缓冲区大小（字节，包含null终止符），输入无效时返回0
 *
 * @note 计算公式：encrypted_len - 16字节IV - 4字节校验和 + 1字节null终止符
 *
 * @example
 * size_t buffer_size = calculate_decrypted_size(encrypted_len);
 * char* buffer = malloc(buffer_size);
 */
size_t calculate_decrypted_size(size_t encrypted_len);

// ================================
// 使用示例和最佳实践
// ================================

/*
 * 推荐使用方式：最简洁的自动内存管理
 *
 * const char* plaintext = "Hello, World!";
 *
 * // 加密 - 最简单的方式
 * uint8_t* encrypted = encrypt_string_auto(plaintext);
 * if (encrypted) {
 *     size_t encrypted_len = calculate_encrypted_size(plaintext);
 *     printf("加密成功，长度: %zu\n", encrypted_len);
 *
 *     // 解密 - 最简单的方式
 *     char* decrypted = decrypt_string_auto(encrypted, encrypted_len);
 *     if (decrypted) {
 *         printf("解密结果: %s\n", decrypted);
 *         free(decrypted);  // 释放解密结果
 *     }
 *     free(encrypted);  // 释放加密结果
 * }
 */

/**
 * @brief 清理StringCryptor资源
 *
 * 在程序退出时调用，清理所有全局状态和敏感数据。
 * 建议在应用程序退出前调用此函数。
 *
 * @note 调用此函数后，所有加密/解密功能将不可用
 * @note 此函数是线程安全的
 */
void string_cryptor_cleanup(void);

/**
 * @brief 检查StringCryptor状态
 *
 * 检查加密模块的健康状态，用于诊断问题。
 *
 * @return 1 表示状态正常，0 表示状态异常
 *
 * @note 如果返回0，建议重启应用程序或调用清理函数
 */
int string_cryptor_health_check(void);

// 类型定义
// typedef uint8_t byte_t;

// void print_hex(const byte_t *data, size_t len);

#ifdef __cplusplus
}
#endif

#endif /* STRINGCRYPTOR_H */