#ifndef STRING_CRYPTOR_V2_H
#define STRING_CRYPTOR_V2_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 核心加密解密函数 (简化版本)
// ================================

/**
 * @brief 加密字符串 (自动内存管理)
 * 
 * 简化版本的加密函数，保持与原版本相同的加密结果格式，
 * 但移除了复杂的线程安全机制和过度的安全检查。
 * 
 * @param input 要加密的字符串
 * @return uint8_t* 加密后的数据，失败返回NULL。调用者负责释放内存。
 * 
 * @note 线程安全，使用简化的同步机制
 * @note 加密结果格式：16字节IV + 加密数据 + 4字节校验和
 * @note 相同输入始终产生相同输出（确定性加密）
 */
uint8_t* encrypt_string_v2(const char* input);

/**
 * @brief 解密字符串 (自动内存管理)
 * 
 * 简化版本的解密函数，与原版本完全兼容，
 * 但使用更稳定的内存管理和错误处理。
 * 
 * @param encrypted 加密数据
 * @param encrypted_len 加密数据长度
 * @return char* 解密后的字符串，失败返回NULL。调用者负责释放内存。
 * 
 * @note 线程安全，无死锁风险
 * @note 兼容原版本的加密数据格式
 * @note 自动验证数据完整性
 */
char* decrypt_string_v2(const uint8_t* encrypted, size_t encrypted_len);

// ================================
// 辅助函数
// ================================

/**
 * @brief 计算加密后的数据大小
 * @param input 要加密的字符串
 * @return size_t 加密后的数据大小（字节）
 */
size_t calculate_encrypted_size_v2(const char* input);

/**
 * @brief 计算解密后的缓冲区大小
 * @param encrypted_len 加密数据长度
 * @return size_t 解密后的缓冲区大小（包含null终止符）
 */
size_t calculate_decrypted_size_v2(size_t encrypted_len);

/**
 * @brief 初始化加密库
 * 
 * 可选调用，用于预先初始化密钥。
 * 如果不调用，会在第一次加密/解密时自动初始化。
 * 
 * @return int 成功返回0，失败返回-1
 */
int string_cryptor_v2_init(void);

/**
 * @brief 清理加密库资源
 * 
 * 程序退出时调用，清理敏感数据。
 * 也会在程序退出时自动调用。
 */
void string_cryptor_v2_cleanup(void);

/**
 * @brief 检查库状态
 * @return int 正常返回1，异常返回0
 */
int string_cryptor_v2_health_check(void);

// ================================
// 调试辅助函数
// ================================

/**
 * @brief 打印十六进制数据 (调试用)
 * @param data 数据指针
 * @param len 数据长度
 */
void print_hex_v2(const uint8_t* data, size_t len);

// ================================
// 使用示例
// ================================

/*
 * 基本使用方式：
 * 
 * // 加密
 * uint8_t* encrypted = encrypt_string_v2("Hello, World!");
 * if (encrypted) {
 *     size_t encrypted_len = calculate_encrypted_size_v2("Hello, World!");
 *     
 *     // 解密
 *     char* decrypted = decrypt_string_v2(encrypted, encrypted_len);
 *     if (decrypted) {
 *         printf("解密结果: %s\n", decrypted);
 *         free(decrypted);
 *     }
 *     free(encrypted);
 * }
 * 
 * // 批量处理时的优化：
 * string_cryptor_v2_init();  // 预先初始化
 * 
 * for (int i = 0; i < 1000; i++) {
 *     uint8_t* encrypted = encrypt_string_v2(texts[i]);
 *     // ... 处理加密数据
 *     free(encrypted);
 * }
 * 
 * string_cryptor_v2_cleanup();  // 清理资源
 */

#ifdef __cplusplus
}
#endif

#endif // STRING_CRYPTOR_V2_H
