#ifndef CryptoKey_H
#define CryptoKey_H

#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 常量定义
#define MIN_KEY_LENGTH 8
#define MAX_KEY_LENGTH 256
#define BLOCK_SIZE 8

// 前向声明
typedef struct encryption_context_t encryption_context_t;

/**
 * 创建加密上下文
 * @return 成功返回上下文指针，失败返回NULL
 */
encryption_context_t* create_encryption_context(void);

/**
 * 销毁加密上下文并安全清理内存
 * @param ctx 要销毁的上下文
 */
void destroy_encryption_context(encryption_context_t* ctx);

/**
 * 加密数据（原地加密，会修改原始数据）
 * @param data 要加密的数据缓冲区
 * @param len 数据长度
 * @param buffer_size 缓冲区总大小（必须足够容纳填充后的数据）
 * @param user_key 用户密钥
 * @param key_len 密钥长度（MIN_KEY_LENGTH到MAX_KEY_LENGTH之间）
 * @return 成功返回加密后的数据长度，失败返回负数错误码
 *         -1: 参数错误
 *         -2: 缓冲区太小
 *         -3: 内存分配失败
 *         -4: 初始化失败
 */
int encrypt_data_with_user_key(uint8_t* data, size_t len, size_t buffer_size,
                               const uint8_t* user_key, size_t key_len);

/**
 * 解密数据（原地解密，会修改原始数据）
 * @param data 要解密的数据
 * @param len 数据长度（必须是BLOCK_SIZE的整数倍）
 * @param user_key 用户密钥
 * @param key_len 密钥长度（MIN_KEY_LENGTH到MAX_KEY_LENGTH之间）
 * @return 成功返回解密后的实际数据长度，失败返回负数错误码
 *         -1: 参数错误
 *         -2: 内存分配失败
 *         -3: 初始化失败
 *         -4: 无效填充
 *         -5: 填充长度超过数据长度
 *         -6: 填充验证失败
 */
int decrypt_data_with_user_key(uint8_t* data, size_t len, const uint8_t* user_key, size_t key_len);

/**
 * 加密数据并返回Base64编码的字符串
 * @param data 要加密的数据
 * @param len 数据长度
 * @param user_key 用户密钥
 * @param key_len 密钥长度（MIN_KEY_LENGTH到MAX_KEY_LENGTH之间）
 * @return 成功返回Base64编码的加密数据，失败返回NULL
 *         调用者负责释放返回的内存
 */
char* msg_encryption_with_key(const uint8_t* data, size_t len, const uint8_t* user_key, size_t key_len);

/**
 * 解密Base64编码的字符串并返回明文
 * @param base64_str Base64编码的加密数据
 * @param out_len 输出参数，存储解密后的数据长度
 * @param user_key 用户密钥
 * @param key_len 密钥长度（MIN_KEY_LENGTH到MAX_KEY_LENGTH之间）
 * @return 成功返回以NULL结尾的明文字符串，失败返回NULL
 *         调用者负责释放返回的内存
 */
char* msg_decryption_with_key(const char* base64_str, size_t* out_len,
                              const uint8_t* user_key, size_t key_len);

#ifdef __cplusplus
}
#endif

#endif // CryptoKey_H

