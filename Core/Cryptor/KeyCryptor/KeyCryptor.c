#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <pthread.h>
#include "Base64.h"

// 常量定义
#define BLOCK_SIZE 8
#define KEY_SIZE 48
#define ROUNDS 48
#define SBOX_SIZE 256
#define MIN_KEY_LENGTH 8
#define MAX_KEY_LENGTH 256

// 加密上下文结构，包含所有状态信息
typedef struct {
    uint8_t custom_sbox[SBOX_SIZE];
    uint32_t round_keys[ROUNDS];
    uint32_t whitebox_mix[ROUNDS];
    uint8_t derived_key[KEY_SIZE];
    int initialized;
    pthread_mutex_t mutex;
} encryption_context_t;

// 安全内存清理函数
static void secure_memzero(void* ptr, size_t len) {
    if (ptr == NULL || len == 0) return;
    volatile uint8_t* p = (volatile uint8_t*)ptr;
    for (size_t i = 0; i < len; i++) {
        p[i] = 0;
    }
}

// 控制流混淆
__attribute__((noinline))
static void fake_control_flow(uint32_t val) {
    if ((val & 0x5A) == 0x2A) {
        asm volatile("nop");
    } else {
        asm volatile("nop");
    }
}

// 创建加密上下文
encryption_context_t* create_encryption_context(void) {
    encryption_context_t* ctx = (encryption_context_t*)calloc(1, sizeof(encryption_context_t));
    if (!ctx) return NULL;

    if (pthread_mutex_init(&ctx->mutex, NULL) != 0) {
        free(ctx);
        return NULL;
    }

    return ctx;
}

// 销毁加密上下文
void destroy_encryption_context(encryption_context_t* ctx) {
    if (!ctx) return;

    pthread_mutex_lock(&ctx->mutex);

    // 安全清理敏感数据
    secure_memzero(ctx->custom_sbox, sizeof(ctx->custom_sbox));
    secure_memzero(ctx->round_keys, sizeof(ctx->round_keys));
    secure_memzero(ctx->whitebox_mix, sizeof(ctx->whitebox_mix));
    secure_memzero(ctx->derived_key, sizeof(ctx->derived_key));

    pthread_mutex_unlock(&ctx->mutex);
    pthread_mutex_destroy(&ctx->mutex);

    secure_memzero(ctx, sizeof(encryption_context_t));
    free(ctx);
}

// 安全的密钥派生函数（使用PBKDF2简化版本）
static void derive_key_from_user_input(const uint8_t* user_key, size_t key_len,
                                      const uint8_t* salt, size_t salt_len,
                                      uint8_t* out_key, size_t out_len) {
    if (!user_key || !out_key || key_len == 0 || out_len == 0) return;

    // 简化的PBKDF2实现
    uint8_t temp_key[KEY_SIZE];
    memset(temp_key, 0, sizeof(temp_key));

    // 如果用户密钥太长，截断；太短则重复填充
    for (size_t i = 0; i < KEY_SIZE; i++) {
        temp_key[i] = user_key[i % key_len];
    }

    // 多轮混淆增强安全性
    for (int round = 0; round < 1000; round++) {
        for (size_t i = 0; i < KEY_SIZE; i++) {
            temp_key[i] ^= (uint8_t)(round + i);
            if (salt && salt_len > 0) {
                temp_key[i] ^= salt[i % salt_len];
            }
            temp_key[i] = (temp_key[i] << (i % 3 + 1)) | (temp_key[i] >> (8 - (i % 3 + 1)));
        }
    }

    // 输出派生密钥
    size_t copy_len = (out_len < KEY_SIZE) ? out_len : KEY_SIZE;
    memcpy(out_key, temp_key, copy_len);

    // 清理临时密钥
    secure_memzero(temp_key, sizeof(temp_key));
}

// S盒初始化（多轮复杂置换）- 线程安全版本
static int init_custom_sbox_with_user_key(encryption_context_t* ctx, const uint8_t* user_key, size_t key_len) {
    if (!ctx || !user_key || key_len < MIN_KEY_LENGTH || key_len > MAX_KEY_LENGTH) {
        return -1;
    }

    pthread_mutex_lock(&ctx->mutex);

    if (ctx->initialized) {
        pthread_mutex_unlock(&ctx->mutex);
        return 0;
    }

    // 初始化S盒
    for (int i = 0; i < SBOX_SIZE; ++i) {
        ctx->custom_sbox[i] = i;
    }

    // 生成固定盐值（替代硬编码密钥）
    uint8_t salt[16] = {0xA5, 0x5A, 0xC3, 0x3C, 0x96, 0x69, 0x78, 0x87,
                        0x1F, 0x2E, 0x3D, 0x4C, 0x5B, 0x6A, 0x79, 0x88};

    // 派生密钥
    derive_key_from_user_input(user_key, key_len, salt, sizeof(salt),
                              ctx->derived_key, KEY_SIZE);

    // 第一轮置换
    uint32_t j = 0;
    for (int i = 0; i < SBOX_SIZE; ++i) {
        j = (j + ctx->custom_sbox[i] + ctx->derived_key[i % KEY_SIZE]) % SBOX_SIZE;
        uint8_t tmp = ctx->custom_sbox[i];
        ctx->custom_sbox[i] = ctx->custom_sbox[j];
        ctx->custom_sbox[j] = tmp;
    }

    // 第二轮置换
    for (int round = 0; round < 3; ++round) {
        for (int i = 0; i < SBOX_SIZE; ++i) {
            uint8_t idx = (ctx->custom_sbox[i] + ctx->derived_key[(i + round * 7) % KEY_SIZE]) % SBOX_SIZE;
            uint8_t tmp = ctx->custom_sbox[i];
            ctx->custom_sbox[i] = ctx->custom_sbox[idx];
            ctx->custom_sbox[idx] = tmp;
        }
    }

    // 非线性扰动
    for (int i = 0; i < SBOX_SIZE; ++i) {
        ctx->custom_sbox[i] = (ctx->custom_sbox[i] ^ ((i * 0x1D) ^ 0xC7) + (i >> 3)) & 0xFF;
    }

    ctx->initialized = 1;
    pthread_mutex_unlock(&ctx->mutex);
    return 0;
}

// 白盒混淆表生成 - 线程安全版本
static void derive_whitebox_mix_with_user_key(encryption_context_t* ctx) {
    for (int i = 0; i < ROUNDS; ++i) {
        ctx->whitebox_mix[i] = ((ctx->derived_key[i % KEY_SIZE] << 24) |
                               (ctx->derived_key[(i+1)%KEY_SIZE] << 16) |
                               (ctx->derived_key[(i+2)%KEY_SIZE] << 8) |
                               ctx->derived_key[(i+3)%KEY_SIZE]) ^ (0x9E3779B9 + i * 0x6A09E667);
    }
}

// 轮密钥扩展 - 线程安全版本
static void derive_round_keys_with_user_key(encryption_context_t* ctx) {
    for (int i = 0; i < ROUNDS; ++i) {
        size_t offset = ((i * 13) ^ (i >> 2)) % (KEY_SIZE - 3);
        ctx->round_keys[i] = ((uint32_t)ctx->derived_key[offset] << 24) |
                            ((uint32_t)ctx->derived_key[(offset + 1) % KEY_SIZE] << 16) |
                            ((uint32_t)ctx->derived_key[(offset + 2) % KEY_SIZE] << 8) |
                            ((uint32_t)ctx->derived_key[(offset + 3) % KEY_SIZE]);
        ctx->round_keys[i] ^= (ctx->round_keys[i] << (i % 11)) ^ ctx->whitebox_mix[i];
    }
}

// 轮函数（多级S盒+非线性）- 线程安全版本
static uint32_t sbox_nonlinear(encryption_context_t* ctx, uint32_t v, int round) {
    uint8_t b0 = ctx->custom_sbox[(v >> 24) & 0xFF];
    uint8_t b1 = ctx->custom_sbox[(v >> 16) & 0xFF];
    uint8_t b2 = ctx->custom_sbox[(v >> 8) & 0xFF];
    uint8_t b3 = ctx->custom_sbox[v & 0xFF];
    uint32_t r = ((b0 << 24) | (b1 << 16) | (b2 << 8) | b3);
    r ^= (r << ((round % 7) + 1));
    r ^= (r >> ((round % 5) + 3));
    return r;
}

static uint32_t f_function(encryption_context_t* ctx, uint32_t value, uint32_t round_key, int round) {
    uint32_t v = value ^ round_key ^ ctx->whitebox_mix[round];
    v = sbox_nonlinear(ctx, v, round);
    v = (v << ((round % 11) + 1)) | (v >> (32 - ((round % 11) + 1)));
    v ^= (v * 0x9E3779B9) ^ (v >> 13);
    fake_control_flow(v);
    return v;
}

// 单块加密 - 线程安全版本
static void encrypt_block(encryption_context_t* ctx, uint8_t* data) {
    uint32_t left, right;
    memcpy(&left, data, 4);
    memcpy(&right, data + 4, 4);
    for (int i = 0; i < ROUNDS; ++i) {
        uint32_t temp = f_function(ctx, right, ctx->round_keys[i], i);
        temp ^= left;
        left = right;
        right = temp;
    }
    memcpy(data, &right, 4);
    memcpy(data + 4, &left, 4);
}

// 单块解密 - 线程安全版本
static void decrypt_block(encryption_context_t* ctx, uint8_t* data) {
    uint32_t left, right;
    memcpy(&right, data, 4);
    memcpy(&left, data + 4, 4);
    for (int i = ROUNDS - 1; i >= 0; --i) {
        uint32_t temp = f_function(ctx, left, ctx->round_keys[i], i);
        temp ^= right;
        right = left;
        left = temp;
    }
    memcpy(data, &left, 4);
    memcpy(data + 4, &right, 4);
}

// 安全的加密函数 - 带缓冲区大小检查
int encrypt_data_with_user_key(uint8_t* data, size_t len, size_t buffer_size,
                               const uint8_t* user_key, size_t key_len) {
    if (!data || !user_key || len == 0 || key_len < MIN_KEY_LENGTH || key_len > MAX_KEY_LENGTH) {
        return -1;
    }

    // PKCS7填充：总是添加填充，即使长度已经是块大小的整数倍
    uint8_t padding = BLOCK_SIZE - (len % BLOCK_SIZE);
    if (padding == 0) padding = BLOCK_SIZE;
    size_t padded_len = len + padding;

    if (padded_len > buffer_size) {
        return -2; // 缓冲区太小
    }

    encryption_context_t* ctx = create_encryption_context();
    if (!ctx) return -3;

    if (init_custom_sbox_with_user_key(ctx, user_key, key_len) != 0) {
        destroy_encryption_context(ctx);
        return -4;
    }

    derive_whitebox_mix_with_user_key(ctx);
    derive_round_keys_with_user_key(ctx);

    // 添加PKCS7填充
    for (size_t i = len; i < padded_len; ++i) {
        data[i] = padding;
    }

    for (size_t i = 0; i < padded_len; i += BLOCK_SIZE) {
        encrypt_block(ctx, data + i);
    }

    destroy_encryption_context(ctx);
    return (int)padded_len;
}


// 安全的解密函数 - 带严格的填充验证
int decrypt_data_with_user_key(uint8_t* data, size_t len, const uint8_t* user_key, size_t key_len) {
    if (!data || !user_key || len == 0 || len % BLOCK_SIZE != 0 ||
        key_len < MIN_KEY_LENGTH || key_len > MAX_KEY_LENGTH) {
        return -1;
    }

    encryption_context_t* ctx = create_encryption_context();
    if (!ctx) return -2;

    if (init_custom_sbox_with_user_key(ctx, user_key, key_len) != 0) {
        destroy_encryption_context(ctx);
        return -3;
    }

    derive_whitebox_mix_with_user_key(ctx);
    derive_round_keys_with_user_key(ctx);

    // 解密所有数据块
    for (size_t i = 0; i < len; i += BLOCK_SIZE) {
        decrypt_block(ctx, data + i);
    }

    // 严格的填充验证
    uint8_t padding = data[len - 1];
    if (padding == 0 || padding > BLOCK_SIZE) {
        destroy_encryption_context(ctx);
        return -4; // 无效填充
    }

    // 检查填充长度是否合理
    if (padding > len) {
        destroy_encryption_context(ctx);
        return -5; // 填充长度超过数据长度
    }

    // 验证所有填充字节
    size_t padding_start = len - padding;
    for (size_t i = padding_start; i < len; ++i) {
        if (data[i] != padding) {
            destroy_encryption_context(ctx);
            return -6; // 填充验证失败
        }
    }

    destroy_encryption_context(ctx);
    return (int)padding_start; // 返回实际数据长度
}

// 加密并输出base64字符串 - 安全版本
// generate_encrypted_key
char* msg_encryption_with_key(const uint8_t* data, size_t len, const uint8_t* user_key, size_t key_len) {
    if (!data || !user_key || len == 0 || key_len < MIN_KEY_LENGTH || key_len > MAX_KEY_LENGTH) {
        return NULL;
    }

    // 计算填充后的长度（PKCS7标准）
    uint8_t padding = BLOCK_SIZE - (len % BLOCK_SIZE);
    if (padding == 0) padding = BLOCK_SIZE;
    size_t padded_len = len + padding;

    uint8_t* buffer = (uint8_t*)calloc(1, padded_len);
    if (!buffer) return NULL;

    memcpy(buffer, data, len);

    int result = encrypt_data_with_user_key(buffer, len, padded_len, user_key, key_len);
    if (result < 0) {
        secure_memzero(buffer, padded_len);
        free(buffer);
        return NULL;
    }

    char* base64_str = base64_encode(buffer, padded_len);
    secure_memzero(buffer, padded_len);
    free(buffer);
    return base64_str;
}


// 解密base64字符串，返回明文字符串（带\0结尾），实际长度写入*out_len - 安全版本
char* msg_decryption_with_key(const char* base64_str, size_t* out_len, const uint8_t* user_key, size_t key_len) {
    // 参数验证
    if (!base64_str || !out_len || !user_key || key_len < MIN_KEY_LENGTH || key_len > MAX_KEY_LENGTH) {
        if (out_len) *out_len = 0;
        return NULL;
    }

    // 检查输入字符串是否为有效的Base64
    size_t input_len = strlen(base64_str);
    if (input_len == 0 || input_len % 4 != 0) {
        *out_len = 0;
        return NULL;
    }

    int decoded_len = 0;
    unsigned char* decoded = base64_decode(base64_str, &decoded_len);
    if (!decoded || decoded_len == 0) {
        *out_len = 0;
        return NULL;
    }

    // 确保解码后的长度是块大小的整数倍
    if (decoded_len % BLOCK_SIZE != 0) {
        secure_memzero(decoded, decoded_len);
        free(decoded);
        *out_len = 0;
        return NULL;
    }

    int real_len = decrypt_data_with_user_key(decoded, decoded_len, user_key, key_len);
    if (real_len < 0) {
        secure_memzero(decoded, decoded_len);
        free(decoded);
        *out_len = 0;
        return NULL;
    }

    // 分配足够的内存并确保字符串结束符
    char* result = (char*)malloc(real_len + 1);
    if (!result) {
        secure_memzero(decoded, decoded_len);
        free(decoded);
        *out_len = 0;
        return NULL;
    }

    memcpy(result, decoded, real_len);
    result[real_len] = '\0';

    // 检查结果是否包含合理的字符
    int invalid_chars = 0;
    for (int i = 0; i < real_len; i++) {
        unsigned char c = result[i];
        if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
            invalid_chars++;
        }
    }

    // 如果无效字符过多（超过20%），认为解密失败
    if (real_len > 0 && invalid_chars > real_len / 5) {
        secure_memzero(result, real_len + 1);
        free(result);
        secure_memzero(decoded, decoded_len);
        free(decoded);
        *out_len = 0;
        return NULL;
    }

    *out_len = real_len;
    secure_memzero(decoded, decoded_len);
    free(decoded);
    return result;
}

