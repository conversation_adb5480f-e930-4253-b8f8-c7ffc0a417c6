#ifndef ParamCrypt_h
#define ParamCrypt_h

#include <stdint.h>
#include <stdlib.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file ParamCrypto.h
 * @brief 高性能数据加密解密库
 *
 * 本库提供基于自定义Feistel网络的高强度数据加密功能，
 * 采用48轮加密、自定义S盒、白盒混淆等多重安全机制。
 * 支持任意长度数据加密，输出Base64编码格式。
 *
 * <AUTHOR> Module
 * @version 2.0
 * @date 2024
 */

// 算法常量定义
#define ParamCrypt_KEY_SIZE     64      ///< 推荐密钥大小（字节）
#define ParamCrypt_BLOCK_SIZE   8       ///< 加密块大小（字节）
#define ParamCrypt_ROUNDS       48      ///< 加密轮数
#define ParamCrypt_SBOX_SIZE    256     ///< S盒大小
#define ParamCrypt_MAX_INPUT    (1024*1024)  ///< 最大输入大小（1MB）

/**
 * @brief 加密数据并返回Base64编码的结果
 *
 * 使用自定义Feistel网络对任意长度的数据进行加密。
 *
 * 算法特性：
 * - 48轮Feistel网络加密
 * - 自定义S盒和白盒混淆
 * - PKCS#7填充到8字节边界
 * - Base64编码输出
 *
 * @param data 要加密的原始数据
 *             - 不能为NULL
 *             - 支持任意二进制数据
 *             - 最大长度：ParamCrypt_MAX_INPUT字节
 * @param len 原始数据的长度（字节）
 *            - 必须大于0
 *            - 最大值：ParamCrypt_MAX_INPUT
 * @param user_key 用户提供的密钥
 *                 - 可以为NULL（使用内置默认密钥）
 *                 - 推荐长度：ParamCrypt_KEY_SIZE字节
 *                 - 支持任意长度（1-1024字节）
 * @param key_len 用户密钥的长度（字节）
 *                - 当user_key为NULL时，此参数被忽略
 *                - 推荐值：ParamCrypt_KEY_SIZE
 *
 * @return char* Base64编码的加密结果字符串
 *         - 成功：返回Base64字符串（需要free()释放）
 *         - 失败：返回NULL
 *
 * @note
 * - 返回的字符串长度约为原始数据长度的1.33倍（Base64编码）
 * - 相同的输入和密钥总是产生相同的输出（确定性加密）
 * - 加密过程自动进行PKCS#7填充
 * - 线程安全（使用固定种子初始化）
 *
 * @warning
 * - 必须使用free()释放返回的字符串
 * - 输入参数data和len不能为NULL/0
 * - 内存分配失败时返回NULL
 *
 * @example
 * ```c
 * // 使用默认密钥
 * const uint8_t data[] = "Hello, World!";
 * char* encrypted = encrypt_data_base64(data, strlen((char*)data), NULL, 0);
 * if (encrypted) {
 *     printf("加密结果: %s\n", encrypted);
 *     free(encrypted);
 * }
 *
 * // 使用自定义密钥
 * const uint8_t key[] = "MySecretKey123456789012345678901234567890123456789012345";
 * char* encrypted2 = encrypt_data_base64(data, strlen((char*)data), key, sizeof(key));
 * if (encrypted2) {
 *     printf("自定义密钥加密: %s\n", encrypted2);
 *     free(encrypted2);
 * }
 * ```
 */
char* encrypt_data_base64(const uint8_t* data, size_t len, const uint8_t* user_key, size_t key_len);

/**
 * @brief 从Base64编码的字符串解密数据
 *
 * 解密由encrypt_data_base64()生成的Base64编码数据。
 *
 * @param base64_str Base64编码的加密数据字符串
 *                   - 不能为NULL
 *                   - 必须是有效的Base64字符串
 *                   - 必须由encrypt_data_base64()生成
 * @param out_len 输出参数，返回解密后的数据长度
 *                - 不能为NULL
 *                - 成功时包含实际数据长度（去除填充）
 *                - 失败时设置为0
 * @param user_key 解密密钥
 *                 - 必须与加密时使用的密钥完全相同
 *                 - 可以为NULL（使用默认密钥）
 * @param key_len 密钥长度
 *                - 必须与加密时的key_len相同
 *                - 当user_key为NULL时忽略
 *
 * @return uint8_t* 解密后的原始数据
 *         - 成功：返回数据指针（需要free()释放）
 *         - 失败：返回NULL，out_len设置为0
 *
 * @note
 * - 自动去除PKCS#7填充
 * - 密钥必须与加密时完全一致
 * - 解密失败可能由于密钥错误或数据损坏
 *
 * @warning
 * - 必须使用free()释放返回的数据
 * - 密钥不匹配会导致解密失败或错误数据
 * - Base64字符串格式错误会导致解密失败
 *
 * @example
 * ```c
 * const char* encrypted_str = "SGVsbG8sIFdvcmxkIQ==";  // 示例加密数据
 * size_t decrypted_len = 0;
 * uint8_t* decrypted = decrypt_data_base64(encrypted_str, &decrypted_len, NULL, 0);
 * if (decrypted) {
 *     printf("解密结果: %.*s\n", (int)decrypted_len, decrypted);
 *     free(decrypted);
 * }
 * ```
 */
uint8_t* decrypt_data_base64(const char* base64_str, size_t* out_len, const uint8_t* user_key, size_t key_len);

/**
 * @brief 使用十六进制字符串作为密钥进行加密
 *
 * 便捷函数，将十六进制字符串转换为字节数组后调用encrypt_data_base64()。
 *
 * @param data 要加密的原始数据
 *             - 不能为NULL
 *             - 支持任意二进制数据
 * @param len 原始数据的长度（字节）
 *            - 必须大于0
 * @param hex_key_str 十六进制格式的密钥字符串
 *                    - 不能为NULL
 *                    - 格式：如"1A2B3C4D5E6F..."
 *                    - 长度必须为偶数（每2个字符表示1个字节）
 *                    - 推荐长度：128个字符（64字节密钥）
 *
 * @return char* Base64编码的加密结果字符串
 *         - 成功：返回Base64字符串（需要free()释放）
 *         - 失败：返回NULL
 *
 * @note
 * - 十六进制字符串不区分大小写
 * - 内部会自动转换十六进制字符串为字节数组
 * - 等价于先调用hex_string_to_bytes()再调用encrypt_data_base64()
 *
 * @warning
 * - 十六进制字符串格式错误会导致加密失败
 * - 必须使用free()释放返回的字符串
 *
 * @example
 * ```c
 * const uint8_t data[] = "Sensitive Data";
 * const char* hex_key = "1A2B3C4D5E6F708192A3B4C5D6E7F8091A2B3C4D5E6F708192A3B4C5D6E7F809";
 * char* encrypted = encrypt_request_params_with_hex_key(data, strlen((char*)data), hex_key);
 * if (encrypted) {
 *     printf("加密结果: %s\n", encrypted);
 *     free(encrypted);
 * }
 * ```
 */

char* encrypt_request_params_with_hex_key(const uint8_t* data, size_t len, const char* hex_key_str);

/**
 * @brief 使用十六进制字符串作为密钥进行解密
 *
 * 便捷函数，将十六进制字符串转换为字节数组后调用decrypt_data_base64()。
 *
 * @param base64_str Base64编码的加密数据字符串
 *                   - 不能为NULL
 *                   - 必须是有效的Base64字符串
 * @param out_len 输出参数，返回解密后的数据长度
 *                - 不能为NULL
 *                - 成功时包含实际数据长度
 *                - 失败时设置为0
 * @param hex_key_str 十六进制格式的密钥字符串
 *                    - 不能为NULL
 *                    - 必须与加密时使用的密钥完全相同
 *                    - 格式：如"1A2B3C4D5E6F..."
 *
 * @return uint8_t* 解密后的原始数据
 *         - 成功：返回数据指针（需要free()释放）
 *         - 失败：返回NULL，out_len设置为0
 *
 * @note
 * - 十六进制密钥必须与加密时完全一致
 * - 内部自动处理十六进制到字节的转换
 *
 * @warning
 * - 密钥格式错误或不匹配会导致解密失败
 * - 必须使用free()释放返回的数据
 *
 * @example
 * ```c
 * const char* encrypted_str = "SGVsbG8sIFdvcmxkIQ==";  // 示例
 * const char* hex_key = "1A2B3C4D5E6F708192A3B4C5D6E7F8091A2B3C4D5E6F708192A3B4C5D6E7F809";
 * size_t decrypted_len = 0;
 * uint8_t* decrypted = decrypt_request_params_with_hex_key(encrypted_str, &decrypted_len, hex_key);
 * if (decrypted) {
 *     printf("解密结果: %.*s\n", (int)decrypted_len, decrypted);
 *     free(decrypted);
 * }
 * ``` 
 */
uint8_t* decrypt_request_params_with_hex_key(const char* base64_str, size_t* out_len, const char* hex_key_str);

/**
 * @brief 将十六进制字符串转换为字节数组
 *
 * 工具函数，将十六进制字符串转换为对应的字节数组。
 *
 * @param hex_str 十六进制字符串
 *                - 不能为NULL
 *                - 格式：如"1A2B3C4D"或"1a2b3c4d"
 *                - 长度必须为偶数
 *                - 只能包含0-9, A-F, a-f字符
 * @param out_len 输出参数，返回转换后的字节数组长度
 *                - 不能为NULL
 *                - 成功时等于hex_str长度的一半
 *                - 失败时设置为0
 *
 * @return uint8_t* 转换后的字节数组
 *         - 成功：返回字节数组指针（需要free()释放）
 *         - 失败：返回NULL，out_len设置为0
 *
 * @note
 * - 不区分大小写（'A'和'a'等价）
 * - 输出长度为输入长度的一半
 * - 空字符串返回NULL
 *
 * @warning
 * - 输入包含非十六进制字符会导致转换失败
 * - 输入长度为奇数会导致转换失败
 * - 必须使用free()释放返回的数组
 *
 * @example
 * ```c
 * const char* hex = "48656C6C6F";  // "Hello"的十六进制
 * size_t byte_len = 0;
 * uint8_t* bytes = hex_string_to_bytes(hex, &byte_len);
 * if (bytes) {
 *     printf("转换结果: %.*s\n", (int)byte_len, bytes);  // 输出: Hello
 *     free(bytes);
 * }
 * ```
 */
uint8_t* hex_string_to_bytes(const char* hex_str, size_t* out_len);

/**
 * @brief 加密单个数据块（8字节）
 *
 * 低级函数，对单个8字节数据块进行就地加密。
 *
 * @param data 要加密的数据块
 *             - 不能为NULL
 *             - 必须至少包含8字节数据
 *             - 加密结果直接覆盖原数据
 * @param user_key 用户提供的密钥
 *                 - 可以为NULL（使用默认密钥）
 *                 - 推荐长度：ParamCrypt_KEY_SIZE字节
 * @param key_len 用户密钥的长度
 *                - 当user_key为NULL时忽略
 *
 * @return void 无返回值（就地加密）
 *
 * @note
 * - 这是底层函数，通常不需要直接调用
 * - 推荐使用encrypt_data_base64()处理任意长度数据
 * - 数据会被直接修改（就地加密）
 * - 不进行填充处理
 *
 * @warning
 * - 数据长度必须恰好为8字节
 * - 原始数据会被覆盖
 * - 不进行参数验证，调用者需确保参数正确
 *
 * @example
 * ```c
 * uint8_t block[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
 * const uint8_t key[] = "MyKey123";
 * encrypt_block(block, key, sizeof(key));
 * // block现在包含加密后的数据
 * ```
 */
void encrypt_block(uint8_t* data, const uint8_t* user_key, size_t key_len);

/**
 * @brief 解密单个数据块（8字节）
 *
 * 低级函数，对单个8字节数据块进行就地解密。
 *
 * @param data 要解密的数据块
 *             - 不能为NULL
 *             - 必须至少包含8字节数据
 *             - 解密结果直接覆盖原数据
 * @param user_key 解密密钥
 *                 - 必须与加密时使用的密钥完全相同
 *                 - 可以为NULL（使用默认密钥）
 * @param key_len 密钥长度
 *                - 必须与加密时的key_len相同
 *                - 当user_key为NULL时忽略
 *
 * @return void 无返回值（就地解密）
 *
 * @note
 * - 这是底层函数，通常不需要直接调用
 * - 推荐使用decrypt_data_base64()处理完整数据
 * - 数据会被直接修改（就地解密）
 * - 密钥必须与加密时完全一致
 *
 * @warning
 * - 数据长度必须恰好为8字节
 * - 原始数据会被覆盖
 * - 密钥不匹配会产生错误的解密结果
 * - 不进行参数验证
 *
 * @example
 * ```c
 * uint8_t encrypted_block[8] = {...};  // 加密后的数据
 * const uint8_t key[] = "MyKey123";
 * decrypt_block(encrypted_block, key, sizeof(key));
 * // encrypted_block现在包含解密后的原始数据
 * ```
 */
void decrypt_block(uint8_t* data, const uint8_t* user_key, size_t key_len);

#ifdef __cplusplus
}
#endif

#endif /* ParamCrypt_h */