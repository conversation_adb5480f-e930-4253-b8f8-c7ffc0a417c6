#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <mach-o/dyld.h>
#include <time.h>
#include <errno.h>
#include <pthread.h>
#include "Base64.h"

// 定义常量
#define KEY_SIZE 64
#define ROUNDS 48
#define BLOCK_SIZE 8
#define SBOX_SIZE 256

// 函数前向声明
void encrypt_block(uint8_t* data, const uint8_t* user_key, size_t key_len);
void decrypt_block(uint8_t* data, const uint8_t* user_key, size_t key_len);
static void derive_subkeys(uint32_t* subkeys, uint32_t* whitebox_mix, size_t count, const uint8_t* master_key);
static uint32_t f_function(uint32_t val, uint32_t subkey, int round, const uint32_t* whitebox_mix);

// 安全内存清零函数 - 防止编译器优化
static void secure_zero(void* ptr, size_t len) {
    if (ptr == NULL || len == 0) return;
    volatile uint8_t* volatile_ptr = (volatile uint8_t*)ptr;
    for (size_t i = 0; i < len; i++) {
        volatile_ptr[i] = 0;
    }
}

// 宏定义
#define ROTL(x,n) (((x) << (n)) | ((x) >> (32 - (n))))
#define ROTR(x,n) (((x) >> (n)) | ((x) << (32 - (n))))

#define SBOX1(x) ((ROTL((x), 5) ^ 0xA5A5A5A5) + 0x1F1F1F1F)
#define SBOX2(x) (((x) * 0x41C64E6D + 0x3039) ^ ROTR((x), 9))
#define SBOX3(x) (ROTL((x), 7) ^ ROTR((x), 11) ^ ((x) * 0x27D4EB2F))
#define SBOX4(x) (ROTL(SBOX1(x), 13) + ROTR(SBOX2(x), 17) - SBOX3(x))

// 线程安全的全局状态管理
static uint8_t CUSTOM_SBOX[SBOX_SIZE];
static volatile int sbox_initialized = 0;
static pthread_mutex_t sbox_mutex = PTHREAD_MUTEX_INITIALIZER;

// 线程安全的S盒初始化函数
static void ensure_sbox_initialized() {
    // 双重检查锁定模式
    if (sbox_initialized) {
        return;
    }

    pthread_mutex_lock(&sbox_mutex);

    // 再次检查，防止其他线程已经初始化
    if (sbox_initialized) {
        pthread_mutex_unlock(&sbox_mutex);
        return;
    }

    // 初始化S盒
    for (int i = 0; i < SBOX_SIZE; i++) {
        CUSTOM_SBOX[i] = i;
    }
    
    // 使用固定种子进行置换，确保加密和解密使用相同的S盒
    uint32_t seed1 = 0x8A7B6C5D;
    uint32_t seed2 = 0xF1E2D3C4;
    uint32_t seed3 = 0x1A2B3C4D;
    
    // 第一轮置换
    for (int i = 0; i < SBOX_SIZE; i++) {
        seed1 = seed1 * 214013 + 2531011;
        int j = seed1 % (i + 1);
        // 交换元素
        uint8_t temp = CUSTOM_SBOX[i];
        CUSTOM_SBOX[i] = CUSTOM_SBOX[j];
        CUSTOM_SBOX[j] = temp;
    }
    
    // 第二轮置换 - 使用不同算法
    for (int i = SBOX_SIZE - 1; i > 0; i--) {
        seed2 = (seed2 ^ (seed2 << 13)) ^ (seed2 >> 7) ^ (seed2 << 17);
        int j = seed2 % (i + 1);
        // 交换元素
        uint8_t temp = CUSTOM_SBOX[i];
        CUSTOM_SBOX[i] = CUSTOM_SBOX[j];
        CUSTOM_SBOX[j] = temp;
    }
    
    // 第三轮置换 - 再次使用不同算法
    for (int i = 0; i < SBOX_SIZE; i += 2) {
        seed3 = (seed3 + 0x7FEB352D) * 0x91E10DA5;
        int j = seed3 % SBOX_SIZE;
        // 交换元素
        uint8_t temp = CUSTOM_SBOX[i];
        CUSTOM_SBOX[i] = CUSTOM_SBOX[j];
        CUSTOM_SBOX[j] = temp;
    }
    
    // 额外混淆 - 应用非线性变换
    for (int i = 0; i < SBOX_SIZE; i++) {
        CUSTOM_SBOX[i] ^= ((i * 0x0F) ^ 0xA5) + (i >> 4);
    }

    // 标记为已初始化
    sbox_initialized = 1;

    pthread_mutex_unlock(&sbox_mutex);
}

// 保持向后兼容的初始化函数
static void init_custom_sbox() {
    ensure_sbox_initialized();
}

// 动态拼接主密钥
void derive_dynamic_master_key(uint8_t* master_key, const uint8_t* user_key, size_t key_len) {
    // 初始化主密钥
    memset(master_key, 0, KEY_SIZE);
    
    // 如果用户提供了密钥，使用用户密钥
    if (user_key != NULL && key_len > 0) {
        // 将用户密钥复制到主密钥，如果用户密钥较短则循环填充
        for (int i = 0; i < KEY_SIZE; i++) {
            master_key[i] = user_key[i % key_len];
        }
    } else {
        // 使用默认密钥（为了向后兼容）- 与Python版本保持一致
        static const uint8_t default_key[] = {
            0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0,
            0xAB, 0xCD, 0xEF, 0x12, 0x45, 0x67, 0x89, 0xAB
        };
        for (int i = 0; i < KEY_SIZE; i++) {
            master_key[i] = default_key[i % sizeof(default_key)];
        }
    }
    
    // 使用固定盐值进行混淆
    static const uint32_t salt[4] = {0x8A7B6C5D, 0x12345678, 0xABCDEF01, 0x87654321};
    
    // 对密钥进行额外混淆，增加安全性
    for (int i = 0; i < KEY_SIZE; i++) {
        master_key[i] ^= (salt[i % 4] >> ((i * 3) % 32)) & 0xFF;
        master_key[i] = CUSTOM_SBOX[master_key[i]]; // 应用S盒变换
    }
}

// 派生子密钥 - 增强版（线程安全）
static void derive_subkeys(uint32_t* subkeys, uint32_t* whitebox_mix, size_t count, const uint8_t* master_key) {
    // 使用固定噪声源，确保加密和解密使用相同的子密钥
    uint32_t noise1 = 0xDEADBEEF;
    uint32_t noise2 = 0x1337C0DE;
    uint32_t noise3 = 0xCAFEBABE;
    
    // 预处理 - 创建临时密钥缓冲区
    uint8_t temp_key[KEY_SIZE];
    memcpy(temp_key, master_key, KEY_SIZE);
    
    // 应用密钥扩展
    for (int i = 0; i < 3; i++) {
        for (size_t j = 0; j < KEY_SIZE; j++) {
            temp_key[j] = CUSTOM_SBOX[(temp_key[j] ^ temp_key[(j + i + 1) % KEY_SIZE]) & 0xFF];
        }
    }
    
    for (size_t i = 0; i < count; ++i) {
        // 使用更复杂的偏移计算
        size_t offset = ((i * 7) ^ (i >> 2)) % (KEY_SIZE - 3);
        
        // 使用更复杂的子密钥派生
        subkeys[i] = (
            ((uint32_t)temp_key[offset] << 24) |
            ((uint32_t)temp_key[(offset + 1) % KEY_SIZE] << 16) |
            ((uint32_t)temp_key[(offset + 2) % KEY_SIZE] << 8) |
            ((uint32_t)temp_key[(offset + 3) % KEY_SIZE])
        );
        
        // 应用多重噪声
        subkeys[i] ^= ROTL(noise1, i % 23);
        subkeys[i] ^= ROTR(noise2, (i + 7) % 19);
        subkeys[i] ^= (noise3 + i * 0x9E3779B9); // 黄金比例常数
        
        // 增强子密钥混淆 - 使用更复杂的变换
        subkeys[i] = SBOX1(subkeys[i]);
        subkeys[i] = SBOX2(subkeys[i] ^ (subkeys[i] >> 11));
        subkeys[i] = SBOX3(subkeys[i]);
        subkeys[i] = ROTL(subkeys[i], (i % 11) + 5) ^ ROTR(subkeys[i], (i % 7) + 3);
        
        // 预计算白盒混淆表 - 使用更复杂的方式
        uint32_t mix_val = SBOX1(subkeys[i]) ^ (i * 0x9E3779B9);
        mix_val = SBOX3(mix_val ^ (mix_val >> 13));
        mix_val = ROTL(mix_val, i % 17) ^ ROTR(mix_val, i % 13);
        whitebox_mix[i] = mix_val ^ (noise1 + i * noise2);
    }
}

// 控制流混淆 - 保留以增加逆向难度
__attribute__((noinline))
static void fake_control_flow(uint32_t val) {
    // 使用volatile变量防止编译器优化
    volatile uint32_t dummy = val;
    if ((dummy & 0x5A) == 0x5A) {
        dummy ^= 0x12345678;
    } else {
        dummy ^= 0x87654321;
    }
}

// 轮函数 - 大幅增强版（线程安全）
static uint32_t f_function(uint32_t val, uint32_t subkey, int round, const uint32_t* whitebox_mix) {
    uint32_t result = val;
    
    // 第一阶段变换 - 位移和混淆
    result = ROTL(result, (round % 7) + 1);
    result ^= whitebox_mix[round];
    
    // 第二阶段变换 - 字节级S盒
    uint8_t* bytes = (uint8_t*)&result;
    for (int i = 0; i < 4; i++) {
        // 应用多重S盒变换
        uint8_t b = bytes[i];
        b = CUSTOM_SBOX[b];
        b = CUSTOM_SBOX[(b + round) & 0xFF];
        b = CUSTOM_SBOX[(b ^ (round * 13)) & 0xFF];
        bytes[i] = b;
    }
    
    // 第三阶段变换 - 非线性函数
    result = SBOX1(result);
    result = SBOX2(result ^ subkey);
    result = SBOX3(result);
    
    // 第四阶段变换 - 扩散
    result = ROTL(result, 5) ^ ROTR(result, 7) ^ subkey;
    
    // 第五阶段变换 - 最终混淆
    result = SBOX4(result ^ whitebox_mix[(round + 7) % ROUNDS]);
    
    return result;
}

// 加密单个数据块 - 修改版
__attribute__((noinline))
void encrypt_block(uint8_t* data, const uint8_t* user_key, size_t key_len) {
    uint32_t left, right, temp;
    memcpy(&left, data, 4);
    memcpy(&right, data + 4, 4);

    // 派生密钥
    uint32_t subkeys[ROUNDS];
    uint32_t whitebox_mix[ROUNDS];
    uint8_t master_key[KEY_SIZE];
    derive_dynamic_master_key(master_key, user_key, key_len);
    derive_subkeys(subkeys, whitebox_mix, ROUNDS, master_key);

    // 增强型Feistel网络加密
    for (int i = 0; i < ROUNDS; ++i) {
        fake_control_flow(subkeys[i]);

        temp = f_function(right, subkeys[i], i, whitebox_mix);
        temp ^= left;
        left = right;
        right = temp;
    }

    memcpy(data, &right, 4);
    memcpy(data + 4, &left, 4);

    // 安全清零敏感数据
    secure_zero(master_key, KEY_SIZE);
    secure_zero(subkeys, sizeof(subkeys));
    secure_zero(whitebox_mix, sizeof(whitebox_mix));
}

// 解密单个数据块 - 修改版
__attribute__((noinline))
void decrypt_block(uint8_t* data, const uint8_t* user_key, size_t key_len) {
    uint32_t left, right, temp;
    memcpy(&right, data, 4);
    memcpy(&left, data + 4, 4);

    // 派生密钥
    uint32_t subkeys[ROUNDS];
    uint32_t whitebox_mix[ROUNDS];
    uint8_t master_key[KEY_SIZE];
    derive_dynamic_master_key(master_key, user_key, key_len);
    derive_subkeys(subkeys, whitebox_mix, ROUNDS, master_key);

    // 增强型Feistel网络解密
    for (int i = ROUNDS - 1; i >= 0; --i) {
        fake_control_flow(subkeys[i]);

        temp = f_function(left, subkeys[i], i, whitebox_mix);
        temp ^= right;
        right = left;
        left = temp;
    }

    memcpy(data, &left, 4);
    memcpy(data + 4, &right, 4);

    // 安全清零敏感数据
    secure_zero(master_key, KEY_SIZE);
    secure_zero(subkeys, sizeof(subkeys));
    secure_zero(whitebox_mix, sizeof(whitebox_mix));
}

// 加密任意长度数据并返回Base64编码的结果
char* encrypt_data_base64(const uint8_t* data, size_t len, const uint8_t* user_key, size_t key_len) {
    // 输入验证
    if (!data || len == 0) {
        return NULL;
    }

    // 检查长度是否合理（防止整数溢出）
    if (len > SIZE_MAX - BLOCK_SIZE) {
        return NULL;
    }

    // 确保S盒已初始化
    init_custom_sbox();

    // 计算填充后的长度
    size_t padded_len = (len + BLOCK_SIZE - 1) & ~(BLOCK_SIZE - 1);

    // 创建临时缓冲区
    uint8_t* temp_data = (uint8_t*)malloc(padded_len);
    if (!temp_data) {
        return NULL;
    }

    // 复制原始数据
    memcpy(temp_data, data, len);

    // 如果需要填充，使用PKCS#7填充
    if (padded_len > len) {
        uint8_t padding = (uint8_t)(padded_len - len);
        // 验证填充值是否合理
        if (padding > BLOCK_SIZE) {
            secure_zero(temp_data, padded_len);
            free(temp_data);
            return NULL;
        }
        for (size_t i = len; i < padded_len; i++) {
            temp_data[i] = padding;
        }
    }

    // 逐块加密
    for (size_t i = 0; i < padded_len; i += BLOCK_SIZE) {
        encrypt_block(temp_data + i, user_key, key_len);
    }

    // 将加密后的数据转换为Base64编码
    char* base64_result = base64_encode(temp_data, padded_len);

    // 安全清零并释放临时缓冲区
    secure_zero(temp_data, padded_len);
    free(temp_data);

    return base64_result;
}

// 从Base64编码的字符串解密数据
uint8_t* decrypt_data_base64(const char* base64_str, size_t* out_len, const uint8_t* user_key, size_t key_len) {
    // 输入验证
    if (!base64_str || !out_len) {
        if (out_len) *out_len = 0;
        return NULL;
    }

    // 确保S盒已初始化
    init_custom_sbox();

    // 解码Base64字符串
    int decoded_len = 0;
    uint8_t* decoded_data = base64_decode(base64_str, &decoded_len);
    if (!decoded_data || decoded_len <= 0) {
        *out_len = 0;
        return NULL;
    }

    // 确保长度是BLOCK_SIZE的倍数且不为0
    if (decoded_len % BLOCK_SIZE != 0 || decoded_len == 0) {
        secure_zero(decoded_data, decoded_len);
        free(decoded_data);
        *out_len = 0;
        return NULL;
    }

    // 逐块解密
    for (size_t i = 0; i < (size_t)decoded_len; i += BLOCK_SIZE) {
        decrypt_block(decoded_data + i, user_key, key_len);
    }

    // 处理PKCS#7填充
    uint8_t padding = decoded_data[decoded_len - 1];
    if (padding > 0 && padding <= BLOCK_SIZE && padding <= decoded_len) {
        // 验证填充
        int valid = 1;
        for (size_t i = decoded_len - padding; i < (size_t)decoded_len; i++) {
            if (decoded_data[i] != padding) {
                valid = 0;
                break;
            }
        }

        // 如果填充有效，调整输出长度
        if (valid) {
            // 安全清零填充部分
            secure_zero(decoded_data + decoded_len - padding, padding);
            decoded_len -= padding;
        }
    }

    *out_len = (size_t)decoded_len;
    return decoded_data;
}

// 将十六进制字符串转换为字节数组
uint8_t* hex_string_to_bytes(const char* hex_str, size_t* out_len) {
    if (!hex_str || !out_len) {
        if (out_len) *out_len = 0;
        return NULL;
    }

    // 计算十六进制字符串转换后的字节长度
    size_t str_len = strlen(hex_str);

    // 检查字符串长度是否为偶数
    if (str_len % 2 != 0) {
        *out_len = 0;
        return NULL;
    }

    size_t byte_len = str_len / 2;

    // 检查长度是否合理（防止过大的分配）
    if (byte_len > SIZE_MAX / 2 || byte_len == 0) {
        *out_len = 0;
        return NULL;
    }

    uint8_t* arr = (uint8_t*)malloc(byte_len);
    if (!arr) {
        *out_len = 0;
        return NULL;
    }

    // 将十六进制字符串转换为字节数组
    for (size_t i = 0; i < byte_len; i++) {
        // 检查字符是否为有效的十六进制字符
        char c1 = hex_str[i*2];
        char c2 = hex_str[i*2+1];

        if (!((c1 >= '0' && c1 <= '9') || (c1 >= 'A' && c1 <= 'F') || (c1 >= 'a' && c1 <= 'f')) ||
            !((c2 >= '0' && c2 <= '9') || (c2 >= 'A' && c2 <= 'F') || (c2 >= 'a' && c2 <= 'f'))) {
            secure_zero(arr, byte_len);
            free(arr);
            *out_len = 0;
            return NULL;
        }

        char byte_str[3] = {c1, c2, '\0'};
        char* endptr;
        long val = strtol(byte_str, &endptr, 16);

        // 检查转换是否成功
        if (*endptr != '\0' || val < 0 || val > 255) {
            secure_zero(arr, byte_len);
            free(arr);
            *out_len = 0;
            return NULL;
        }

        arr[i] = (uint8_t)val;
    }

    *out_len = byte_len;
    return arr;
}

// 使用十六进制字符串作为密钥进行加密
char* encrypt_request_params_with_hex_key(const uint8_t* data, size_t len, const char* hex_key_str) {
    if (!data || !hex_key_str || len == 0) {
        return NULL;
    }

    // 将十六进制字符串转换为字节数组
    size_t key_len = 0;
    uint8_t* key_bytes = hex_string_to_bytes(hex_key_str, &key_len);
    if (!key_bytes || key_len == 0) {
        return NULL;
    }

    // 使用转换后的密钥进行加密
    char* result = encrypt_data_base64(data, len, key_bytes, key_len);

    // 安全清零并释放临时密钥
    secure_zero(key_bytes, key_len);
    free(key_bytes);

    return result;
}

// 使用十六进制字符串作为密钥进行解密
uint8_t* decrypt_request_params_with_hex_key(const char* base64_str, size_t* out_len, const char* hex_key_str) {
    if (!base64_str || !hex_key_str || !out_len) {
        if (out_len) *out_len = 0;
        return NULL;
    }

    // 将十六进制字符串转换为字节数组
    size_t key_len = 0;
    uint8_t* key_bytes = hex_string_to_bytes(hex_key_str, &key_len);
    if (!key_bytes || key_len == 0) {
        *out_len = 0;
        return NULL;
    }

    // 使用转换后的密钥进行解密
    uint8_t* result = decrypt_data_base64(base64_str, out_len, key_bytes, key_len);

    // 安全清零并释放临时密钥
    secure_zero(key_bytes, key_len);
    free(key_bytes);

    return result;
}
