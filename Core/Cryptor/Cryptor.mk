CRYPTOR_FILES = \
../../Core/Cryptor/Base64/Base64.c \
../../Core/Cryptor/Sha256/Sha256.c \
../../Core/Cryptor/MD5/MD5.c \
../../Core/Cryptor/String/StringCryptor_v2.c \
../../Core/Cryptor/Signature/Signature.c \
../../Core/Cryptor/KeyCryptor/KeyCryptor.c \
../../Core/Cryptor/ParamsCryptor/ParamsCryptor.c


CRYPTOR_CFLAGS = \
-I../../Core/Cryptor/Base64/ \
-I../../Core/Cryptor/Sha256/ \
-I../../Core/Cryptor/MD5/ \
-I../../Core/Cryptor/String/ \
-I../../Core/Cryptor/Signature/ \
-I../../Core/Cryptor/KeyCryptor/ \
-I../../Core/Cryptor/ParamsCryptor/
