// JDSign.m
#import "JDSign.h"
#import <objc/runtime.h>
#import <objc/message.h>
#import <substrate.h>
#import "Log.h"

// 定义自定义错误域
// static NSString *const JDGuardSignErrorDomain = @"com.g4.jmSign.JDGuardSignErrorDomain";
static NSString *const JDSignErrorDomain = @"com.g4.jdsign.JDSignErrorDomain";

static Class _JDSignServiceClass = nil;
static IMP _JDSignServiceGetSignIMP = nil; // 缓存 +[JDSignService getSignWithDic:keys:] 的 IMP
static dispatch_once_t onceTokenJDSignService = 0;

typedef id (*_JDSignServiceGetSignCallType)(Class, SEL, NSDictionary *, NSArray *);

@implementation JDSign


static id callJDGuardSignWithUrlString(NSString *urlString, NSString *contentType, NSString *method, NSData *bodyData) {
    Class JDGuard = objc_getClass("JDGuardModule");
    if (!JDGuard) {
        NSLOG(@"[JDSign] 未找到 JDGuardModule 类");
        return nil;
    }

    SEL sel = sel_registerName("signWithUrlString:contentType:method:bodyData:");
    if (![JDGuard respondsToSelector:sel]) {
        NSLOG(@"[JDSign] JDGuardModule 不响应 signWithUrlString:... 方法");
        return nil;
    }

    // 使用 objc_msgSend 直接调用类方法
    typedef id (*FuncType)(id, SEL, NSString *, NSString *, NSString *, NSData *);
    FuncType func = (FuncType)&objc_msgSend;

    id result = func(JDGuard, sel, urlString, contentType, method, bodyData);
    NSLOG(@"[JDSign] 返回结果: %@", result);

    return result;
}

static id CallJDSignServiceGetSign(NSDictionary *dic, NSArray *keys) {
    // 1. 输入参数有效性检查
    if (!dic || !keys) {
        return [NSError errorWithDomain:JDSignErrorDomain code:201 userInfo:@{NSLocalizedDescriptionKey: @"CallJDSignServiceGetSign: 输入参数缺失或无效"}];
    }

    // 2. 核心初始化逻辑 (只执行一次)
    dispatch_once(&onceTokenJDSignService, ^{
        // 获取类对象
        _JDSignServiceClass = objc_getClass("JDSignService");
        if (!_JDSignServiceClass) {
            return; // 退出初始化块
        }
        NSLOG(@"[JDSignServiceHook] 成功找到 JDSignService 类。");

        // 获取方法签名和 IMP
        SEL sel = NSSelectorFromString(@"getSignWithDic:keys:");
        Method classMethod = class_getClassMethod(_JDSignServiceClass, sel);
        if (!classMethod) {
            NSLOG(@"[JDSignServiceHook] 错误: JDSignService 类方法 getSignWithDic:keys: 未找到。");
            _JDSignServiceClass = nil; // 标记初始化失败
            return;
        }

        _JDSignServiceGetSignIMP = method_getImplementation(classMethod);
        if (!_JDSignServiceGetSignIMP) {
            NSLOG(@"[JDSignServiceHook] 错误: 获取 JDSignService 签名方法 IMP 失败。");
            _JDSignServiceClass = nil; // 标记初始化失败
            return;
        }
        NSLOG(@"[JDSignServiceHook] 签名方法 IMP 获取成功。");
        NSLOG(@"[JDSignServiceHook] JDSignService: 核心初始化完成。");
    }); // dispatch_once 结束

    // 3. 初始化后检查缓存状态
    if (!_JDSignServiceClass) {
        return [NSError errorWithDomain:JDSignErrorDomain code:201 userInfo:@{NSLocalizedDescriptionKey: @"JDSignService 类未找到或初始化失败"}];
    }
    if (!_JDSignServiceGetSignIMP) {
        return [NSError errorWithDomain:JDSignErrorDomain code:201 userInfo:@{NSLocalizedDescriptionKey: @"JDSignService 签名方法 IMP 未成功初始化"}];
    }

    // 4. 执行签名调用 (使用缓存的 IMP)
    _JDSignServiceGetSignCallType getSignMethod = (_JDSignServiceGetSignCallType)_JDSignServiceGetSignIMP;
    SEL sel = NSSelectorFromString(@"getSignWithDic:keys:"); // SEL 仍然需要作为 _cmd 参数传递

    id result = getSignMethod(_JDSignServiceClass, sel, dic, keys); // 调用类方法

    // 5. 签名结果处理
    if (result) {
        return result;
    } else {
        NSLOG(@"⚠️ JDSignService 签名调用失败或返回 nil.");
        return [NSError errorWithDomain:JDSignErrorDomain code:201 userInfo:@{NSLocalizedDescriptionKey: @"JDSignService 签名方法返回了 nil"}];
    }
}

+ (NSDictionary *)getJdgsSign:(NSDictionary *)params {
    NSString *urlString = params[@"url"];
    NSString *httpMethod = params[@"method"];
    NSString *contentType = params[@"contentType"];
    id bodyRawValue = params[@"body"];

    NSData *bodyData;
    if ([bodyRawValue isKindOfClass:[NSData class]]) {
        bodyData = (NSData *)bodyRawValue;
    } else if ([bodyRawValue isKindOfClass:[NSString class]]) {
        bodyData = [(NSString *)bodyRawValue dataUsingEncoding:NSUTF8StringEncoding];
    } else {
        NSLOG(@"[JDSign] body 参数必须是 NSString 或 NSData 类型");
        return @{@"result": @"Invalid body type", @"code": @(400)};
    }

    id signResult = callJDGuardSignWithUrlString(urlString, contentType, httpMethod, bodyData);

    if (signResult == nil) {
        return @{@"result": @"Sign failed", @"code": @(500)};
    }

    // 如果返回值是字符串或字典，可以进一步解析
    if ([signResult isKindOfClass:[NSString class]] || [signResult isKindOfClass:[NSDictionary class]]) {
        return @{@"result": signResult, @"code": @(200)};
    }

    return @{@"result": @"Unknown sign result format", @"code": @(200)};
}

+ (NSDictionary *)getHttpSign:(NSDictionary *)params {
    NSDictionary *dict = @{
        @"functionId": [params objectForKey:@"functionId"],
        @"body": [params objectForKey:@"body"],
        @"openudid": [params objectForKey:@"openudid"],
        @"client": [params objectForKey:@"client"],
        @"clientVersion": [params objectForKey:@"clientVersion"]
    };

    // NSArray *keys = @[ @"body", @"client", @"clientVersion", @"functionId", @"openudid" ];
    NSArray *keys = @[ @"functionId", @"body", @"openudid", @"client", @"clientVersion" ];

    // 调用 CallJDSignServiceGetSign
    id signResult = CallJDSignServiceGetSign(dict, keys);

    if ([signResult isKindOfClass:[NSError class]]) {
        NSError *error = (NSError *)signResult;
        return @{
            @"error": error.localizedDescription ?: @"未知错误",
            @"code": @(error.code)
        };
    } else if (signResult) {
        // 成功时返回签名结果
        return @{@"result": signResult, @"code": @(200)};
    } else {
        // Fallback for unexpected nil result
        return @{@"error": @"HTTP 签名方法返回了 nil (未指定错误)", @"code": @(500)};
    }
}

@end