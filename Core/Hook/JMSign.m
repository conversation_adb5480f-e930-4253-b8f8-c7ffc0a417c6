// JMSign.m
#import "JMSign.h"
#import <objc/runtime.h>
#import <objc/message.h>
#import <substrate.h>
#import "Log.h"

// 定义自定义错误域
static NSString *const JMSignErrorDomain = @"com.g4.jmsign.JMSignErrorDomain";


static Class _JDGuardClass = nil;
static id _JDGuardInstance = nil;
static IMP _JDGuardSignMethodIMP = nil;
static dispatch_once_t onceTokenJDGuard = 0;
typedef id (*_JDGuardSignMethodCallType)(id, SEL, NSString *, NSString *, NSString *, NSData *, BOOL);

static Class _JMHttpSignClass = nil;
static IMP _JMHttpSignSignMethodIMP = nil;
static dispatch_once_t onceTokenJMHttpSign = 0;
typedef id (*_JMHttpSignMethodCallType)(Class, SEL, NSDictionary *, NSString *);



@implementation JMSign


static id CallJDGuardSign(NSString *urlString, NSString *contentType, NSString *method, NSData *bodyData, BOOL uppercaseBD) {
    // --- 1. 输入参数有效性检查 ---
    if (!urlString || !contentType || !method || !bodyData) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"CallJDGuardSign: 输入参数缺失或无效"}];
    }

    // --- 2. 核心初始化逻辑 (只执行一次) ---
    // 使用 dispatch_once 确保 JDGuardClass, JDGuardInstance, _JDGuardSignMethodIMP 只被初始化一次
    dispatch_once(&onceTokenJDGuard, ^{
        NSLOG(@"[JDGuardHook] JDGuardSign: 核心初始化开始...");

        // 获取类
        // 再次确认类名是 "JDGS_minicomputer" 还是 "JDGS_reprehend"
        _JDGuardClass = objc_getClass("JDGS_minicomputer"); // <-- 请再次核对这个类名
        if (!_JDGuardClass) {
            NSLOG(@"[JDGuardHook] 错误: JDGS_minicomputer 类未找到，无法初始化签名服务。");
            return; // 退出初始化块，实例和 IMP 将保持为 nil
        }

        // 获取单例实例
        SEL sharedInstanceSelector = NSSelectorFromString(@"sharedInstance");
        if ([_JDGuardClass respondsToSelector:sharedInstanceSelector]) {
            typedef id (*GetInstanceMethodType)(Class, SEL);
            GetInstanceMethodType getInstanceMethod = (GetInstanceMethodType)objc_msgSend;
            _JDGuardInstance = getInstanceMethod(_JDGuardClass, sharedInstanceSelector);
            if (!_JDGuardInstance) {
                NSLOG(@"[JDGuardHook] 警告: JDGS_minicomputer 类的 sharedInstance 方法返回 nil，无法初始化签名服务。");
                // 如果单例返回 nil，后续操作也无法进行，所以退出
                _JDGuardClass = nil; // 置空类，标记初始化失败
                return;
            }
            NSLOG(@"[JDGuardHook] 成功获取 JDGS_minicomputer 实例: %@", _JDGuardInstance);
        } else {
            NSLOG(@"[JDGuardHook] 错误: JDGS_minicomputer 类不响应 sharedInstance 选择器，无法获取实例。");
            _JDGuardClass = nil; // 置空类，标记初始化失败
            return;
        }

        // 获取签名方法的 IMP
        SEL signSelector = @selector(signWithUrlString:contentType:method:bodyData:uppercaseBD:);
        if ([_JDGuardInstance respondsToSelector:signSelector]) {
            _JDGuardSignMethodIMP = [_JDGuardInstance methodForSelector:signSelector];
            if (!_JDGuardSignMethodIMP) {
                NSLOG(@"[JDGuardHook] 错误: 获取签名方法 IMP 失败，签名服务将不可用。");
                 _JDGuardInstance = nil; // 如果 IMP 无法获取，实例也视为无效
                 _JDGuardClass = nil;
                 return;
            }
            NSLOG(@"[JDGuardHook] 签名方法 IMP 获取成功。");
        } else {
            NSLOG(@"[JDGuardHook] 错误: JDGS_minicomputer 实例不响应签名方法 %@，签名服务将不可用。", NSStringFromSelector(signSelector));
            _JDGuardInstance = nil; // 如果方法不存在，实例也视为无效
            _JDGuardClass = nil;
            return;
        }
        NSLOG(@"[JDGuardHook] JDGuardSign: 核心初始化完成。");
    }); // dispatch_once 结束

    // --- 3. 初始化后检查缓存状态 ---
    // 如果在 dispatch_once 中任何一个步骤失败，对应的静态变量会保持为 nil
    if (!_JDGuardClass) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"JDGS_minicomputer 类未找到或初始化失败"}];
    }
    if (!_JDGuardInstance) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"JDGS_minicomputer 类的实例获取失败或为 nil"}];
    }
    if (!_JDGuardSignMethodIMP) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"签名方法 IMP 未成功初始化"}];
    }

    // --- 4. 执行签名调用 ---
    // 使用缓存的 IMP 直接调用方法
    _JDGuardSignMethodCallType signMethod = (_JDGuardSignMethodCallType)_JDGuardSignMethodIMP;
    SEL signSelector = @selector(signWithUrlString:contentType:method:bodyData:uppercaseBD:); // 传递 SEL 仍然是必要的，因为 IMP 的第一个参数是 self，第二个是 _cmd

    id result = signMethod(_JDGuardInstance, signSelector, urlString, contentType, method, bodyData, uppercaseBD);

    // --- 5. 签名结果处理 ---
    if (!result) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"签名方法返回了 nil (非预期)"}];
    }

    return result; // 成功时返回签名结果
}

static id CallJMHttpSign(NSString *api, NSString *appId, NSString *timestamp, NSString *v, NSString *body) {
    // 1. 构造参数字典
    NSDictionary *params = @{
        @"api": api,
        @"appId": appId,
        @"timestamp": timestamp,
        @"v": v,
        @"body": body,
    };
    NSString *secret = @"rabobypyi1omsax4bjme";

    if (!params || !secret) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"CallJMHttpSign: 输入参数缺失或无效"}];
    }

    // 2. 核心初始化逻辑 (只执行一次)
    dispatch_once(&onceTokenJMHttpSign, ^{
        NSLOG(@"[JMHttpSignHook] JMHttpSign: 核心初始化开始...");

        // 获取类对象
        _JMHttpSignClass = objc_getClass("JMHttpSign");
        if (!_JMHttpSignClass) {
            NSLOG(@"[JMHttpSignHook] 错误: JMHttpSign 类未找到，无法初始化。");
            return; // 退出初始化块
        }
        NSLOG(@"[JMHttpSignHook] 成功找到 JMHttpSign 类。");

        // 获取方法签名和 IMP
        SEL sel = sel_registerName("signString:secret:");
        Method classMethod = class_getClassMethod(_JMHttpSignClass, sel);
        if (!classMethod) {
            NSLOG(@"[JMHttpSignHook] 错误: JMHttpSign 类方法 signString:secret: 未找到。");
            _JMHttpSignClass = nil; // 标记初始化失败
            return;
        }

        _JMHttpSignSignMethodIMP = method_getImplementation(classMethod);
        if (!_JMHttpSignSignMethodIMP) {
            NSLOG(@"[JMHttpSignHook] 错误: 获取 JMHttpSign 签名方法 IMP 失败。");
            _JMHttpSignClass = nil; // 标记初始化失败
            return;
        }
        NSLOG(@"[JMHttpSignHook] 签名方法 IMP 获取成功。");
        NSLOG(@"[JMHttpSignHook] JMHttpSign: 核心初始化完成。");
    }); // dispatch_once 结束

    // 3. 初始化后检查缓存状态
    if (!_JMHttpSignClass) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"JMHttpSign 类未找到或初始化失败"}];
    }
    if (!_JMHttpSignSignMethodIMP) {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: @"JMHttpSign 签名方法 IMP 未成功初始化"}];
    }

    // 4. 执行签名调用 (使用缓存的 IMP)
    _JMHttpSignMethodCallType signMethod = (_JMHttpSignMethodCallType)_JMHttpSignSignMethodIMP;
    SEL sel = sel_registerName("signString:secret:"); // SEL 仍然需要作为 _cmd 参数传递

    id result = signMethod(_JMHttpSignClass, sel, params, secret); // 调用类方法

    // 5. 签名结果处理
    if (result && [result isKindOfClass:[NSString class]]) {
        return result;
    } else {
        return [NSError errorWithDomain:JMSignErrorDomain code:200 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"签名调用失败或返回值无效: %@", result]}];
    }
}

+ (NSDictionary *)getJdgsSign:(NSDictionary *)args {
    NSString *urlString = [args objectForKey:@"url"];
    NSString *httpMethod = [args objectForKey:@"method"];
    NSString *contentType = [args objectForKey:@"contentType"];
    id bodyRawValue = [args objectForKey:@"body"]; 
    NSData *bodyData = nil;

    // 检查 IPC 传入的 body 类型并进行转换
    if ([bodyRawValue isKindOfClass:[NSString class]]) {
        bodyData = [(NSString *)bodyRawValue dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([bodyRawValue isKindOfClass:[NSData class]]) {
        bodyData = (NSData *)bodyRawValue;
    } else if ([bodyRawValue isKindOfClass:[NSDictionary class]]) {
        // 如果 body 是字典，尝试序列化为 JSON Data
        NSError *error = nil;
        bodyData = [NSJSONSerialization dataWithJSONObject:bodyRawValue options:0 error:&error];
        if (error) {
            NSLOG(@"[JDGuardHook] Error converting bodyDict to NSData: %@", error);
            return @{@"error": @"Body数据JSON转换失败", @"code": @(500)};
        }
    } else {
        NSLOG(@"[JDGuardHook] getJdgsSign: IPC 传入的 body 参数类型未知: %@", [bodyRawValue class]);
        return @{@"error": @"IPC传入body参数类型未知", @"code": @(500)};
    }
    
    if (!bodyData) { // 再次检查 bodyData 是否成功转换
         NSLOG(@"[JDGuardHook] getJdgsSign: Body数据转换后仍为空");
         return @{@"error": @"Body数据转换后为空", @"code": @(500)};
    }

    BOOL uppercaseBD = YES; 

    // 调用 CallJDGuardSign
    id signResult = CallJDGuardSign(urlString , contentType, httpMethod, bodyData, uppercaseBD);

    // 判断 CallJDGuardSign 的返回值是签名结果还是错误对象
    if ([signResult isKindOfClass:[NSError class]]) {
        NSError *error = (NSError *)signResult;
        return @{
            @"error": error.localizedDescription ?: @"未知错误",
            @"code": @(error.code)
        };
    } else if (signResult) {
        // 成功时返回签名结果
        return @{@"result": signResult, @"code": @(200)};
    } else {
        // Fallback for unexpected nil result without NSError
        return @{@"error": @"签名方法返回了 nil (未指定错误)", @"code": @(500)};
    }
}

+ (NSDictionary *)getHttpSign:(NSDictionary *)args {
    NSString *api = [args objectForKey:@"api"];
    NSString *appId = [args objectForKey:@"appId"];
    NSString *timestamp = [args objectForKey:@"timestamp"];
    NSString *v = [args objectForKey:@"v"];
    NSString *body = [args objectForKey:@"body"]; // CallJMHttpSign 期望 NSString for body

    // 调用 CallJMHttpSign
    id signResult = CallJMHttpSign(api, appId, timestamp, v, body);

    // 判断 CallJMHttpSign 的返回值
    if ([signResult isKindOfClass:[NSError class]]) {
        NSError *error = (NSError *)signResult;
        return @{
            @"error": error.localizedDescription ?: @"未知错误",
            @"code": @(error.code)
        };
    } else if (signResult) {
        // 成功时返回签名结果
        return @{@"sign": signResult, @"code": @(200)};
    } else {
        // Fallback for unexpected nil result
        return @{@"error": @"HTTP 签名方法返回了 nil (未指定错误)", @"code": @(500)};
    }
}

// + (NSDictionary *)getSign:(NSDictionary *)args {
//     // 第一步：调用 getHttpSign 获取 HTTP 签名
//     NSDictionary *httpSignResult = [self getHttpSign:args];
    
//     // 检查 HTTP 签名是否成功
//     NSNumber *httpCode = [httpSignResult objectForKey:@"code"];
//     if (!httpCode || [httpCode intValue] != 200) {
//         // HTTP 签名失败，直接返回错误
//         return @{
//             @"error": [httpSignResult objectForKey:@"error"] ?: @"HTTP签名失败",
//             @"code": httpCode ?: @(500),
//             @"step": @"getHttpSign"
//         };
//     }
    
//     // 获取 HTTP 签名结果
//     NSString *httpSign = [httpSignResult objectForKey:@"sign"];
//     if (!httpSign) {
//         return @{
//             @"error": @"HTTP签名结果为空",
//             @"code": @(500),
//             @"step": @"getHttpSign"
//         };
//     }
    
//     // 第二步：准备 JDGS 签名的参数
//     // 将 HTTP 签名结果添加到原始参数中，或者根据需要修改参数
//     NSMutableDictionary *jdgsArgs = [args mutableCopy];
    
//     // 如果需要将 HTTP 签名作为 body 或其他参数传递给 JDGS，可以在这里修改
//     // 例如：将 HTTP 签名添加到 body 中
//     NSString *originalBody = [jdgsArgs objectForKey:@"body"];
//     if (originalBody) {
//         // 如果原始 body 存在，可以将 HTTP 签名附加或替换
//         // 这里假设需要将 HTTP 签名作为新的 body
//         [jdgsArgs setObject:httpSign forKey:@"body"];
//     } else {
//         // 如果没有原始 body，直接使用 HTTP 签名
//         [jdgsArgs setObject:httpSign forKey:@"body"];
//     }
    
//     // 调用 getJdgsSign 获取 JDGS 签名
//     NSDictionary *jdgsSignResult = [self getJdgsSign:jdgsArgs];
    
//     // 检查 JDGS 签名是否成功
//     NSNumber *jdgsCode = [jdgsSignResult objectForKey:@"code"];
//     if (!jdgsCode || [jdgsCode intValue] != 200) {
//         // JDGS 签名失败，返回错误信息，但包含已成功的 HTTP 签名
//         return @{
//             @"error": [jdgsSignResult objectForKey:@"error"] ?: @"JDGS签名失败",
//             @"code": jdgsCode ?: @(500),
//             @"step": @"getJdgsSign",
//             @"httpSign": httpSign  // 保留已成功的 HTTP 签名
//         };
//     }
    
//     // 获取 JDGS 签名结果
//     id jdgsSign = [jdgsSignResult objectForKey:@"result"];
//     if (!jdgsSign) {
//         return @{
//             @"error": @"JDGS签名结果为空",
//             @"code": @(500),
//             @"step": @"getJdgsSign",
//             @"httpSign": httpSign  // 保留已成功的 HTTP 签名
//         };
//     }
    
//     // 第三步：返回合并的结果
//     return @{
//         @"sign": httpSign,      // HTTP 签名结果
//         @"jdgs": jdgsSign,      // JDGS 签名结果
//         @"code": @(200),        // 成功状态码
//         @"message": @"签名流程完成"
//     };
// }

@end