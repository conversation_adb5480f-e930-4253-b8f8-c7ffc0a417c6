// JMSign.h
#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JMSign : NSObject

/// 获取签名结果字典（用于 JSBridge、IPC 等场景）
/// @param params 包含 url, method, contentType, body 的字典
+ (NSDictionary *)getJdgsSign:(NSDictionary *)params;

/// @param params 包含 body, client, clientVersion, functionId openudid 的字典
+ (NSDictionary *)getHttpSign:(NSDictionary *)params;

// 自动获取签名和jdgs
// + (NSDictionary *)getSign:(NSDictionary *)params;

@end

NS_ASSUME_NONNULL_END