#import "File.h"
#import "Log.h"
#include <unistd.h>
#include <stdio.h> 

@implementation File
// 读取 plist 文件
+ (NSDictionary *)readPlistAtPath:(NSString *)path error:(NSError **)error {
    @try {
        if (!path || [path length] == 0) {
            if (error) {
                *error = [NSError errorWithDomain:@"PlistReadError"
                                             code:400
                                         userInfo:@{NSLocalizedDescriptionKey: @"路径不能为空"}];
            }
            return nil;
        }

        NSFileManager *fileManager = [NSFileManager defaultManager];
        if (![fileManager fileExistsAtPath:path]) {
            if (error) {
                *error = [NSError errorWithDomain:@"PlistReadError"
                                             code:404
                                         userInfo:@{NSLocalizedDescriptionKey: @"文件不存在"}];
            }
            return nil;
        }

        // ⚡️【强制读取方式】⚡️
        // root 直接用 NSData 读取，绕过 `isReadableFileAtPath:` 限制
        NSData *plistData = [NSData dataWithContentsOfFile:path];
        if (!plistData) {
            // 如果 data 方式读取失败，则尝试 `fopen()` 读取二进制数据
            FILE *file = fopen([path UTF8String], "rb");
            if (!file) {
                if (error) {
                    *error = [NSError errorWithDomain:@"PlistReadError"
                                                 code:403
                                             userInfo:@{NSLocalizedDescriptionKey: @"文件存在但无法读取（可能是权限问题）"}];
                }
                return nil;
            }

            // 读取文件内容
            fseek(file, 0, SEEK_END);
            long fileSize = ftell(file);
            rewind(file);

            NSMutableData *fileData = [NSMutableData dataWithLength:fileSize];
            fread(fileData.mutableBytes, 1, fileSize, file);
            fclose(file);

            plistData = fileData;
        }

        // ⚡️ 解析 `.plist` 数据 ⚡️
        NSError *plistError = nil;
        NSDictionary *plistDict = [NSPropertyListSerialization propertyListWithData:plistData
                                                                            options:NSPropertyListImmutable
                                                                             format:nil
                                                                              error:&plistError];

        if (!plistDict || plistError) {
            if (error) {
                *error = [NSError errorWithDomain:@"PlistReadError"
                                             code:500
                                         userInfo:@{NSLocalizedDescriptionKey: @"Plist 解析失败"}];
            }
            return nil;
        }

        return plistDict;

    } @catch (NSException *exception) {
        if (error) {
            *error = [NSError errorWithDomain:@"PlistReadError"
                                         code:500
                                     userInfo:@{NSLocalizedDescriptionKey: [exception description]}];
        }
        return nil;
    }
}

+ (BOOL)checkPathExists:(NSString *)path isDirectory:(BOOL *)isDirectory error:(NSError **)error {
    
    @try {
        if (!path || [path length] == 0) {
            if (error) {
                *error = [NSError errorWithDomain:@"FileCheckError"
                                             code:400
                                         userInfo:@{NSLocalizedDescriptionKey: @"路径不能为空"}];
            }
            return NO;
        }

        NSFileManager *fileManager = [NSFileManager defaultManager];
        BOOL isDir = NO;

        // 先检查文件是否存在
        BOOL exists = [fileManager fileExistsAtPath:path isDirectory:&isDir];
        if (!exists) {
            if (error) {
                *error = [NSError errorWithDomain:@"FileCheckError"
                                             code:404
                                         userInfo:@{NSLocalizedDescriptionKey: @"路径不存在"}];
            }
            return NO;
        }

        // 如果 isDirectory 不为空，才赋值
        if (isDirectory) {
            *isDirectory = isDir;
        }

        // ⚡️【重点修复】⚡️
        // root 用户可以访问任何文件，所以如果 `isReadableFileAtPath:` 失败，尝试直接打开
        if (![fileManager isReadableFileAtPath:path]) {
            FILE *file = fopen([path UTF8String], "r");
            if (file) {
                fclose(file); // 关闭文件，说明可读
            } else {
                if (error) {
                    *error = [NSError errorWithDomain:@"FileCheckError"
                                                 code:403
                                             userInfo:@{NSLocalizedDescriptionKey: @"文件存在但无法读取（可能是权限问题）"}];
                }
                return NO;
            }
        }

        return YES;
    } @catch (NSException *exception) {
        NSLOG(@"[FileCheck] 异常: %@", exception);
        return NO;
    }
}

// 使用 access 函数简单判断文件是否存在
+ (BOOL)fileExistsAtPath:(NSString *)path {
    if (!path || [path length] == 0) {
        return NO;
    }
    
    return access([path UTF8String], F_OK) == 0;
}

// 使用 remove() 函数删除文件
+ (BOOL)removeFileAtPath:(NSString *)path {
    if (!path || [path length] == 0) {
        NSLOG(@"[FileRemove] 路径为空");
        return NO;
    }
    
    // 先检查文件是否存在
    if (![self fileExistsAtPath:path]) {
        // 文件不存在，视为删除成功
        NSLOG(@"[FileRemove] 文件不存在，无需删除: %@", path);
        return YES;
    }
    
    // 文件存在，使用 remove() 函数删除
    int result = remove([path UTF8String]);
    if (result == 0) {
        NSLOG(@"[FileRemove] 成功删除文件: %@", path);
        return YES;
    } else {
        NSLOG(@"[FileRemove] 删除文件失败: %@, 错误码: %d", path, errno);
        return NO;
    }
}

// 创建文本文件并写入内容 - 简化版
+ (BOOL)createTextFile:(NSString *)path withContent:(NSString *)content append:(BOOL)append {
    @try {
        if (!path || [path length] == 0 || !content) {
            NSLOG(@"[FileCreate] 路径或内容为空");
            return NO;
        }
        
        // 确保目录存在
        NSString *directory = [path stringByDeletingLastPathComponent];
        NSFileManager *fileManager = [NSFileManager defaultManager];
        
        if (![fileManager fileExistsAtPath:directory]) {
            [fileManager createDirectoryAtPath:directory 
                  withIntermediateDirectories:YES 
                                   attributes:nil 
                                        error:nil];
        }
        
        BOOL success = NO;
        
        if (append && [fileManager fileExistsAtPath:path]) {
            // 追加模式：如果文件存在，则读取现有内容并追加
            NSError *readError = nil;
            NSString *existingContent = [NSString stringWithContentsOfFile:path 
                                                                  encoding:NSUTF8StringEncoding 
                                                                     error:&readError];
            
            if (readError) {
                NSLOG(@"[FileCreate] 读取现有文件失败: %@, 错误: %@", path, readError.localizedDescription);
                // 如果读取失败，尝试直接写入
                success = [content writeToFile:path 
                                   atomically:YES 
                                     encoding:NSUTF8StringEncoding 
                                        error:nil];
            } else {
                // 检查现有内容是否以换行符结束
                BOOL needsNewline = NO;
                if ([existingContent length] > 0) {
                    unichar lastChar = [existingContent characterAtIndex:[existingContent length] - 1];
                    needsNewline = (lastChar != '\n');
                }
                
                // 追加内容并写回文件
                NSString *newContent;
                if (needsNewline) {
                    newContent = [existingContent stringByAppendingFormat:@"\n%@", content];
                } else {
                    newContent = [existingContent stringByAppendingString:content];
                }
                
                success = [newContent writeToFile:path 
                                      atomically:YES 
                                        encoding:NSUTF8StringEncoding 
                                           error:nil];
            }
        } else {
            // 覆盖模式：直接写入文件
            success = [content writeToFile:path 
                              atomically:YES 
                                encoding:NSUTF8StringEncoding 
                                   error:nil];
        }
        
        if (success) {
            NSLOG(@"[FileCreate] 成功%@文件: %@", append ? @"追加" : @"创建并写入", path);
        } else {
            NSLOG(@"[FileCreate] %@文件失败: %@", append ? @"追加" : @"写入", path);
        }
        
        return success;
    } @catch (NSException *exception) {
        NSLOG(@"[FileCreate] 异常: %@", exception);
        return NO;
    }
}

// 创建文本文件并写入内容 - 带回调版本
+ (void)createTextFile:(NSString *)path withContent:(NSString *)content append:(BOOL)append completion:(void (^)(BOOL success, NSString *errorMessage))completion {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        @try {
            if (!path || [path length] == 0 || !content) {
                NSLOG(@"[FileCreate] 路径或内容为空");
                if (completion) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        completion(NO, @"路径或内容为空");
                    });
                }
                return;
            }
            
            // 确保目录存在
            NSString *directory = [path stringByDeletingLastPathComponent];
            NSFileManager *fileManager = [NSFileManager defaultManager];
            
            if (![fileManager fileExistsAtPath:directory]) {
                NSError *dirError = nil;
                BOOL dirCreated = [fileManager createDirectoryAtPath:directory 
                                      withIntermediateDirectories:YES 
                                                       attributes:nil 
                                                            error:&dirError];
                if (!dirCreated) {
                    NSString *errorMsg = [NSString stringWithFormat:@"创建目录失败: %@", dirError.localizedDescription];
                    NSLOG(@"[FileCreate] %@", errorMsg);
                    if (completion) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            completion(NO, errorMsg);
                        });
                    }
                    return;
                }
            }
            
            NSError *writeError = nil;
            BOOL success = NO;
            
            if (append && [fileManager fileExistsAtPath:path]) {
                // 追加模式：如果文件存在，则读取现有内容并追加
                NSError *readError = nil;
                NSString *existingContent = [NSString stringWithContentsOfFile:path 
                                                                      encoding:NSUTF8StringEncoding 
                                                                         error:&readError];
                
                if (readError) {
                    NSString *errorMsg = [NSString stringWithFormat:@"读取现有文件失败: %@", readError.localizedDescription];
                    NSLOG(@"[FileCreate] %@", errorMsg);
                    // 如果读取失败，尝试直接写入
                    success = [content writeToFile:path 
                                       atomically:YES 
                                         encoding:NSUTF8StringEncoding 
                                            error:&writeError];
                } else {
                    // 检查现有内容是否以换行符结束
                    BOOL needsNewline = NO;
                    if ([existingContent length] > 0) {
                        unichar lastChar = [existingContent characterAtIndex:[existingContent length] - 1];
                        needsNewline = (lastChar != '\n');
                    }
                    
                    // 追加内容并写回文件
                    NSString *newContent;
                    if (needsNewline) {
                        newContent = [existingContent stringByAppendingFormat:@"\n%@", content];
                    } else {
                        newContent = [existingContent stringByAppendingString:content];
                    }
                    
                    success = [newContent writeToFile:path 
                                          atomically:YES 
                                            encoding:NSUTF8StringEncoding 
                                               error:&writeError];
                }
            } else {
                // 覆盖模式：直接写入文件
                success = [content writeToFile:path 
                                  atomically:YES 
                                    encoding:NSUTF8StringEncoding 
                                       error:&writeError];
            }
            
            if (success) {
                NSLOG(@"[FileCreate] 成功%@文件: %@", append ? @"追加" : @"创建并写入", path);
                if (completion) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        completion(YES, nil);
                    });
                }
            } else {
                NSString *errorMsg = [NSString stringWithFormat:@"%@文件失败: %@", 
                                     append ? @"追加" : @"写入", 
                                     writeError ? writeError.localizedDescription : @"未知错误"];
                NSLOG(@"[FileCreate] %@", errorMsg);
                if (completion) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        completion(NO, errorMsg);
                    });
                }
            }
        } @catch (NSException *exception) {
            NSString *errorMsg = [NSString stringWithFormat:@"创建文件异常: %@", exception.reason];
            NSLOG(@"[FileCreate] %@", errorMsg);
            if (completion) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(NO, errorMsg);
                });
            }
        }
    });
}

// 为了保持向后兼容，添加原始方法的实现，内部调用新方法
+ (BOOL)createTextFile:(NSString *)path withContent:(NSString *)content {
    return [self createTextFile:path withContent:content append:NO];
}

+ (void)createTextFile:(NSString *)path withContent:(NSString *)content completion:(void (^)(BOOL success, NSString *errorMessage))completion {
    [self createTextFile:path withContent:content append:NO completion:completion];
}

@end