#import <Foundation/Foundation.h>

@interface File : NSObject
// 读取plist文件
+ (NSDictionary *)readPlistAtPath:(NSString *)path error:(NSError **)error;

// 判断路径是否存在
+ (BOOL)checkPathExists:(NSString *)path isDirectory:(BOOL *)isDirectory error:(NSError **)error;

// 判断文件是否存在
+ (BOOL)fileExistsAtPath:(NSString *)path;

// 使用 remove() 函数删除文件
+ (BOOL)removeFileAtPath:(NSString *)path;

// 创建文本文件并写入内容 - 简化版
+ (BOOL)createTextFile:(NSString *)path withContent:(NSString *)content append:(BOOL)append;

// 创建文本文件并写入内容 - 带回调版本
+ (void)createTextFile:(NSString *)path withContent:(NSString *)content append:(BOOL)append completion:(void (^)(BOOL success, NSString *errorMessage))completion;

@end


// [File createTextFile:@"/var/mobile/Documents/test.txt" 
//     withContent:@"这是测试内容" 
//     append:YES
//      completion:^(BOOL success, NSString *errorMessage) {
// if (success) {
//    NSLog(@"文件创建成功");
//    // 执行成功后的操作
// } else {
//    NSLog(@"文件创建失败: %@", errorMessage);
//    // 处理错误
// }
// }];