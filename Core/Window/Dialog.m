#import "Dialog.h"
#import <UIKit/UIKit.h>

@implementation Dialog

// 静态变量用于跟踪当前的弹窗
static UIWindow *currentDialogWindow = nil;

+ (void)show:(NSString *)message duration:(NSTimeInterval)duration {
    // 如果当前有弹窗，先关闭它
    if (currentDialogWindow) {
        currentDialogWindow.hidden = YES;
        currentDialogWindow = nil;
    }

    // 创建新的顶层 UIWindow
    UIWindow *dialogWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    dialogWindow.windowLevel = UIWindowLevelAlert + 500;
    dialogWindow.backgroundColor = UIColor.clearColor;
    dialogWindow.rootViewController = [UIViewController new];
    [dialogWindow makeKeyAndVisible];

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"提示"
                                                                   message:message
                                                            preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                       style:UIAlertActionStyleDefault
                                                     handler:^(UIAlertAction * _Nonnull action) {
        dialogWindow.hidden = YES;
        currentDialogWindow = nil; // 清除当前弹窗的引用
    }];

    [alert addAction:okAction];
    [dialogWindow.rootViewController presentViewController:alert animated:YES completion:nil];

    // 自动关闭逻辑（如果 duration > 0）
    if (duration > 0) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(duration * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (!alert.beingDismissed && alert.presentingViewController) {
                [alert dismissViewControllerAnimated:YES completion:^{
                    dialogWindow.hidden = YES;
                    currentDialogWindow = nil; // 清除当前弹窗的引用
                }];
            }
        });
    }

    // 更新当前弹窗的引用
    currentDialogWindow = dialogWindow;
}

@end