#import <UIKit/UIKit.h>

// 按钮配置的键
extern NSString * const kButtonTitleKey;    // 按钮的标题
extern NSString * const kButtonIdKey;       // 按钮的标识符，用于区分按钮
extern NSString * const kButtonIconKey;     // 按钮的图标，可选
extern NSString * const kButtonAutoCollapseKey; // 控制点击按钮后是否自动收起

@interface FloatingView : UIView

// 添加类方法用于管理所有窗口实例
+ (void)registerWindow:(FloatingView *)window;
+ (void)unregisterWindow:(FloatingView *)window;
+ (void)collapseAllWindowsExcept:(FloatingView *)exceptWindow;

// 主控制器相关方法
+ (FloatingView *)createMasterControlWindow;
+ (void)toggleAllWindowsVisibility;
+ (FloatingView *)masterControlWindow;
+ (BOOL)areAllWindowsVisible;

// 复位功能
+ (void)resetAllWindowsToInitialPositions;

- (void)resetToInitialPosition;

// 窗口删除功能
+ (BOOL)removeWindowWithId:(NSString *)windowId;

// 窗口管理功能
- (void)collapseAllExpandedWindows;

// 使用配置初始化悬浮窗
- (instancetype)initWithConfig:(NSDictionary *)config;

// 展开/收起悬浮窗
- (void)toggleExpand;

// 强制收起悬浮窗（用于互斥展开）
- (void)forceCollapse;

// 边缘吸附动画
- (void)snapToEdgeWithAnimation;

// 添加单个按钮
- (void)addButton:(NSDictionary *)buttonConfig;

// 删除指定ID的按钮
- (void)removeButtonWithId:(NSString *)buttonId;

// 批量更新按钮配置
- (void)updateButtonsWithConfigs:(NSArray<NSDictionary *> *)buttonConfigs;

@end