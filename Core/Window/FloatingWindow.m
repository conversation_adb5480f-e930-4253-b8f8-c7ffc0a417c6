#import "FloatingWindow.h"
#import "Log.h"

@implementation FloatingWindow

/**
 * 动态获取最高窗口层级
 * 确保FloatingWindow始终显示在最顶层
 */
+ (UIWindowLevel)getMaxWindowLevel {
    UIWindowLevel maxLevel = UIWindowLevelAlert;

    @try {
        // 遍历所有Scene和窗口，找到最高层级
        NSSet *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            if ([scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                for (UIWindow *window in windowScene.windows) {
                    if (window.windowLevel > maxLevel) {
                        maxLevel = window.windowLevel;
                    }
                }
            }
        }
    } @catch (NSException *exception) {
        LOG("[FloatingWindow] 获取窗口层级异常: %s", exception.reason.UTF8String);
    }

    // 确保足够高的层级
    UIWindowLevel targetLevel = maxLevel + 2000;
    LOG("[FloatingWindow] 设置窗口层级: %f", targetLevel);

    return targetLevel;
}

/**
 * 获取当前活跃的WindowScene
 */
+ (UIWindowScene *)getActiveWindowScene {
    UIWindowScene *activeScene = nil;

    @try {
        NSSet *connectedScenes = [UIApplication sharedApplication].connectedScenes;

        // 优先查找前台活跃的Scene
        for (UIScene *scene in connectedScenes) {
            if ([scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                if (windowScene.activationState == UISceneActivationStateForegroundActive) {
                    activeScene = windowScene;
                    break;
                }
            }
        }

        // 如果没有前台活跃的，找第一个可用的
        if (!activeScene) {
            for (UIScene *scene in connectedScenes) {
                if ([scene isKindOfClass:[UIWindowScene class]]) {
                    activeScene = (UIWindowScene *)scene;
                    break;
                }
            }
        }
    } @catch (NSException *exception) {
        LOG("[FloatingWindow] 获取WindowScene异常: %s", exception.reason.UTF8String);
    }

    return activeScene;
}

- (instancetype)init {
    self = [super initWithFrame:[UIScreen mainScreen].bounds];
    if (self) {
        LOG("[FloatingWindow] 开始初始化FloatingWindow");

        // 动态设置最高窗口层级
        self.windowLevel = [FloatingWindow getMaxWindowLevel];
        self.backgroundColor = [UIColor clearColor];
        self.userInteractionEnabled = YES;

        // 设置WindowScene（iOS 13+必需）
        UIWindowScene *activeScene = [FloatingWindow getActiveWindowScene];
        if (activeScene) {
            self.windowScene = activeScene;
            LOG("[FloatingWindow] 成功设置WindowScene");
        } else {
            LOG("[FloatingWindow] ⚠️ 未找到可用的WindowScene");
        }

        // 设置根视图控制器
        self.rootViewController = [[UIViewController alloc] init];
        self.rootViewController.view.backgroundColor = [UIColor clearColor];

        // 显示窗口
        self.hidden = NO;

        // 确保不会成为keyWindow
        [self resignKeyWindow];

        LOG("[FloatingWindow] FloatingWindow初始化完成，层级: %f", self.windowLevel);
    }
    return self;
}

// 防止成为keyWindow
- (BOOL)canBecomeKeyWindow {
    return NO;
}

// 防止成为第一响应者
- (BOOL)canBecomeFirstResponder {
    return NO;
}

// 只处理悬浮视图的触摸事件，其他事件穿透
- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    // 如果窗口隐藏或不可交互，直接穿透
    if (self.hidden || !self.userInteractionEnabled || self.alpha < 0.01) {
        return nil;
    }

    // 快速检查：如果点击位置不在任何FloatingView区域内，直接穿透
    if (![self isPointInFloatingViewArea:point]) {
        return nil;
    }

    // 只有在FloatingView区域内才进行详细的hitTest
    UIView *hitView = [super hitTest:point withEvent:event];

    // 如果没有命中任何视图，穿透事件
    if (!hitView) {
        return nil;
    }

    // 如果点击的是窗口本身或根视图控制器的view，穿透事件
    if (hitView == self || hitView == self.rootViewController.view) {
        return nil;
    }

    // 验证命中的视图确实属于FloatingView层次结构
    UIView *currentView = hitView;
    while (currentView && currentView != self.rootViewController.view) {
        NSString *className = NSStringFromClass([currentView class]);

        // 检查是否是FloatingView或其相关组件
        if ([className isEqualToString:@"FloatingView"] ||
            [currentView isKindOfClass:[UIButton class]] ||
            [className containsString:@"Floating"]) {

            // 这是FloatingView的有效组件，允许处理事件
            return hitView;
        }
        currentView = currentView.superview;
    }

    // 其他所有情况都穿透事件，确保不影响SpringBoard的正常操作
    return nil;
}

// 强制显示并保持在最顶层
- (void)makeKeyAndVisible {
    LOG("[FloatingWindow] 尝试显示窗口");

    @try {
        // 确保窗口层级是最高的
        self.windowLevel = [FloatingWindow getMaxWindowLevel];

        // 强制显示
        self.hidden = NO;
        self.alpha = 1.0;

        // 调用父类方法
        [super makeKeyAndVisible];

        // 立即恢复原应用的keyWindow状态
        dispatch_async(dispatch_get_main_queue(), ^{
            [self resignKeyWindow];

            // 查找并恢复原应用的主窗口
            UIWindowScene *activeScene = [FloatingWindow getActiveWindowScene];
            if (activeScene) {
                for (UIWindow *window in activeScene.windows) {
                    if (window != self && !window.isHidden && window.rootViewController) {
                        [window makeKeyWindow];
                        break;
                    }
                }
            }
        });

        LOG("[FloatingWindow] 窗口显示成功，层级: %f, hidden: %d",
               self.windowLevel, self.hidden);

    } @catch (NSException *exception) {
        LOG("[FloatingWindow] 显示窗口异常: %s", exception.reason.UTF8String);
    }
}

/**
 * 强制刷新窗口显示状态
 * 用于解决某些系统下窗口不显示的问题
 */
- (void)forceRefreshDisplay {
    LOG("[FloatingWindow] 强制刷新窗口显示");

    dispatch_async(dispatch_get_main_queue(), ^{
        // 临时隐藏再显示，强制刷新
        self.hidden = YES;

        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.hidden = NO;
            self.windowLevel = [FloatingWindow getMaxWindowLevel];

            // 强制重新布局
            [self setNeedsLayout];
            [self layoutIfNeeded];

            // 强制重绘所有子视图
            [self.rootViewController.view setNeedsDisplay];
            [self.rootViewController.view setNeedsLayout];
            [self.rootViewController.view layoutIfNeeded];

            LOG("[FloatingWindow] 窗口刷新完成");
        });
    });
}

/**
 * 验证窗口是否真正可见
 */
- (BOOL)isActuallyVisible {
    BOOL visible = !self.hidden && self.alpha > 0 && self.superview == nil; // 窗口没有superview是正常的
    LOG("[FloatingWindow] 窗口可见性检查: hidden=%d, alpha=%f, windowLevel=%f",
           self.hidden, self.alpha, self.windowLevel);
    return visible;
}

/**
 * 检查点击位置是否在FloatingView的有效区域内
 */
- (BOOL)isPointInFloatingViewArea:(CGPoint)point {
    // 遍历所有FloatingView子视图
    for (UIView *subview in self.rootViewController.view.subviews) {
        if ([NSStringFromClass([subview class]) isEqualToString:@"FloatingView"]) {
            // 将点击坐标转换到FloatingView的坐标系
            CGPoint localPoint = [self.rootViewController.view convertPoint:point toView:subview];

            // 检查是否在FloatingView的bounds内
            if (CGRectContainsPoint(subview.bounds, localPoint)) {
                LOG("[FloatingWindow] 点击在FloatingView区域内: %s",
                       NSStringFromCGPoint(localPoint).UTF8String);
                return YES;
            }
        }
    }
    return NO;
}

/**
 * 添加子视图的优化方法
 * 确保子视图添加到正确的位置
 */
- (void)addFloatingSubview:(UIView *)view {
    if (self.rootViewController && self.rootViewController.view) {
        [self.rootViewController.view addSubview:view];
        LOG("[FloatingWindow] 子视图已添加到rootViewController.view");
    } else {
        [self addSubview:view];
        LOG("[FloatingWindow] 子视图已添加到window（备用方案）");
    }
}

@end