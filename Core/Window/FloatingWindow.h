#import <UIKit/UIKit.h>

/**
 * @brief 针对多系统兼容性优化的悬浮窗口
 *
 * 解决某些系统下FloatingView不显示的问题
 * 支持动态窗口层级、强制刷新显示等功能
 */
@interface FloatingWindow : UIWindow

/**
 * @brief 强制刷新窗口显示状态
 *
 * 用于解决某些系统下窗口不显示的问题
 * 通过临时隐藏再显示来强制刷新窗口
 */
- (void)forceRefreshDisplay;

/**
 * @brief 验证窗口是否真正可见
 *
 * @return 窗口是否实际可见
 */
- (BOOL)isActuallyVisible;

/**
 * @brief 添加子视图的优化方法
 *
 * 确保子视图添加到正确的位置（rootViewController.view）
 *
 * @param view 要添加的子视图
 */
- (void)addFloatingSubview:(UIView *)view;

/**
 * @brief 获取动态计算的最高窗口层级
 *
 * @return 适合当前系统环境的窗口层级
 */
+ (UIWindowLevel)getMaxWindowLevel;

/**
 * @brief 检查点击位置是否在FloatingView的有效区域内
 *
 * 用于精确的事件穿透控制，确保只有FloatingView区域响应触摸
 *
 * @param point 点击位置（窗口坐标系）
 * @return 是否在FloatingView区域内
 */
- (BOOL)isPointInFloatingViewArea:(CGPoint)point;

@end