#import "Toast.h"
#import <UIKit/UIKit.h>
#import <objc/runtime.h>

@implementation Toast

static UIWindow *currentToastWindow = nil;
static UILabel *currentToastLabel = nil;

/**
 * 获取最高的窗口层级
 * 确保Toast显示在所有窗口之上
 */
+ (UIWindowLevel)getHighestWindowLevel {
    UIWindowLevel maxLevel = UIWindowLevelAlert;

    // iOS 13+ 使用 Scene 架构遍历所有窗口
    NSMutableArray *allWindows = [NSMutableArray array];
    for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
        if ([scene isKindOfClass:[UIWindowScene class]]) {
            [allWindows addObjectsFromArray:scene.windows];
        }
    }

    for (UIWindow *window in allWindows) {
        if (window.windowLevel > maxLevel) {
            maxLevel = window.windowLevel;
        }
    }

    // 确保Toast窗口层级足够高
    return maxLevel + 1000;
}

/**
 * 创建或更新Toast窗口
 */
+ (void)setupToastWindow {
    CGSize screenSize = [UIScreen mainScreen].bounds.size;

    if (currentToastWindow && currentToastLabel) {
        // 清理现有的Toast
        [currentToastLabel.layer removeAllAnimations];
        [currentToastLabel removeFromSuperview];
        currentToastLabel = nil;
    } else {
        // 创建新的Toast窗口
        currentToastWindow = [[UIWindow alloc] initWithFrame:CGRectMake(0, 0, screenSize.width, screenSize.height)];
        currentToastWindow.backgroundColor = UIColor.clearColor;
        currentToastWindow.userInteractionEnabled = NO;
        currentToastWindow.rootViewController = [UIViewController new];

        // 设置最高层级
        currentToastWindow.windowLevel = [self getHighestWindowLevel];

        // 获取当前活跃的 windowScene
        UIWindowScene *activeScene = nil;
        for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if ([scene isKindOfClass:[UIWindowScene class]] && scene.activationState == UISceneActivationStateForegroundActive) {
                activeScene = scene;
                break;
            }
        }

        if (activeScene) {
            currentToastWindow.windowScene = activeScene;
        }

        // 激活窗口
        currentToastWindow.hidden = NO;
        [currentToastWindow makeKeyAndVisible];

        // 立即恢复原来的 key window（避免影响应用正常交互）
        dispatch_async(dispatch_get_main_queue(), ^{
            UIWindow *mainWindow = nil;
            for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
                if ([scene isKindOfClass:[UIWindowScene class]]) {
                    for (UIWindow *window in scene.windows) {
                        if (window.isKeyWindow && window != currentToastWindow) {
                            mainWindow = window;
                            break;
                        }
                    }
                    if (mainWindow) break;
                }
            }

            if (mainWindow) {
                [mainWindow makeKeyWindow];
            }
        });
    }
}

+ (void)show:(NSString *)message duration:(NSTimeInterval)duration {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self setupToastWindow];

        CGSize screenSize = [UIScreen mainScreen].bounds.size;

        // 创建Toast标签
        UILabel *label = [[UILabel alloc] init];
        label.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.9];
        label.textColor = [UIColor whiteColor];
        label.textAlignment = NSTextAlignmentCenter;
        // label.font = [UIFont systemFontOfSize:14.0];
        // 字体加粗
        label.font = [UIFont boldSystemFontOfSize:14.0];
        label.layer.cornerRadius = 8;
        label.layer.masksToBounds = YES;
        label.numberOfLines = 0; // 支持多行

        // 添加阴影效果，增强可见性
        label.layer.shadowColor = [UIColor blackColor].CGColor;
        label.layer.shadowOffset = CGSizeMake(0, 2);
        label.layer.shadowOpacity = 0.8;
        label.layer.shadowRadius = 4;

        CGFloat maxWidth = screenSize.width * 0.65;      // 最大宽度
        CGFloat contentPadding = 14;                     // 增加内边距

        // 计算文字区域
        CGSize maxTextSize = CGSizeMake(maxWidth - 2*contentPadding, CGFLOAT_MAX);

        CGRect textRect = [message boundingRectWithSize:maxTextSize
                                                 options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                                              attributes:@{NSFontAttributeName:label.font}
                                                 context:nil];

        CGFloat labelWidth = textRect.size.width + 2*contentPadding;
        CGFloat labelHeight = textRect.size.height + 2*contentPadding;

        if (labelWidth > maxWidth) {
            labelWidth = maxWidth;
        }

        // 确保最小尺寸
        if (labelWidth < 80) labelWidth = 80;
        if (labelHeight < 36) labelHeight = 36;

        // 计算安全区域，避免被刘海屏或底部指示器遮挡
        CGFloat bottomSafeArea = 0;

        // 获取当前活跃窗口的安全区域
        UIWindow *activeWindow = nil;
        for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if ([scene isKindOfClass:[UIWindowScene class]]) {
                for (UIWindow *window in scene.windows) {
                    if (window.isKeyWindow) {
                        activeWindow = window;
                        break;
                    }
                }
                if (activeWindow) break;
            }
        }

        if (activeWindow) {
            bottomSafeArea = activeWindow.safeAreaInsets.bottom;
        }

        // 设置Toast位置（距离底部安全距离）
        CGFloat yPosition = screenSize.height - labelHeight - 100 - bottomSafeArea;

        label.frame = CGRectMake((screenSize.width - labelWidth)/2,
                                 yPosition,
                                 labelWidth,
                                 labelHeight);

        // 设置 attributedText 以支持多行 & 居中
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.alignment = NSTextAlignmentCenter;
        paragraphStyle.lineBreakMode = NSLineBreakByWordWrapping;

        NSAttributedString *attrText = [[NSAttributedString alloc] initWithString:message
                                                                        attributes:@{
            NSFontAttributeName: label.font,
            NSForegroundColorAttributeName: label.textColor,
            NSParagraphStyleAttributeName: paragraphStyle
        }];

        label.attributedText = attrText;

        // 添加到窗口
        [currentToastWindow.rootViewController.view addSubview:label];
        currentToastLabel = label;

        // 动画显示
        label.alpha = 0;
        label.transform = CGAffineTransformMakeScale(0.8, 0.8);

        [UIView animateWithDuration:0.3
                              delay:0
             usingSpringWithDamping:0.7
              initialSpringVelocity:0.5
                            options:UIViewAnimationOptionCurveEaseOut
                         animations:^{
            label.alpha = 1;
            label.transform = CGAffineTransformIdentity;
        } completion:^(BOOL finished) {
            // 延时隐藏
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(duration * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (currentToastLabel == label) {
                    [UIView animateWithDuration:0.3 animations:^{
                        label.alpha = 0;
                        label.transform = CGAffineTransformMakeScale(0.8, 0.8);
                    } completion:^(BOOL finished) {
                        [label removeFromSuperview];
                        if (currentToastLabel == label) {
                            currentToastLabel = nil;
                            currentToastWindow.hidden = YES;
                            currentToastWindow = nil;
                        }
                    }];
                }
            });
        }];
    });
}

@end
