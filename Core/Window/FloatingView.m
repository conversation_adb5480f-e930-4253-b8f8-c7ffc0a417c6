#import "FloatingView.h"
#import "Log.h"

// 常量定义
static const CGFloat kMainButtonSize = 35.0;           // 主按钮的尺寸，圆形按钮的宽高相等
static const CGFloat kFunctionButtonWidth = 110.0;     // 功能按钮的宽度，用于展开后的功能按钮
static const CGFloat kFunctionButtonHeight = 40.0;     // 功能按钮的高度，用于展开后的功能按钮
static const CGFloat kFunctionButtonCornerRadius = 12.0; // 功能按钮的圆角半径，控制按钮边缘的圆滑程度
static const CGFloat kFunctionButtonFontSize = 13.0;   // 功能按钮内文字的字体大小
static const CGFloat kButtonSpacing = 5.0;            // 按钮之间的水平间距，以及按钮与窗口边缘的间距
static const CGFloat kButtonVerticalSpacing = 5.0;     // 功能按钮之间的垂直间距，控制展开后按钮的上下间隔
static const CGFloat kMainButtonAlphaCollapsed = 0.5;  // 主按钮在收起状态下的透明度
static const CGFloat kMainButtonAlphaExpanded = 1.0;   // 主按钮在展开状态下的透明度
static const CGFloat kFunctionButtonAlpha = 0.9;       // 功能按钮的透明度，控制展开后按钮的显示效果

static const CGFloat kMainButtonBorderWidth = 2.0;     // 主按钮的描边宽度
static const CGFloat kMainButtonBorderAlpha = 0.7;     // 主按钮描边的不透明度

static const CGFloat kMainButtonBorderAlphaCollapsed = 0.7;  // 收起状态下描边的不透明度
static const CGFloat kMainButtonBorderAlphaExpanded = 1.0;   // 展开状态下描边的不透明度

static const CGFloat kEdgeMargin = kMainButtonBorderWidth + 2.0; // 窗口吸附到屏幕边缘时与屏幕边缘的距离

static const BOOL kDebugMode = NO;


// 按钮配置的键
NSString * const kButtonTitleKey = @"title";    // 按钮的标题
NSString * const kButtonIdKey = @"id";        // 按钮的标识符，用于区分按钮
NSString * const kButtonIconKey = @"icon";        // 按钮的图标，可选
NSString * const kButtonAutoCollapseKey = @"autoCollapse"; // 控制点击按钮后是否自动收起

typedef NS_ENUM(NSInteger, FloatingWindowEdge) {
    FloatingWindowEdgeLeft,
    FloatingWindowEdgeRight
};

// 添加静态变量用于管理所有窗口实例 - 增强内存保护
static NSMutableArray<FloatingView *> * volatile allFloatingWindows = nil;
static NSLock *windowArrayLock = nil;
// 主控制器窗口实例
static FloatingView *masterControlWindow = nil;
// 所有窗口的显示状态
static BOOL allWindowsVisible = YES;

// 安全获取窗口数组的函数
static NSMutableArray<FloatingView *> *safeGetWindowArray(void) {
    [windowArrayLock lock];
    if (!allFloatingWindows) {
        // 使用强引用初始化，避免autorelease问题
        allFloatingWindows = [[NSMutableArray alloc] initWithCapacity:10];
    }
    NSMutableArray<FloatingView *> *result = allFloatingWindows;
    [windowArrayLock unlock];
    return result;
}

// 获取窗口数量的安全函数
static NSUInteger getWindowCount(void) {
    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    return array.count;
}

// 打印当前窗口状态的调试函数
// static void logCurrentWindowState(NSString *context) {
//     NSUInteger count = getWindowCount();
//     NSLOG(@"[FloatingView] 窗口状态 [%@]: 总数=%lu, 可见=%@",
//           context, (unsigned long)count, allWindowsVisible ? @"是" : @"否");
// }

@interface FloatingView ()
@property (nonatomic, strong) UIButton *mainButton;
@property (nonatomic, strong) NSMutableArray<UIButton *> *functionButtons;
@property (nonatomic, assign) BOOL isExpanded;
@property (nonatomic, assign) BOOL isDragging;
@property (nonatomic, assign) CGPoint initialTouchPoint;
@property (nonatomic, assign) FloatingWindowEdge currentEdge;
@property (nonatomic, strong) NSDictionary *buttonConfigs;
@property (nonatomic, copy) void (^buttonActionBlock)(NSString *buttonId);
@property (nonatomic, assign) CGPoint initialPosition;
@property (nonatomic, strong) NSMutableArray *currentButtonConfigs;
@property (nonatomic, assign) BOOL isMasterControl; // 标识是否为主控制器
@property (nonatomic, assign) BOOL isHidden; // 窗口隐藏状态
@property (nonatomic, strong) NSString *windowId; // 窗口唯一标识符
@end

@implementation FloatingView

#pragma mark - 位置记忆功能

// 保存窗口位置到UserDefaults
- (void)saveWindowPosition {
    if (!self.windowId || [self.windowId length] == 0) {
        NSLOG(@"[FloatingView] ⚠️ 窗口ID为空，无法保存位置");
        return;
    }

    @try {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *key = [NSString stringWithFormat:@"FloatingWindow_Position_%@", self.windowId];

        NSDictionary *positionData = @{
            @"x": @(self.frame.origin.x),
            @"y": @(self.frame.origin.y),
            @"edge": @(self.currentEdge)
        };

        [defaults setObject:positionData forKey:key];
        [defaults synchronize];

        NSLOG(@"[FloatingView] ✅ 保存窗口位置 - ID: %@, 位置: %@", self.windowId, NSStringFromCGPoint(self.frame.origin));
    } @catch (NSException *exception) {
        NSLOG(@"[FloatingView] ❌ 保存位置失败: %@", exception.reason);
    }
}

// 从UserDefaults恢复窗口位置
- (CGPoint)getSavedWindowPosition {
    if (!self.windowId || [self.windowId length] == 0) {
        NSLOG(@"[FloatingView] ⚠️ 窗口ID为空，无法恢复位置");
        return CGPointZero;
    }

    @try {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *key = [NSString stringWithFormat:@"FloatingWindow_Position_%@", self.windowId];

        NSDictionary *positionData = [defaults objectForKey:key];
        if (positionData && [positionData isKindOfClass:[NSDictionary class]]) {
            CGFloat x = [positionData[@"x"] floatValue];
            CGFloat y = [positionData[@"y"] floatValue];
            FloatingWindowEdge savedEdge = [positionData[@"edge"] integerValue];

            // 恢复边缘状态
            self.currentEdge = savedEdge;

            CGPoint savedPosition = CGPointMake(x, y);
            NSLOG(@"[FloatingView] ✅ 恢复窗口位置 - ID: %@, 位置: %@", self.windowId, NSStringFromCGPoint(savedPosition));
            return savedPosition;
        }
    } @catch (NSException *exception) {
        NSLOG(@"[FloatingView] ❌ 恢复位置失败: %@", exception.reason);
    }

    return CGPointZero; // 没有保存的位置
}

// 复位当前窗口到初始位置
- (void)resetToInitialPosition {
    if (!self.windowId || [self.windowId length] == 0) {
        NSLOG(@"[FloatingView] ⚠️ 窗口ID为空，无法复位位置");
        return;
    }

    NSLOG(@"[FloatingView] 开始复位窗口到初始位置 - ID: %@", self.windowId);
    NSLOG(@"[FloatingView] 当前位置: %@", NSStringFromCGPoint(self.frame.origin));
    NSLOG(@"[FloatingView] 初始位置: %@", NSStringFromCGPoint(self.initialPosition));
    NSLOG(@"[FloatingView] 窗口是否展开: %@", self.isExpanded ? @"是" : @"否");

    // 检查初始位置是否有效
    if (CGPointEqualToPoint(self.initialPosition, CGPointZero)) {
        NSLOG(@"[FloatingView] ⚠️ 初始位置为零点，可能未正确保存");
        return;
    }

    // 如果当前是展开状态，先收起
    if (self.isExpanded) {
        NSLOG(@"[FloatingView] 窗口处于展开状态，先收起");
        [self forceCollapse];

        // 延迟执行复位，等待收起动画完成
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self performResetToInitialPosition];
        });
    } else {
        NSLOG(@"[FloatingView] 窗口未展开，直接执行复位");
        [self performResetToInitialPosition];
    }
}

// 执行复位到初始位置的动画
- (void)performResetToInitialPosition {
    NSLOG(@"[FloatingView] 开始执行复位动画 - ID: %@", self.windowId);

    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    NSLOG(@"[FloatingView] 屏幕尺寸: %@", NSStringFromCGSize(screenSize));

    // 使用保存的初始位置
    CGPoint targetPosition = self.initialPosition;
    NSLOG(@"[FloatingView] 原始目标位置: %@", NSStringFromCGPoint(targetPosition));

    // 确保目标位置在屏幕范围内
    targetPosition.x = MAX(kEdgeMargin, MIN(screenSize.width - kMainButtonSize - kEdgeMargin, targetPosition.x));
    targetPosition.y = MAX(kEdgeMargin, MIN(screenSize.height - kMainButtonSize - kEdgeMargin, targetPosition.y));
    NSLOG(@"[FloatingView] 调整后目标位置: %@", NSStringFromCGPoint(targetPosition));

    // 根据初始位置重新计算边缘状态
    CGFloat midX = screenSize.width / 2;
    if (targetPosition.x < midX) {
        self.currentEdge = FloatingWindowEdgeLeft;
    } else {
        self.currentEdge = FloatingWindowEdgeRight;
    }

    NSLOG(@"[FloatingView] 复位目标位置: %@, 边缘: %ld", NSStringFromCGPoint(targetPosition), (long)self.currentEdge);

    // 先清除保存的位置，确保不会被覆盖
    [self clearSavedPosition];

    // 确保在主线程执行UI更新
    dispatch_async(dispatch_get_main_queue(), ^{
        NSLOG(@"[FloatingView] 开始执行UI动画");

        // 执行复位动画 - 立即移动到初始位置
        [UIView animateWithDuration:0.5
                              delay:0
             usingSpringWithDamping:0.7
              initialSpringVelocity:0.5
                            options:UIViewAnimationOptionCurveEaseOut
                         animations:^{
            NSLOG(@"[FloatingView] 动画中 - 设置frame到: %@", NSStringFromCGPoint(targetPosition));
            self.frame = CGRectMake(targetPosition.x, targetPosition.y, kMainButtonSize, kMainButtonSize);
        } completion:^(BOOL finished) {
            NSLOG(@"[FloatingView] 动画完成状态: %@", finished ? @"成功" : @"失败");
            if (finished) {
                NSLOG(@"[FloatingView] ✅ 窗口复位完成 - ID: %@, 最终位置: %@", self.windowId, NSStringFromCGPoint(self.frame.origin));
            }
        }];
    });
}

// 清除保存的位置信息
- (void)clearSavedPosition {
    if (!self.windowId || [self.windowId length] == 0) {
        NSLOG(@"[FloatingView] ⚠️ 窗口ID为空，无法清除保存的位置");
        return;
    }

    @try {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *key = [NSString stringWithFormat:@"FloatingWindow_Position_%@", self.windowId];

        [defaults removeObjectForKey:key];
        [defaults synchronize];

        NSLOG(@"[FloatingView] ✅ 已清除保存的位置 - ID: %@", self.windowId);
    } @catch (NSException *exception) {
        NSLOG(@"[FloatingView] ❌ 清除保存位置失败: %@", exception.reason);
    }
}

// 检查是否有保存的位置
- (BOOL)hasSavedPosition {
    if (!self.windowId || [self.windowId length] == 0) {
        return NO;
    }

    @try {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *key = [NSString stringWithFormat:@"FloatingWindow_Position_%@", self.windowId];

        NSDictionary *positionData = [defaults objectForKey:key];
        return (positionData != nil && [positionData isKindOfClass:[NSDictionary class]]);
    } @catch (NSException *exception) {
        NSLOG(@"[FloatingView] ❌ 检查保存位置失败: %@", exception.reason);
        return NO;
    }
}

// 类方法实现
+ (void)initialize {
    if (self == [FloatingView class]) {
        // 初始化线程安全锁
        windowArrayLock = [[NSLock alloc] init];
        // 使用安全函数初始化数组
        (void)safeGetWindowArray();
        NSLOG(@"[FloatingView] 多窗口管理系统初始化完成");
    }
}

+ (void)registerWindow:(FloatingView *)window {
    if (!window) return;

    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    [windowArrayLock lock];
    if (![array containsObject:window]) {
        [array addObject:window];
        NSLOG(@"[FloatingView] 窗口注册成功: %@, 当前窗口数: %lu", window.windowId, (unsigned long)array.count);
    }
    [windowArrayLock unlock];
}

+ (void)unregisterWindow:(FloatingView *)window {
    if (!window) return;

    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    [windowArrayLock lock];
    [array removeObject:window];
    NSLOG(@"[FloatingView] 窗口注销成功: %@, 剩余窗口数: %lu", window.windowId, (unsigned long)array.count);
    [windowArrayLock unlock];
}

+ (void)collapseAllWindowsExcept:(FloatingView *)exceptWindow {
    NSUInteger windowCount = getWindowCount();

    // 性能优化：只有多个窗口时才需要遍历
    if (windowCount <= 1) {
        NSLOG(@"[FloatingView] 只有 %lu 个窗口，跳过收起操作", (unsigned long)windowCount);
        return;
    }

    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    NSInteger collapsedCount = 0;
    (void)collapsedCount;

    [windowArrayLock lock];
    for (FloatingView *window in array) {
        if (window != exceptWindow && window.isExpanded) {
            [window forceCollapse];
            collapsedCount++;
        }
    }
    [windowArrayLock unlock];

    NSLOG(@"[FloatingView] 收起了 %ld 个其他窗口，例外窗口: %@", (long)collapsedCount, exceptWindow.windowId);
}

// 创建主控制器窗口
+ (FloatingView *)createMasterControlWindow {
    if (masterControlWindow) {
        NSLOG(@"[FloatingView] 主控制器已存在，返回现有实例");
        return masterControlWindow;
    }

    NSLOG(@"[FloatingView] 开始创建主控制器窗口");

    // 主控制器配置
    NSDictionary *masterConfig = @{
        @"windowId": @"master_control_window", // 🎯 主控制器专用ID
        @"buttonText": @"⊖", // 默认显示状态图标
        @"color": [UIColor colorWithRed:0.2 green:0.7 blue:0.3 alpha:0.9], // 绿色背景表示活跃
        @"position": @"right",
        @"startY": @(100),
        @"buttonConfigs": @[], // 没有功能按钮
        @"actionBlock": ^(NSString *buttonId) {
            // 主控制器按钮点击事件
            NSLOG(@"[FloatingView] 主控制器按钮被点击");
            [FloatingView toggleAllWindowsVisibility];
        }
    };

    NSLOG(@"[FloatingView] 主控制器配置: %@", masterConfig);

    masterControlWindow = [[FloatingView alloc] initWithConfig:masterConfig];
    masterControlWindow.isMasterControl = YES; // 标记为主控制器

    NSLOG(@"[FloatingView] 主控制器创建完成，frame: %@, alpha: %f",
          NSStringFromCGRect(masterControlWindow.frame), masterControlWindow.alpha);

    return masterControlWindow;
}

// 切换所有窗口的显示状态
+ (void)toggleAllWindowsVisibility {
    // 🎯 如果要隐藏窗口，先检查是否有展开的窗口需要收起
    if (allWindowsVisible) {
        BOOL hasExpandedWindows = NO;

        // 检查是否有展开的窗口
        for (FloatingView *window in allFloatingWindows) {
            if (window != masterControlWindow && window.isExpanded) {
                hasExpandedWindows = YES;
                break;
            }
        }

        // 如果有展开的窗口，先收起它们，然后延迟隐藏所有窗口
        if (hasExpandedWindows) {
            [FloatingView collapseAllWindowsExcept:nil]; // 收起所有窗口

            // 延迟0.3秒后隐藏所有窗口，让收起动画完成
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [FloatingView hideAllWindows];
            });
            return;
        }
    }

    allWindowsVisible = !allWindowsVisible;

    // 更新主控制器按钮图标和颜色
    if (masterControlWindow) {
        if (allWindowsVisible) {
            // 显示状态：使用减号表示可以隐藏
            [masterControlWindow.mainButton setTitle:@"⊖" forState:UIControlStateNormal];
            masterControlWindow.mainButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.7 blue:0.3 alpha:0.9]; // 绿色表示活跃
            [masterControlWindow.mainButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        } else {
            // 隐藏状态：使用加号表示可以显示
            [masterControlWindow.mainButton setTitle:@"⊕" forState:UIControlStateNormal];
            masterControlWindow.mainButton.backgroundColor = [UIColor colorWithRed:0.8 green:0.4 blue:0.4 alpha:0.7]; // 红色表示隐藏
            [masterControlWindow.mainButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        }

        // 添加按钮点击动画效果
        [UIView animateWithDuration:0.2 animations:^{
            masterControlWindow.mainButton.transform = CGAffineTransformMakeScale(1.2, 1.2);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.2 animations:^{
                masterControlWindow.mainButton.transform = CGAffineTransformIdentity;
            }];
        }];
    }

    // 显示/隐藏所有非主控制器窗口
    for (FloatingView *window in allFloatingWindows) {
        if (window != masterControlWindow) {
            if (allWindowsVisible) {
                [window showWindow];
            } else {
                [window hideWindow];
            }
        }
    }
}

// 隐藏所有窗口（内部方法）
+ (void)hideAllWindows {
    allWindowsVisible = NO;

    // 更新主控制器按钮图标和颜色
    if (masterControlWindow) {
        // 隐藏状态：使用加号表示可以显示
        [masterControlWindow.mainButton setTitle:@"⊕" forState:UIControlStateNormal];
        masterControlWindow.mainButton.backgroundColor = [UIColor colorWithRed:0.8 green:0.4 blue:0.4 alpha:0.7]; // 红色表示隐藏
        [masterControlWindow.mainButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];

        // 添加按钮点击动画效果
        [UIView animateWithDuration:0.2 animations:^{
            masterControlWindow.mainButton.transform = CGAffineTransformMakeScale(1.2, 1.2);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.2 animations:^{
                masterControlWindow.mainButton.transform = CGAffineTransformIdentity;
            }];
        }];
    }

    // 隐藏所有非主控制器窗口 - 优化版本
    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    NSInteger hiddenCount = 0;
    (void)hiddenCount;

    [windowArrayLock lock];
    for (FloatingView *window in array) {
        if (window != masterControlWindow) {
            [window hideWindow];
            hiddenCount++;
        }
    }
    [windowArrayLock unlock];

    NSLOG(@"[FloatingView] 隐藏了 %ld 个窗口", (long)hiddenCount);
}

// 获取主控制器窗口
+ (FloatingView *)masterControlWindow {
    return masterControlWindow;
}

// 检查是否所有窗口都可见
+ (BOOL)areAllWindowsVisible {
    return allWindowsVisible;
}

// 复位所有窗口到初始位置 - 优化版本
+ (void)resetAllWindowsToInitialPositions {
    NSUInteger windowCount = getWindowCount();
    NSLOG(@"[FloatingView] 开始复位所有窗口到初始位置，当前窗口数: %lu", (unsigned long)windowCount);

    if (windowCount == 0) {
        NSLOG(@"[FloatingView] 没有窗口需要复位");
        return;
    }

    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    NSInteger resetCount = 0;
    (void)resetCount;

    [windowArrayLock lock];
    for (FloatingView *window in array) {
        // 排除主控制器窗口
        if (window != masterControlWindow && !window.isMasterControl) {
            NSLOG(@"[FloatingView] 复位窗口: %@", window.windowId);
            [window resetToInitialPosition];
            resetCount++;
        } else {
            NSLOG(@"[FloatingView] 跳过主控制器窗口: %@", window.windowId);
        }
    }
    [windowArrayLock unlock];

    NSLOG(@"[FloatingView] 所有窗口复位完成，共复位 %ld 个窗口", (long)resetCount);
}

// 根据windowId删除指定窗口
+ (BOOL)removeWindowWithId:(NSString *)windowId {
    if (!windowId || [windowId length] == 0) {
        NSLOG(@"[FloatingView] 错误：窗口ID为空");
        return NO;
    }

    NSLOG(@"[FloatingView] 开始删除窗口: %@", windowId);

    // 查找要删除的窗口
    FloatingView *targetWindow = nil;
    NSInteger targetIndex = -1;
    (void)targetIndex;

    for (NSInteger i = 0; i < allFloatingWindows.count; i++) {
        FloatingView *window = allFloatingWindows[i];
        if ([window.windowId isEqualToString:windowId]) {
            targetWindow = window;
            targetIndex = i;
            break;
        }
    }

    if (!targetWindow) {
        NSLOG(@"[FloatingView] 警告：未找到窗口ID为 %@ 的窗口", windowId);
        return NO;
    }

    // 防止删除主控制器窗口
    if (targetWindow.isMasterControl || targetWindow == masterControlWindow) {
        NSLOG(@"[FloatingView] 错误：不能删除主控制器窗口");
        return NO;
    }

    // 从数组中移除窗口
    [allFloatingWindows removeObject:targetWindow];

    // 清理窗口保存的位置信息
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *positionKey = [NSString stringWithFormat:@"FloatingWindow_Position_%@", windowId];
    [defaults removeObjectForKey:positionKey];
    [defaults synchronize];

    // 隐藏并移除窗口
    [UIView animateWithDuration:0.3 animations:^{
        targetWindow.alpha = 0;
        targetWindow.transform = CGAffineTransformMakeScale(0.1, 0.1);
    } completion:^(BOOL finished) {
        [targetWindow removeFromSuperview];

        // 如果窗口有父窗口（FloatingWindow），也要隐藏父窗口
        if (targetWindow.superview && [targetWindow.superview.superview isKindOfClass:[UIWindow class]]) {
            UIWindow *parentWindow = (UIWindow *)targetWindow.superview.superview;
            parentWindow.hidden = YES;
        }
    }];

    NSLOG(@"[FloatingView] 窗口删除完成: %@", windowId);
    return YES;
}

// 收起所有展开的窗口 - 优化版本
- (void)collapseAllExpandedWindows {
    NSUInteger windowCount = getWindowCount();

    // 逻辑优化：如果只有当前窗口，直接收起自己
    if (windowCount <= 1) {
        if (self.isExpanded) {
            NSLOG(@"[FloatingView] 单窗口场景，直接收起自己: %@", self.windowId);
            [self forceCollapse];
        }
        return;
    }

    // 多窗口场景：收起所有其他窗口
    NSLOG(@"[FloatingView] 多窗口场景，开始收起所有展开的窗口");

    NSMutableArray<FloatingView *> *array = safeGetWindowArray();
    NSInteger collapsedCount = 0;
    (void)collapsedCount;

    [windowArrayLock lock];
    for (FloatingView *window in array) {
        // 排除主控制器窗口
        if (window != masterControlWindow && !window.isMasterControl) {
            if (window.isExpanded) {
                NSLOG(@"[FloatingView] 收起展开的窗口: %@", window.windowId);
                [window forceCollapse];
                collapsedCount++;
            }
        }
    }
    [windowArrayLock unlock];

    NSLOG(@"[FloatingView] 所有展开窗口收起完成，共收起 %ld 个窗口", (long)collapsedCount);
}

- (instancetype)init {
    return [self initWithConfig:nil];
}

- (instancetype)initWithConfig:(NSDictionary *)config {
    NSLOG(@"[FloatingView] initWithConfig 开始，config: %@", config);

    // 🎯 生成窗口唯一ID
    NSString *windowId = config[@"windowId"];
    if (!windowId || [windowId length] == 0) {
        // 如果配置中没有指定ID，根据按钮文本生成
        NSString *buttonText = config[@"buttonText"];
        if (buttonText && [buttonText length] > 0) {
            windowId = [NSString stringWithFormat:@"window_%@", buttonText];
        } else {
            // 最后的备用方案：使用时间戳
            windowId = [NSString stringWithFormat:@"window_%ld", (long)[[NSDate date] timeIntervalSince1970]];
        }
        NSLOG(@"[FloatingView] 自动生成窗口ID: %@， %@", windowId, buttonText);
    }

    // 获取屏幕尺寸
    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    NSLOG(@"[FloatingView] 屏幕尺寸: %@", NSStringFromCGSize(screenSize));

    // 🎯 首先计算并保存真正的初始位置（来自配置）
    CGFloat initialPosY = [config[@"startY"] floatValue] ?: screenSize.height / 2;
    CGFloat initialPosX = kEdgeMargin;
    if (![config[@"position"] isEqualToString:@"left"]) {
        initialPosX = screenSize.width - kMainButtonSize - kEdgeMargin;
    }
    CGPoint configInitialPosition = CGPointMake(initialPosX, initialPosY);

    // 保存真正的初始位置（用于复位）
    self.initialPosition = configInitialPosition;
    NSLOG(@"[FloatingView] 保存初始位置（用于复位）: %@", NSStringFromCGPoint(self.initialPosition));

    // 然后决定当前使用的位置（优先使用保存的位置）
    CGPoint position;
    BOOL useSavedPosition = NO;

    // 直接检查UserDefaults，避免创建临时对象
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *positionKey = [NSString stringWithFormat:@"FloatingWindow_Position_%@", windowId];
    NSDictionary *positionData = [defaults objectForKey:positionKey];

    if (positionData && [positionData isKindOfClass:[NSDictionary class]]) {
        CGFloat x = [positionData[@"x"] floatValue];
        CGFloat y = [positionData[@"y"] floatValue];
        position = CGPointMake(x, y);
        useSavedPosition = YES;
        NSLOG(@"[FloatingView] 使用保存的位置: %@", NSStringFromCGPoint(position));
    } else {
        // 使用配置中的初始位置
        position = configInitialPosition;
        NSLOG(@"[FloatingView] 使用配置位置: %@", NSStringFromCGPoint(position));
    }

    // 直接使用按钮大小作为窗口大小，不添加额外的边距
    CGRect frame = CGRectMake(position.x, position.y, kMainButtonSize, kMainButtonSize);
    NSLOG(@"[FloatingView] 设置frame: %@", NSStringFromCGRect(frame));

    self = [super initWithFrame:frame];
    
    if (self) {
        if (kDebugMode) {
            self.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.5];
            self.layer.cornerRadius = kFunctionButtonCornerRadius;
        }
        
        self.isExpanded = NO;
        self.functionButtons = [NSMutableArray array];
        self.isMasterControl = NO; // 默认不是主控制器
        self.isHidden = NO; // 默认显示状态

        // 🎯 设置窗口ID
        self.windowId = windowId;

        // 初始化吸附边缘状态
        if (useSavedPosition && positionData) {
            // 如果使用保存的位置，恢复边缘状态
            FloatingWindowEdge savedEdge = [positionData[@"edge"] integerValue];
            self.currentEdge = savedEdge;
            NSLOG(@"[FloatingView] 恢复边缘状态: %ld", (long)savedEdge);
        } else {
            // 根据初始位置确定边缘状态
            self.currentEdge = (position.x > screenSize.width / 2) ? FloatingWindowEdgeRight : FloatingWindowEdgeLeft;
            NSLOG(@"[FloatingView] 计算边缘状态: %ld", (long)self.currentEdge);
        }

        // 主按钮位置居中
        self.mainButton = [UIButton buttonWithType:UIButtonTypeSystem];
        self.mainButton.frame = CGRectMake(0, 0, kMainButtonSize, kMainButtonSize);
        self.mainButton.alpha = kMainButtonAlphaCollapsed;
        self.mainButton.tintColor = [UIColor whiteColor];
        self.mainButton.titleLabel.font = [UIFont systemFontOfSize:kFunctionButtonFontSize];
        self.mainButton.layer.cornerRadius = kMainButtonSize / 2.0;
        self.mainButton.layer.shadowColor = [UIColor blackColor].CGColor;
        self.mainButton.layer.shadowOpacity = 0.25;
        self.mainButton.layer.shadowOffset = CGSizeMake(0, 2);
        self.mainButton.layer.shadowRadius = 4;

        // 添加白色描边
        self.mainButton.layer.borderWidth = kMainButtonBorderWidth;
        self.mainButton.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:kMainButtonBorderAlpha].CGColor;

        // 设置按钮配置
        [self setupButtonConfigsWithConfig:config];

        // 使用配置中的buttonText属性设置主按钮文本
        NSString *buttonText = self.buttonConfigs[@"buttonText"] ?: @"N";
        [self.mainButton setTitle:buttonText forState:UIControlStateNormal];

        // 为主控制器设置特殊字体
        if ([buttonText isEqualToString:@"⊕"] || [buttonText isEqualToString:@"⊖"]) {
            // 主控制器使用更大的字体
            self.mainButton.titleLabel.font = [UIFont systemFontOfSize:18.0 weight:UIFontWeightBold];
        } else if (self.buttonConfigs[@"buttonText"]) {
            // 普通窗口使用粗体
            self.mainButton.titleLabel.font = [UIFont systemFontOfSize:kFunctionButtonFontSize weight:UIFontWeightHeavy];
        } else {
            [self.mainButton setTitle:@"N" forState:UIControlStateNormal];
        }

        // 使用配置中的color属性设置主按钮背景色，如果没有设置，默认蓝色
        UIColor *btnColor = self.buttonConfigs[@"color"];
        if (btnColor) {
            self.mainButton.backgroundColor = btnColor;
        } else {
            self.mainButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.6 blue:1.0 alpha:0.9];
        }

        [self.mainButton addTarget:self action:@selector(toggleExpand) forControlEvents:UIControlEventTouchUpInside];
        [self.mainButton addGestureRecognizer:[[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePan:)]];
        [self addSubview:self.mainButton];
    }

    if (self) {
        // 注册当前窗口
        [FloatingView registerWindow:self];
        NSLOG(@"[FloatingView] ✅ 窗口初始化完成 - ID: %@, Frame: %@", self.windowId, NSStringFromCGRect(self.frame));
    } else {
        NSLOG(@"[FloatingView] ❌ 窗口初始化失败");
    }

    return self;
}

- (void)setupButtonConfigsWithConfig:(NSDictionary *)config {
    self.buttonConfigs = config;
    // 初始化当前按钮配置数组
    self.currentButtonConfigs = [NSMutableArray arrayWithArray:self.buttonConfigs[@"buttonConfigs"]];
    
    // 设置actionBlock，如果没有传入则使用空实现
    id actionBlock = self.buttonConfigs[@"actionBlock"];
    if (actionBlock) {
        self.buttonActionBlock = actionBlock;
    } else {
        // 如果没有传入actionBlock，使用空实现
        self.buttonActionBlock = ^(NSString *buttonId) {
            NSLOG(@"按钮被点击: %@，但没有配置处理逻辑", buttonId);
        };
    }
}

- (void)forceCollapse {
    if (self.isExpanded) {
        self.isExpanded = NO;
        
        [self collapseButtons];

        CGRect frame = self.frame;
        // 根据吸附边缘调整收起后的窗口位置
        if (self.currentEdge == FloatingWindowEdgeRight) {
            // 确保保持与屏幕右侧的固定边距
            CGSize screenSize = [UIScreen mainScreen].bounds.size;
            frame.origin.x = screenSize.width - kMainButtonSize - kEdgeMargin;
        } else {
            // 左侧吸附时保持固定边距
            frame.origin.x = kEdgeMargin;
        }
        frame.size.width = kMainButtonSize;
        frame.size.height = kMainButtonSize;

        [UIView animateWithDuration:0.3 animations:^{
            self.frame = frame;
            self.mainButton.alpha = kMainButtonAlphaCollapsed;
            self.mainButton.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:kMainButtonBorderAlphaCollapsed].CGColor;
            if (kDebugMode) {
                self.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.5];
            }
        }];
    }
}

// 显示窗口
- (void)showWindow {
    if (self.isHidden) {
        self.isHidden = NO;
        self.alpha = 0.0;
        self.hidden = NO;

        [UIView animateWithDuration:0.3 animations:^{
            self.alpha = 1.0;
        }];
    }
}

// 隐藏窗口
- (void)hideWindow {
    if (!self.isHidden) {
        self.isHidden = YES;

        [UIView animateWithDuration:0.3 animations:^{
            self.alpha = 0.0;
        } completion:^(BOOL finished) {
            self.hidden = YES;
        }];
    }
}

- (void)toggleExpand {
    // 主控制器不展开，直接执行动作
    if (self.isMasterControl) {
        if (self.buttonActionBlock) {
            self.buttonActionBlock(@"master_control");
        }
        return;
    }

    // 性能优化：如果要展开，先收起其他所有窗口（仅在多窗口时）
    if (!self.isExpanded) {
        NSUInteger windowCount = getWindowCount();
        if (windowCount > 1) {
            NSLOG(@"[FloatingView] 多窗口环境，收起其他窗口后展开: %@", self.windowId);
            [FloatingView collapseAllWindowsExcept:self];
        } else {
            NSLOG(@"[FloatingView] 单窗口环境，直接展开: %@", self.windowId);
        }
    }

    self.isExpanded = !self.isExpanded;
    // 使用配置中的buttonText属性
    [self.mainButton setTitle:self.buttonConfigs[@"buttonText"] ?: @"测试" forState:UIControlStateNormal];
    if (self.isExpanded) {
        // 获取按钮配置数组
        NSArray *buttonConfigs = self.currentButtonConfigs;

        // 根据按钮数量动态计算展开高度
        // 优化宽度计算：主按钮 + 功能按钮 + 两者之间的间距
        CGFloat expandedWidth = kMainButtonSize + kFunctionButtonWidth + kButtonSpacing;
        CGFloat expandedHeight = kFunctionButtonHeight * buttonConfigs.count + kButtonVerticalSpacing * (buttonConfigs.count - 1);

        CGRect frame = self.frame;
        // 根据吸附边缘调整展开后的窗口位置
        if (self.currentEdge == FloatingWindowEdgeRight) {
            // 确保保持与屏幕右侧的固定边距
            CGSize screenSize = [UIScreen mainScreen].bounds.size;
            frame.origin.x = screenSize.width - expandedWidth - kEdgeMargin;
        }
        frame.size.width = expandedWidth;
        frame.size.height = expandedHeight;

        [UIView animateWithDuration:0.3 animations:^{
            self.frame = frame;
            self.mainButton.alpha = kMainButtonAlphaExpanded;
            self.mainButton.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:kMainButtonBorderAlphaExpanded].CGColor;
            if (kDebugMode) {
                self.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.5];
                self.layer.cornerRadius = kFunctionButtonCornerRadius;
            }
        }];

        [self expandButtons];
    } else {
        [self collapseButtons];

        CGRect frame = self.frame;
        // 根据吸附边缘调整收起后的窗口位置
        if (self.currentEdge == FloatingWindowEdgeRight) {
            // 确保保持与屏幕右侧的固定边距
            CGSize screenSize = [UIScreen mainScreen].bounds.size;
            frame.origin.x = screenSize.width - kMainButtonSize - kEdgeMargin;
        } else {
            // 左侧吸附时保持固定边距
            frame.origin.x = kEdgeMargin;
        }
        frame.size.width = kMainButtonSize;
        frame.size.height = kMainButtonSize;

        [UIView animateWithDuration:0.3 animations:^{
            self.frame = frame;
            self.mainButton.alpha = kMainButtonAlphaCollapsed;
            self.mainButton.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:kMainButtonBorderAlphaCollapsed].CGColor;
            if (kDebugMode) {
                self.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.5];
            }
        }];

    }
}

// 展开按钮
- (void)expandButtons {
    // 获取按钮配置数组
    NSArray *buttonConfigs = self.currentButtonConfigs;

    CGFloat originY = 0; // 从顶部开始
    CGFloat originX;


    
    // 根据吸附边缘决定按钮展开的方向
    if (self.currentEdge == FloatingWindowEdgeLeft) {
        // 左侧吸附时，主按钮在左，功能按钮在右
        originX = kMainButtonSize + kButtonSpacing;
        // 调整主按钮位置到左侧
        [UIView animateWithDuration:0.3 animations:^{
            CGRect mainBtnFrame = self.mainButton.frame;
            mainBtnFrame.origin.x = 0;
            mainBtnFrame.origin.y = 0;
            self.mainButton.frame = mainBtnFrame;
        }];
    } else {
        // 右侧吸附时，主按钮在右，功能按钮在左
        originX = 0;
        // 调整主按钮位置到右侧
        [UIView animateWithDuration:0.3 animations:^{
            CGRect mainBtnFrame = self.mainButton.frame;
            mainBtnFrame.origin.x = self.bounds.size.width - kMainButtonSize;
            mainBtnFrame.origin.y = 0;
            self.mainButton.frame = mainBtnFrame;
        }];
    }

    for (NSInteger i = 0; i < buttonConfigs.count; i++) {
        NSDictionary *buttonConfig = buttonConfigs[i];
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeSystem];
        btn.frame = CGRectMake(originX, originY, kFunctionButtonWidth, kFunctionButtonHeight);
        btn.backgroundColor = [UIColor blackColor];
        btn.alpha = 0;
        btn.tintColor = [UIColor whiteColor];
        btn.titleLabel.font = [UIFont systemFontOfSize:kFunctionButtonFontSize];
        btn.layer.cornerRadius = kFunctionButtonCornerRadius;
        btn.layer.shadowColor = [UIColor blackColor].CGColor;
        btn.layer.shadowOpacity = 0.15;
        btn.layer.shadowOffset = CGSizeMake(0, 2);
        btn.layer.shadowRadius = 3;
        [btn setTitle:buttonConfig[kButtonTitleKey] forState:UIControlStateNormal];
        // 将按钮ID存储在按钮的tag属性中，使用哈希值
        NSString *buttonId = buttonConfig[kButtonIdKey];
        btn.tag = [buttonId hash];
        [btn addTarget:self action:@selector(functionButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self addSubview:btn];
        [self.functionButtons addObject:btn];

        // 设置按钮的初始位置（动画起点）
        CGFloat initialX;
        if (self.currentEdge == FloatingWindowEdgeLeft) {
            // 左侧吸附时，按钮从左侧开始动画
            initialX = originX - kFunctionButtonWidth; // 从左侧屏幕外开始
        } else {
            // 右侧吸附时，按钮从右侧开始动画
            initialX = originX + kFunctionButtonWidth; // 从右侧屏幕外开始
        }
        btn.frame = CGRectMake(initialX, originY + (kFunctionButtonHeight + kButtonVerticalSpacing) * i, kFunctionButtonWidth, kFunctionButtonHeight);
        
        CGRect targetFrame = CGRectMake(originX, originY + (kFunctionButtonHeight + kButtonVerticalSpacing) * i, kFunctionButtonWidth, kFunctionButtonHeight);

        [UIView animateWithDuration:0.3 delay:0.05 * i options:UIViewAnimationOptionCurveEaseOut animations:^{
            btn.frame = targetFrame;
            btn.alpha = kFunctionButtonAlpha;
        } completion:nil];
    }
}

// 收起按钮
- (void)collapseButtons {
    CGFloat originX;
    
    // 根据吸附边缘决定按钮收起的方向
    if (self.currentEdge == FloatingWindowEdgeLeft) {
        originX = kMainButtonSize + kButtonSpacing;
    } else {
        originX = 0;
    }
    
    // 收起时将主按钮移回中心位置
    [UIView animateWithDuration:0.3 animations:^{
        CGRect mainBtnFrame = self.mainButton.frame;
        mainBtnFrame.origin.x = 0;
        mainBtnFrame.origin.y = 0;
        self.mainButton.frame = mainBtnFrame;
    }];
    
    for (UIButton *btn in self.functionButtons) {
        // 设置按钮的目标位置（动画终点）
        CGFloat targetX;
        if (self.currentEdge == FloatingWindowEdgeLeft) {
            // 左侧吸附时，按钮向左侧收起
            targetX = originX - kFunctionButtonWidth; // 向左侧屏幕外收起
        } else {
            // 右侧吸附时，按钮向右侧收起
            targetX = originX + kFunctionButtonWidth; // 向右侧屏幕外收起
        }
        
        CGRect targetFrame = CGRectMake(targetX, btn.frame.origin.y, kFunctionButtonWidth, kFunctionButtonHeight);
        
        [UIView animateWithDuration:0.2 animations:^{
            btn.frame = targetFrame;
            btn.alpha = 0;
        } completion:^(BOOL finished) {
            [btn removeFromSuperview];
        }];
    }
    [self.functionButtons removeAllObjects];
}

// 按钮点击事件
- (void)functionButtonTapped:(UIButton *)sender {
    // 获取按钮配置数组
    NSArray *buttonConfigs = self.currentButtonConfigs;
    
    // 遍历按钮配置，找到对应的按钮ID
    for (NSDictionary *buttonConfig in buttonConfigs) {
        NSString *buttonId = buttonConfig[kButtonIdKey];
        if (sender.tag == [buttonId hash]) {
            // 调用按钮点击回调
            if (self.buttonActionBlock) {
                self.buttonActionBlock(buttonId);
            }
            
            // 检查是否需要自动收起
            NSNumber *autoCollapse = buttonConfig[kButtonAutoCollapseKey];
            if (autoCollapse && [autoCollapse boolValue]) {
                if (self.isExpanded) {
                    [self toggleExpand];
                }
            }
            break;
        }
    }
}

- (void)handlePan:(UIPanGestureRecognizer *)gesture {
    CGPoint translation = [gesture translationInView:self.superview];
    
    switch (gesture.state) {
        case UIGestureRecognizerStateBegan: {
            self.isDragging = YES;
            self.initialTouchPoint = [gesture locationInView:self.superview];

            NSUInteger windowCount = getWindowCount();
            NSLOG(@"[FloatingView] 开始拖动窗口: %@, 当前窗口总数: %lu", self.windowId, (unsigned long)windowCount);

            // 逻辑优化：根据窗口数量决定收起策略
            if (windowCount > 1) {
                // 多窗口：收起所有展开的窗口
                NSLOG(@"[FloatingView] 多窗口环境，收起所有展开窗口");
                [self collapseAllExpandedWindows];
            } else {
                // 单窗口：只收起自己（如果展开的话）
                if (self.isExpanded) {
                    NSLOG(@"[FloatingView] 单窗口环境，收起自己");
                    [self forceCollapse];
                }
            }

            // 拖拽开始时的视觉反馈
            [UIView animateWithDuration:0.2 animations:^{
                self.mainButton.alpha = kMainButtonAlphaExpanded;
                self.mainButton.transform = CGAffineTransformMakeScale(1.1, 1.1);
            }];
            break;
        }
            
        case UIGestureRecognizerStateChanged: {
            // 移动视图
            CGPoint newCenter = CGPointMake(self.center.x + translation.x, self.center.y + translation.y);
            self.center = newCenter;
            [gesture setTranslation:CGPointZero inView:self.superview];
            break;
        }
            
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            self.isDragging = NO;
            // 拖拽结束时恢复正常状态
            [UIView animateWithDuration:0.2 animations:^{
                self.mainButton.alpha = kMainButtonAlphaCollapsed;
                self.mainButton.transform = CGAffineTransformIdentity;
            }];
            // 边缘吸附
            [self snapToEdgeWithAnimation];
            break;
        }
            
        default:
            break;
    }
}

// 增强的边缘吸附动画
- (void)snapToEdgeWithAnimation {
    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    CGFloat midX = screenSize.width / 2;
    
    CGRect frame = self.frame;
    CGFloat finalX;
    
    // 根据释放位置决定吸附到哪一边，并保持固定边距
    if (CGRectGetMidX(frame) < midX) {
        finalX = kEdgeMargin;
        self.currentEdge = FloatingWindowEdgeLeft;
    } else {
        finalX = screenSize.width - frame.size.width - kEdgeMargin;
        self.currentEdge = FloatingWindowEdgeRight;
    }
    
    // 确保Y坐标在屏幕范围内
    CGFloat finalY = MAX(kEdgeMargin, MIN(screenSize.height - frame.size.height - kEdgeMargin, frame.origin.y));
    
    // 弹性吸附动画
    [UIView animateWithDuration:0.5
                          delay:0
         usingSpringWithDamping:0.6
          initialSpringVelocity:0.8
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.frame = CGRectMake(finalX, finalY, frame.size.width, frame.size.height);
    } completion:^(BOOL finished) {
        // 🎯 拖拽结束后保存新位置
        if (finished) {
            [self saveWindowPosition];
        }
    }];
}

// 添加按钮
- (void)addButton:(NSDictionary *)buttonConfig {
    NSLOG(@"[FloatingView] 🔘 开始添加按钮: %@", buttonConfig[kButtonTitleKey]);

    // 检查是否已存在相同ID的按钮
    NSString *newButtonId = buttonConfig[kButtonIdKey];
    for (NSDictionary *config in self.currentButtonConfigs) {
        if ([config[kButtonIdKey] isEqualToString:newButtonId]) {
            NSLOG(@"[FloatingView] ⚠️ 按钮ID已存在，跳过添加: %@", newButtonId);
            return; // 已存在相同ID的按钮，直接返回
        }
    }

    // 添加新按钮配置
    [self.currentButtonConfigs addObject:buttonConfig];
    NSLOG(@"[FloatingView] ✅ 按钮配置已添加，当前按钮数量: %lu", (unsigned long)self.currentButtonConfigs.count);
    NSLOG(@"[FloatingView] 📊 当前展开状态: %@", self.isExpanded ? @"展开" : @"收起");

    // 无论当前状态如何，都需要更新按钮布局
    if (self.isExpanded) {
        // 如果当前是展开状态，重新布局
        NSLOG(@"[FloatingView] 🔄 窗口已展开，重新布局按钮");
        [self collapseButtons];
        [self toggleExpand];
    } else {
        // 如果当前是收起状态，展开一次以显示新按钮，然后可以选择是否保持展开
        NSLOG(@"[FloatingView] 📤 窗口已收起，展开以显示新按钮");
        [self toggleExpand];

        // 可选：如果希望添加按钮后自动收起，可以延时收起
        // dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        //     if (self.isExpanded) {
        //         [self toggleExpand];
        //     }
        // });
    }

    NSLOG(@"[FloatingView] ✅ 按钮添加完成: %@", buttonConfig[kButtonTitleKey]);
}

// 移除按钮 - 安全版本
- (void)removeButtonWithId:(NSString *)buttonId {
    NSLOG(@"[FloatingView] 🗑️ 开始安全删除按钮: %@", buttonId);

    if (!buttonId || buttonId.length == 0) {
        NSLOG(@"[FloatingView] ❌ 按钮ID为空，取消删除");
        return;
    }

    NSInteger indexToRemove = NSNotFound;

    // 查找要删除的按钮索引
    for (NSInteger i = 0; i < self.currentButtonConfigs.count; i++) {
        NSDictionary *config = self.currentButtonConfigs[i];
        if ([config[kButtonIdKey] isEqualToString:buttonId]) {
            indexToRemove = i;
            break;
        }
    }

    // 如果找到了要删除的按钮
    if (indexToRemove != NSNotFound) {
        NSLOG(@"[FloatingView] 🗑️ 找到要删除的按钮，索引: %ld", (long)indexToRemove);

        // 安全删除：延迟执行，避免在按钮回调中立即修改UI
        dispatch_async(dispatch_get_main_queue(), ^{
            @try {
                // 再次检查索引是否有效（防止并发修改）
                if (indexToRemove < self.currentButtonConfigs.count) {
                    [self.currentButtonConfigs removeObjectAtIndex:indexToRemove];
                    NSLOG(@"[FloatingView] ✅ 按钮配置已删除");

                    // 延迟重新布局，确保当前的点击事件完全结束
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        @try {
                            // 如果当前是展开状态，需要重新布局
                            if (self.isExpanded) {
                                NSLOG(@"[FloatingView] 🔄 重新布局按钮");
                                [self collapseButtons];

                                // 再次延迟展开，确保收起动画完成
                                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                    [self toggleExpand];
                                    NSLOG(@"[FloatingView] ✅ 按钮删除和重新布局完成");
                                });
                            } else {
                                NSLOG(@"[FloatingView] ✅ 按钮删除完成（窗口未展开）");
                            }
                        } @catch (NSException *layoutException) {
                            NSLOG(@"[FloatingView] ❌ 重新布局时异常: %@", layoutException.reason);
                        }
                    });
                } else {
                    NSLOG(@"[FloatingView] ❌ 索引已失效，取消删除");
                }
            } @catch (NSException *exception) {
                NSLOG(@"[FloatingView] ❌ 删除按钮时异常: %@", exception.reason);
            }
        });
    } else {
        NSLOG(@"[FloatingView] ⚠️ 未找到要删除的按钮: %@", buttonId);
    }
}

// 批量更新按钮配置
- (void)updateButtonsWithConfigs:(NSArray<NSDictionary *> *)buttonConfigs {
    // 创建一个可变数组来存储新的按钮配置
    NSMutableArray *updatedConfigs = [NSMutableArray array];
    
    // 创建一个字典来存储新配置中的按钮ID，用于快速查找
    NSMutableDictionary *newConfigIds = [NSMutableDictionary dictionary];
    for (NSDictionary *config in buttonConfigs) {
        NSString *buttonId = config[kButtonIdKey];
        if (buttonId) {
            newConfigIds[buttonId] = config;
        }
    }
    
    // 遍历当前按钮配置，保留新配置中存在的按钮
    for (NSDictionary *currentConfig in self.currentButtonConfigs) {
        NSString *currentId = currentConfig[kButtonIdKey];
        if (currentId && newConfigIds[currentId]) {
            // 如果按钮在新配置中存在，使用新配置
            [updatedConfigs addObject:newConfigIds[currentId]];
            [newConfigIds removeObjectForKey:currentId];
        }
    }
    
    // 添加剩余的新按钮配置
    [updatedConfigs addObjectsFromArray:newConfigIds.allValues];
    
    // 更新当前按钮配置
    self.currentButtonConfigs = updatedConfigs;
    
    // 如果当前是展开状态，需要重新布局
    if (self.isExpanded) {
        [self collapseButtons];
        [self toggleExpand];
    }


}

@end

// 批量更新按钮配置
// [floatingManager updateButtonsWithConfigs:@[
//     @{kButtonTitleKey: @"按钮1", kButtonIdKey: @"btn1", kButtonAutoCollapseKey: @YES},
//     @{kButtonTitleKey: @"按钮2", kButtonIdKey: @"btn2", kButtonAutoCollapseKey: @YES},
//     @{kButtonTitleKey: @"按钮3", kButtonIdKey: @"btn3", kButtonAutoCollapseKey: @NO}
// ]];
