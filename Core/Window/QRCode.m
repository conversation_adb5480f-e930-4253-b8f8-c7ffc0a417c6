#import "QRCode.h"
#import <CoreImage/CoreImage.h>
#import <objc/runtime.h>

@implementation QRCode

+ (void)show:(NSString *)text title:(NSString *)title message:(NSString *)message {
    if (!text || text.length == 0) return;

    UIWindow *qrWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    qrWindow.windowLevel = UIWindowLevelAlert + 300;
    qrWindow.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
    qrWindow.rootViewController = [UIViewController new];
    [qrWindow makeKeyAndVisible];

    // 增加上下间隔
    CGFloat verticalPadding = 20.0;
    UIView *container = [[UIView alloc] initWithFrame:CGRectMake(0, verticalPadding, 280, 380)];
    container.center = CGPointMake(qrWindow.center.x, qrWindow.center.y + verticalPadding);
    container.backgroundColor = [UIColor whiteColor];
    container.layer.cornerRadius = 15;
    container.clipsToBounds = YES;
    [qrWindow addSubview:container];

    // 固定标题为“扫码付款”
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 15, container.frame.size.width, 25)];
    titleLabel.text = title ? title : @"扫码付款";
    titleLabel.font = [UIFont boldSystemFontOfSize:15];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.textColor = [UIColor darkGrayColor];
    [container addSubview:titleLabel];

    // QRCode Image
    UIImageView *qrView = [[UIImageView alloc] initWithFrame:CGRectMake(40, 50, 200, 200)];
    qrView.contentMode = UIViewContentModeScaleAspectFit;
    qrView.image = [self generateQRCodeImageFrom:text size:qrView.bounds.size];
    [container addSubview:qrView];

    // 提示信息
    UILabel *messageLabel = [[UILabel alloc] initWithFrame:CGRectMake(10, CGRectGetMaxY(qrView.frame) + 15, container.frame.size.width - 20, 40)];
    messageLabel.text = message;
    messageLabel.font = [UIFont systemFontOfSize:14];
    messageLabel.textAlignment = NSTextAlignmentCenter;
    messageLabel.textColor = [UIColor grayColor];
    messageLabel.numberOfLines = 0;
    [container addSubview:messageLabel];

    // Close button
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    closeBtn.frame = CGRectMake(0, CGRectGetMaxY(messageLabel.frame) + 15, container.frame.size.width, 40);
    [closeBtn setTitle:@"关闭" forState:UIControlStateNormal];
    [closeBtn setTitleColor:[UIColor blueColor] forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(closeTopWindow) forControlEvents:UIControlEventTouchUpInside];
    [container addSubview:closeBtn];

    // 保存 window 弱引用防止提前释放
    objc_setAssociatedObject([self class], @"_qrWindow", qrWindow, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

/// CoreImage 生成二维码
+ (UIImage *)generateQRCodeImageFrom:(NSString *)text size:(CGSize)size {
    NSData *data = [text dataUsingEncoding:NSUTF8StringEncoding];
    CIFilter *filter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    [filter setDefaults];
    [filter setValue:data forKey:@"inputMessage"];

    CIImage *ciImage = filter.outputImage;
    if (!ciImage) return nil;

    // 缩放二维码图像
    CGFloat scaleX = size.width / ciImage.extent.size.width;
    CGFloat scaleY = size.height / ciImage.extent.size.height;
    CIImage *transformed = [ciImage imageByApplyingTransform:CGAffineTransformMakeScale(scaleX, scaleY)];

    return [UIImage imageWithCIImage:transformed];
}

+ (void)closeTopWindow {
    UIWindow *qrWindow = objc_getAssociatedObject([self class], @"_qrWindow");
    if (qrWindow) {
        qrWindow.hidden = YES;
        objc_setAssociatedObject([self class], @"_qrWindow", nil, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}

@end